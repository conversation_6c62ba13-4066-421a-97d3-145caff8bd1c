{"__meta": {"id": "X49a842c5988970bfa6191b7950166e04", "datetime": "2025-07-14 22:28:21", "utime": **********.827042, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.355114, "end": **********.82706, "duration": 0.47194600105285645, "duration_str": "472ms", "measures": [{"label": "Booting", "start": **********.355114, "relative_start": 0, "end": **********.750201, "relative_end": **********.750201, "duration": 0.39508700370788574, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.750212, "relative_start": 0.3950979709625244, "end": **********.827062, "relative_end": 1.9073486328125e-06, "duration": 0.07684993743896484, "duration_str": "76.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46325160, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02297, "accumulated_duration_str": "22.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.784824, "duration": 0.021070000000000002, "duration_str": "21.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.728}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.816206, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.728, "width_percent": 3.657}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.819598, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 95.385, "width_percent": 4.615}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2010945489 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=1ue2vqu%7C1752523517717%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhiNHI0N2E0cFpkTjB6UEpPdmxnRnc9PSIsInZhbHVlIjoiQjJyWThqS21TVXVUdlIyVms3amFLOTBrSjdOZ2ZmSDRmRVF5TVlZbHNxZ0RWL2VSWWtnbnZhSU1WV2lyQzlHM2lDanF6bXI3c1hZSllyWHNTOWtxR1BtZVJ4V0ZOdFQyQWxrMWFzREFWazhYV3NUbWRZV2NzUzFISzVmOENiQnpXVGpHWm1wL2lJMG1oVmYxSTdKL2o4Zm5TWWQ2RGd1cVdkWTJBdE5QcFkvZVlhaEJyaVhKNHdsVXFLTjc5WTV2Sno0SnUyR2xpcHhBbWRnTDNKWnZILy9sQUlwWE4rY0w1WFYvUDdBWjEwK2xqUmxhZDlkTEFCTUducXFtaHpBTm1PRlU2VWFBOFVIRm9xYW5XRUJySEx6eDAvbzltbHkyaFNXNVoyRkFPYnRHNkt5QUxkc2JVZVBGU3dmdHh2VklrUERDdGwvU0hKWFQ1eXdUa2JML3ljYlJZNkoyUzA0WHhIR2pPcUpHa29jdFhOZlgxVWptYVlqS0cyRlhmcG9NSy9MUlNmRGNwNVBKMlhHV3dXWmhBKytxQVVpOWJadExhWHN6WHc4WVorTmN0bExKd05wTWhPalRyaHdEZys1SEhoZ2hsdEZuVkNVY3o2Q2tQQ1FrV05QZHJXS3ZiWi9FUEhOZk1Ca2cwTk1VNHlLWEY5RXdKdzQya1FVZG5ENUUiLCJtYWMiOiJlNDJmOWM2ZDdjMmI4ZTYzYTU3NTZhYjMyMTJmMGFkNDRiZGFlNzdkMzVkODg3OTRjMzgxZDBhOGY4NjQ4YWUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFUcEtEMlFuRGlja3plNTJCbjRyeGc9PSIsInZhbHVlIjoiT1BOTzBSWTRxRzlSei9MOXllcjJVUjVRdkVEUTZ4RS9aOXFJMUk1SDViVTZEcjlTbFZSbTJ2UnRrVG55L1hVZUo3d0ZHMmFKT0dhNUtEQVlFcFEzT3p6UmwyaG90cUR0SHdUWjBTZmxMbG9yaTdvUDNCYllsT2NQY3FnNlgzKzE2MGJHTmtjYWFPK3Zqbithc0tEV1VucklIQzRNdG1PdHdNL21XWVVhNEMxcXptcitVTGhZWDhyeC9OYU5rSWsrMWpMOGN2UFhEcUU0MUJWVWlyREViUUZibU4yQ0hVdmdIL0N4SHFyMUVWUTBndFNCcEx6bWVlY05RckQvL1pXRi9iZzlkdnc0STl3ekY2anpaQUZrMExWelk5STFsVXhXd2JrRzNxNTBWbXozV3JOT3llUDRWb2ZzYklIY29ld3Vvamt4d3lEY3BFakxELzFxbGUzakcyOXVhVzJNWE9NUUxmM2l5RW9lVHpjcjRYTzhtdjhuNWJMeDNHY2JLVkEwZjBXU0YyWUdRc2dycmFHOEQyODA2cFhHR0VlS2dLUjRhMTYwK1FSdVA4TkNBY1RxMk44bWFjUFdqK0ZNa1YzUldFd2N5T1M5eFE3YWlOa3dnWWlkMm9hT1Bzb245ZkpXcUtkdHV3dEtLOUpMM2ttcllZTm4rNWFIaUhHSHJWUUEiLCJtYWMiOiI1ZTdlOGZhMGIxYzZjZjlmMDkyOTdjNTkzMjEzYjNkNjhlZDU0ZjcyNGQzZTIyMjZhMmE3ZmI2YmNhMjVkNTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010945489\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2036076920 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2036076920\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:28:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldJME1haTc3c1ExMVFjZHZFR3dQRmc9PSIsInZhbHVlIjoiM2UvSnFNUjg5N2ZrOGtxT1hVVXhFSk4wSjdmRnRsUDB2aytFWitFTHlERkt4NlRrNGhRTUZQTlNiRk5WbmpqWEZEUWxKWUNRMm5xWXE5Z0xURURJQ0c0bUxOc25JY05zdWNmYTZ6ZFBPNFkvUTViWUIrVlF1U0ZMaTc3UGs2VFA2U200Wmd4d2dVRnEwYnRPaUtsQmZzajU4S25IOXRYTWlYUjNRTEtGMFlmaVdFSFBXQVpicmFSb2pBcThaK3pFOGlTdENReTZBM1hPQ1EvcGMreHVzRDJpZmcxNVE0dU9UN0hmTXBtQTVGV3dxZHdoWnBBMFFZb1FTbnpvUk5WM2ZRajFxT1dQcDhhYU5oQTlGVEFGMDc1ckRlMnZmTFlCeGpOZVBYY2JOSGpMcjlyQWNyMUZMY0RGVFpNTCt2VDRnSHpCQm81U0JPSVd5MFBjQXoyTWZCdCtidDZJMVlMbEJYRWgzanFiY3hNK0diMHNtSkViK2s4RGFxcnIwdEVoQnovVXNWTzFZV3FvTndxbDJlamxKS2YwYkkrQ2pMQ216bTBZQy9MeStPNG40VmdWN09kS3pLRTFLQ2dCKys4OE5DUWhYcXF4bUJpU2poa1FpNjFMQlZHTnBtbW8wdi81ZzdJa0VtY3FIV0RVaUoxU2djWDJ4WFFJbWlXaTJXVFMiLCJtYWMiOiJlYTcyZGZlMjA3NjRiMTU3NjM1YWE3YmY4NDI3Zjk1N2UyMzQ2YzQxMTU0MzQ4MTMwYTkxYzVkOGVlOTIxMTAyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZManhxNFRTVmdZVnZFT21nV1Q4WEE9PSIsInZhbHVlIjoiSGUybm1EMUFXQnpiUDZEZFRuYW9xaUlOMHJOMEhRTHBIRmk0TDd3dHVZQ2ZiNFFBeDIraHpWcVZhamhkN3ZwL2tBRGREcWpJZ2N5bTNKUnFrbnRNVVU4WGw0NElWYjk1OGVUNVI3VmFJdjlvWmxTbkJRZU96SlV2a0Z0alF6ZnBtdGxveTZzeXNrdmVIelkzSkYrT3RZSm85MWxLb05ueDJlR0l4RUZWL0l0WHNjeU92R3FNV2tJNTRmLzBVYkNLZkVyRDBKaWx0d0VDa2tZNmI2MXRNeEs1ZHFDM2g1NEVqbHVhdGRJV2V2d1pMTmtzODh4SCtDZndjS2JDK0h1dnV2TDNmMFhtS0l1NFQ1YUMzSkpXWS9scm82NllMOGxJUCtnaGtkS0JLWmdLOXNxbUpzNlBKSi85UEVKQVVRTGhYZkZSL2pyOEpPeVBHY3ZvdW12TXpnYjlVQlR0eE9tTHhRRlRWWFNXWVZsOTNkMWovNWhUOWwzRVJNMHRFNHJTUWJrUzBQYkw0WEI2T1owV3g2MjhiYlF6TStRY2xNUWtUSWVnZ1NvT2Q0UTcrdDhrTkR4UXo1cGRjN0UwS2RaRDJGUVUrUk9CVkE4ckozVlZwMnJqZGZ6OVFTNE1HQ084anM2citWL3RSSjRHQmxBYlZIUkRpdGYyd2Z6VlJWM2oiLCJtYWMiOiJkMjRkNjgyNGI4MjhjNjk5OWRmY2M1M2I3YTk5M2ZlZWViNDU0Mjg3MmUyYjM2NGM0YWU4MzVlNTQ4Y2Y3ZDExIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldJME1haTc3c1ExMVFjZHZFR3dQRmc9PSIsInZhbHVlIjoiM2UvSnFNUjg5N2ZrOGtxT1hVVXhFSk4wSjdmRnRsUDB2aytFWitFTHlERkt4NlRrNGhRTUZQTlNiRk5WbmpqWEZEUWxKWUNRMm5xWXE5Z0xURURJQ0c0bUxOc25JY05zdWNmYTZ6ZFBPNFkvUTViWUIrVlF1U0ZMaTc3UGs2VFA2U200Wmd4d2dVRnEwYnRPaUtsQmZzajU4S25IOXRYTWlYUjNRTEtGMFlmaVdFSFBXQVpicmFSb2pBcThaK3pFOGlTdENReTZBM1hPQ1EvcGMreHVzRDJpZmcxNVE0dU9UN0hmTXBtQTVGV3dxZHdoWnBBMFFZb1FTbnpvUk5WM2ZRajFxT1dQcDhhYU5oQTlGVEFGMDc1ckRlMnZmTFlCeGpOZVBYY2JOSGpMcjlyQWNyMUZMY0RGVFpNTCt2VDRnSHpCQm81U0JPSVd5MFBjQXoyTWZCdCtidDZJMVlMbEJYRWgzanFiY3hNK0diMHNtSkViK2s4RGFxcnIwdEVoQnovVXNWTzFZV3FvTndxbDJlamxKS2YwYkkrQ2pMQ216bTBZQy9MeStPNG40VmdWN09kS3pLRTFLQ2dCKys4OE5DUWhYcXF4bUJpU2poa1FpNjFMQlZHTnBtbW8wdi81ZzdJa0VtY3FIV0RVaUoxU2djWDJ4WFFJbWlXaTJXVFMiLCJtYWMiOiJlYTcyZGZlMjA3NjRiMTU3NjM1YWE3YmY4NDI3Zjk1N2UyMzQ2YzQxMTU0MzQ4MTMwYTkxYzVkOGVlOTIxMTAyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZManhxNFRTVmdZVnZFT21nV1Q4WEE9PSIsInZhbHVlIjoiSGUybm1EMUFXQnpiUDZEZFRuYW9xaUlOMHJOMEhRTHBIRmk0TDd3dHVZQ2ZiNFFBeDIraHpWcVZhamhkN3ZwL2tBRGREcWpJZ2N5bTNKUnFrbnRNVVU4WGw0NElWYjk1OGVUNVI3VmFJdjlvWmxTbkJRZU96SlV2a0Z0alF6ZnBtdGxveTZzeXNrdmVIelkzSkYrT3RZSm85MWxLb05ueDJlR0l4RUZWL0l0WHNjeU92R3FNV2tJNTRmLzBVYkNLZkVyRDBKaWx0d0VDa2tZNmI2MXRNeEs1ZHFDM2g1NEVqbHVhdGRJV2V2d1pMTmtzODh4SCtDZndjS2JDK0h1dnV2TDNmMFhtS0l1NFQ1YUMzSkpXWS9scm82NllMOGxJUCtnaGtkS0JLWmdLOXNxbUpzNlBKSi85UEVKQVVRTGhYZkZSL2pyOEpPeVBHY3ZvdW12TXpnYjlVQlR0eE9tTHhRRlRWWFNXWVZsOTNkMWovNWhUOWwzRVJNMHRFNHJTUWJrUzBQYkw0WEI2T1owV3g2MjhiYlF6TStRY2xNUWtUSWVnZ1NvT2Q0UTcrdDhrTkR4UXo1cGRjN0UwS2RaRDJGUVUrUk9CVkE4ckozVlZwMnJqZGZ6OVFTNE1HQ084anM2citWL3RSSjRHQmxBYlZIUkRpdGYyd2Z6VlJWM2oiLCJtYWMiOiJkMjRkNjgyNGI4MjhjNjk5OWRmY2M1M2I3YTk5M2ZlZWViNDU0Mjg3MmUyYjM2NGM0YWU4MzVlNTQ4Y2Y3ZDExIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-14******** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-14********\", {\"maxDepth\":0})</script>\n"}}