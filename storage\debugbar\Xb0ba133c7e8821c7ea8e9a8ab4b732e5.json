{"__meta": {"id": "Xb0ba133c7e8821c7ea8e9a8ab4b732e5", "datetime": "2025-07-14 22:30:59", "utime": **********.79082, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.304318, "end": **********.790835, "duration": 0.*****************, "duration_str": "487ms", "measures": [{"label": "Booting", "start": **********.304318, "relative_start": 0, "end": **********.715216, "relative_end": **********.715216, "duration": 0.*****************, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.715225, "relative_start": 0.****************, "end": **********.790836, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "75.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00364, "accumulated_duration_str": "3.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.753302, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.242}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.772415, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.242, "width_percent": 16.484}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.782675, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 74.725, "width_percent": 25.275}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C**********094%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJpOGYxQk5xZ1V1OG1JdHNZY20zZ2c9PSIsInZhbHVlIjoieDgvQTBUSEFwLzhDdUJXTnpLL09pMmU4QThTZ0x1a0VIU1lXYTM1d3dsZWxScDdXTlNmZVRhZUhROVR6YjVhZmJFMnNyWUIycnEzU0hWbktmVzBvdXY3NG5KOUJtZXp1N3NKeEtOS3c1TWhCdUUxamIya1BHUjQ5eHF1K084YzZqZGJaUFRWbWdOMnduOWpxSGNRaHR0RVNsU0FUUkJPZjdvUlNVY1VhVUY0djQ1V0ZEWVBibFVYVkZJRGhNaHNZQTBkVEJQaTNLdTArSStiMENkNmpxaE5naDFQZmJETmFxMCtyNlF4QlJYdnY4anc5amNGdUU3TjZ2dEdESjNCL3paK3VPbVEvZ3pzL2ZmVWVCQ0dNUi9PUVJOdEVBQmdua2FNS1NwMU9yUlA2RkxqbVAzZE1mdlZYWWVRMlpJMER0UGYxUTlZc2hlTVBPK0ZNWE1LMjZEaXhzeEZGSlQwaVU1TXVBcXZMdHFGTHhKdjI5Yzc4Rk05UnhEcnIwQXNKdmU5RnNXWjlmclRudGp0Qjk5Zk02SWNwRG0wR2VXamY5MXFsWkNzWkNsdXNObXBkalc1TDJBTHNQU2FqaUkwRSs5bG1CbjBGSmdQK1pPZFVZOUcyT1NlOVpIYjAxa3l6ODVzaXBOME9JeWQ5dHlxV1dJQkNMUG91N2c1b2d1THAiLCJtYWMiOiIwYjJmNWY0Yzc1MjAzZGZlZmEyNDM5N2U1Mjc1NWYwMDgzYWU1ODJlZWZmMjI1OWVjMWViY2Q5ZGExMGQ4MTlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBwRW5HQndVSm01d3ZDTDg0MitlelE9PSIsInZhbHVlIjoid1RMVWFSM21hd3VVdFBkeHQ4RFJKVEJMbVZGNW5yOCtaZzQ5RDJ6cFlkcytDbWVjaVNhd2k3cjB5TUlQd1BZSktNVjA5WXRMQlJ4MXdzL1BFdGZDOU9rbmk3TFd4MnljUjRqQ3NkamMvY2ZYVnVzTUlmMGxCNzBUOFRUY1M5WFdHWm9XaVEySnN1YzFtYThYa0REMmtHS1Q2ZWk3MjJrOTNqNFhxMS8yaDBRV0prL1BFRlgyUTdCSVE5SGY0TTN6YmZSRmxMVzRoK1FzK25iTC9yTHBEK1FQNVhNdnRma0Q0MzBUR2I1OGExYVc4c2ZxYVVMbk9Lc0ZMRndLU2VWenYxanRBVlZPaWNSajR4STNudi9UK0FGcjRhTGFBUDFsQkpMQVNmVGptbUJpYWdKbVFoVzVuVm53cGlOc1lHQ2VSYUZ4NFNrN0hlbnkrVUR1UlUwYXNOenVadFljaDhaSTlmaUxsWmNTdjkySy93dXRlTUc0MTc2Z2p4OGlhV1RMbkxZZkxscU92VkFxR2FQdE0zOGN5UVhkUDErT2huUVFSVW5aVVRlRkJEMkM5dXNsUFpFR0wyNE9yQ3l3NVdub1pDUEdBQVU1bGNzQ3JNT1ZSUzhJaEc2Qm5uOEpFNFFzZ1ZQK2tTbGpGeDEwcHBGSHVETVhuODgwbjhkcXRlckUiLCJtYWMiOiJiNTQzZjE5ODBkNGRjNDIzOWUzYmFkYWMwYzBmZTNlNzZiM2MyMzEzYTYwZjYzMjYxYTNiZTczMzU0YjMzMjM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-112183585 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-112183585\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1171671321 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtSK2NqaVhLQmhsSFd2Ukl0ckVYc0E9PSIsInZhbHVlIjoiV2VuN2VBZXpjblFHWmxlWGpFT1BzL0FOc0d1YkxON1RSSlpEQllWVnl1Z2tGRFZObDZuNkhEQ3ZJWC82Qzd1bWhLdDN5NXE2elBoMDhCTk5aaHFzNm8xSW1vYlZ2MHFLRWJ6UXVLZFhFL29WTTVnYUIrM0R5UFlZYXJGWTBlS1N0R2hMUHpHU0VQTVZ2KzlsZTAydlFZLys3MTQ4RG5SYlFxM09ibEJVYldSYjhKM2lueXFOTm1CMi9mQ3NLbXhpR1oxSW9WWW5CbktHakhZNHh2Z2Z5NGlYSFFHY2R5TGNCdDlURjR4WGFHL0x3SUsvQVUyZkQ3RWk2eE1WQXpxS25Jc29jT29vbHlFREpHOFJsQzhQMEJGS0NRNjFlMDEvYkU3bEZ6ZUFPOHJiNWZ1T2Z1Wm4rYzBWanljcUsrQU9LV25PcFFlRHM1dzV5bHRVUUFrZ2dZV0tYUFZWZ0FCa2xEdVpvOFh1L0xVU0hjWEdGSlBIUnh1bEhJbFNHQnRrSy9MOERwU1lsNEovN2l4UW8rMUNVekFuT3pRUis2UDFTRmlrU1FZM1NEcGprbmh3bGgwZWphUWs3VUlPMk13SW9rYnZqUjcwR3J6SE5nUFFmdWEvR1lTQmp0bzVGVXlMcGdrNi9qNFlpeTk1eTB0LzRVN21QMlUxNkNySVdjU3UiLCJtYWMiOiIxNDUyODYyNjMyZjMwYzBiZDRiYzVmMGI0M2RlYTZkZDEzMTM0NWJhZGFkNTdhOTZkNTgxODJhMjNmOTkwMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtuWk9lblpFVkJFUlhIRmNmVVo1clE9PSIsInZhbHVlIjoielgxU2lwZDRqazgwdVBQQ0RKSldzYnZrQjBQb3hqaU5wbHE1bGNKVzJJN01oRVAyMGxFd2E0UU5jNzhtTUhPS0hJT3hBMXFOeXB3ZkdjMEpuWmY0MGg2QnlTek0rVUR5SkdGRVQ4akZjN0VUL1JsbGJnQlhZTXZXZHd0QVFGTXltSEl5ODQzQ3ZmTGkyTjU0YWtKYkJESHNZN0FWajFDbGxZM1QyaDVtTFI0cXNEZ3ZvcDdIamc0YTdkOEd3amZKNjAvUDJsSGx0bUVmb3NnaEw2LzNib2VSVllrWUE0L2hzZDhUNitNcEdjREFzTlluaFgxUDc4a2FEdkQ1Mm1TY0s4anE4MEpaeU1BMmtGTTVybFZjM2tuaXY3YmVGN25TMy91THVJSXl6UjdLb3NnMHpHZzNsbWZHRDFQdnJEWHU0a3hmTnhRTWtJN3VVMzNjdWVLUU9HdmgxU2tITEwzS3ZQRlNiSHphN0JBR2lBVlFuY0IrSGx1MUpwR29VZ1I5OUlHQmdkNC9MNzgyY2IwOVRBY1V3K1lSUUVUZElyeEVNTDdvMDluVHBDc3RWQ0Q2MTZrL01jRWM2QzcvSXU2MlB5NW1sVWhZOGVZSVdPbHBjNXRnZkI4UnAyUUtxTDB3bE5oclBwQ3ZYdENFSkVubmVUaTJlZ3lYWFFOVUxKd2wiLCJtYWMiOiJjY2U4OTgxYzUzZDNmZjg3MmViM2M0ZjQ0MmNiNjEzZGQzNmY1ZTY3Y2EzYzE4ZDhhNTdjOGUyYjZmYzUxYzg4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtSK2NqaVhLQmhsSFd2Ukl0ckVYc0E9PSIsInZhbHVlIjoiV2VuN2VBZXpjblFHWmxlWGpFT1BzL0FOc0d1YkxON1RSSlpEQllWVnl1Z2tGRFZObDZuNkhEQ3ZJWC82Qzd1bWhLdDN5NXE2elBoMDhCTk5aaHFzNm8xSW1vYlZ2MHFLRWJ6UXVLZFhFL29WTTVnYUIrM0R5UFlZYXJGWTBlS1N0R2hMUHpHU0VQTVZ2KzlsZTAydlFZLys3MTQ4RG5SYlFxM09ibEJVYldSYjhKM2lueXFOTm1CMi9mQ3NLbXhpR1oxSW9WWW5CbktHakhZNHh2Z2Z5NGlYSFFHY2R5TGNCdDlURjR4WGFHL0x3SUsvQVUyZkQ3RWk2eE1WQXpxS25Jc29jT29vbHlFREpHOFJsQzhQMEJGS0NRNjFlMDEvYkU3bEZ6ZUFPOHJiNWZ1T2Z1Wm4rYzBWanljcUsrQU9LV25PcFFlRHM1dzV5bHRVUUFrZ2dZV0tYUFZWZ0FCa2xEdVpvOFh1L0xVU0hjWEdGSlBIUnh1bEhJbFNHQnRrSy9MOERwU1lsNEovN2l4UW8rMUNVekFuT3pRUis2UDFTRmlrU1FZM1NEcGprbmh3bGgwZWphUWs3VUlPMk13SW9rYnZqUjcwR3J6SE5nUFFmdWEvR1lTQmp0bzVGVXlMcGdrNi9qNFlpeTk1eTB0LzRVN21QMlUxNkNySVdjU3UiLCJtYWMiOiIxNDUyODYyNjMyZjMwYzBiZDRiYzVmMGI0M2RlYTZkZDEzMTM0NWJhZGFkNTdhOTZkNTgxODJhMjNmOTkwMGE5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtuWk9lblpFVkJFUlhIRmNmVVo1clE9PSIsInZhbHVlIjoielgxU2lwZDRqazgwdVBQQ0RKSldzYnZrQjBQb3hqaU5wbHE1bGNKVzJJN01oRVAyMGxFd2E0UU5jNzhtTUhPS0hJT3hBMXFOeXB3ZkdjMEpuWmY0MGg2QnlTek0rVUR5SkdGRVQ4akZjN0VUL1JsbGJnQlhZTXZXZHd0QVFGTXltSEl5ODQzQ3ZmTGkyTjU0YWtKYkJESHNZN0FWajFDbGxZM1QyaDVtTFI0cXNEZ3ZvcDdIamc0YTdkOEd3amZKNjAvUDJsSGx0bUVmb3NnaEw2LzNib2VSVllrWUE0L2hzZDhUNitNcEdjREFzTlluaFgxUDc4a2FEdkQ1Mm1TY0s4anE4MEpaeU1BMmtGTTVybFZjM2tuaXY3YmVGN25TMy91THVJSXl6UjdLb3NnMHpHZzNsbWZHRDFQdnJEWHU0a3hmTnhRTWtJN3VVMzNjdWVLUU9HdmgxU2tITEwzS3ZQRlNiSHphN0JBR2lBVlFuY0IrSGx1MUpwR29VZ1I5OUlHQmdkNC9MNzgyY2IwOVRBY1V3K1lSUUVUZElyeEVNTDdvMDluVHBDc3RWQ0Q2MTZrL01jRWM2QzcvSXU2MlB5NW1sVWhZOGVZSVdPbHBjNXRnZkI4UnAyUUtxTDB3bE5oclBwQ3ZYdENFSkVubmVUaTJlZ3lYWFFOVUxKd2wiLCJtYWMiOiJjY2U4OTgxYzUzZDNmZjg3MmViM2M0ZjQ0MmNiNjEzZGQzNmY1ZTY3Y2EzYzE4ZDhhNTdjOGUyYjZmYzUxYzg4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1171671321\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-108423996 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-108423996\", {\"maxDepth\":0})</script>\n"}}