{"__meta": {"id": "Xb9d826748785c9637e87def1f309d5dd", "datetime": "2025-07-14 22:28:21", "utime": **********.928822, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.356113, "end": **********.928855, "duration": 0.5727419853210449, "duration_str": "573ms", "measures": [{"label": "Booting", "start": **********.356113, "relative_start": 0, "end": **********.787686, "relative_end": **********.787686, "duration": 0.43157315254211426, "duration_str": "432ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.787698, "relative_start": 0.43158507347106934, "end": **********.928859, "relative_end": 4.0531158447265625e-06, "duration": 0.1411609649658203, "duration_str": "141ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46671560, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00355, "accumulated_duration_str": "3.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8276849, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.859}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.844619, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.859, "width_percent": 14.085}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8525178, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.944, "width_percent": 16.056}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=1ue2vqu%7C1752523517717%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhiNHI0N2E0cFpkTjB6UEpPdmxnRnc9PSIsInZhbHVlIjoiQjJyWThqS21TVXVUdlIyVms3amFLOTBrSjdOZ2ZmSDRmRVF5TVlZbHNxZ0RWL2VSWWtnbnZhSU1WV2lyQzlHM2lDanF6bXI3c1hZSllyWHNTOWtxR1BtZVJ4V0ZOdFQyQWxrMWFzREFWazhYV3NUbWRZV2NzUzFISzVmOENiQnpXVGpHWm1wL2lJMG1oVmYxSTdKL2o4Zm5TWWQ2RGd1cVdkWTJBdE5QcFkvZVlhaEJyaVhKNHdsVXFLTjc5WTV2Sno0SnUyR2xpcHhBbWRnTDNKWnZILy9sQUlwWE4rY0w1WFYvUDdBWjEwK2xqUmxhZDlkTEFCTUducXFtaHpBTm1PRlU2VWFBOFVIRm9xYW5XRUJySEx6eDAvbzltbHkyaFNXNVoyRkFPYnRHNkt5QUxkc2JVZVBGU3dmdHh2VklrUERDdGwvU0hKWFQ1eXdUa2JML3ljYlJZNkoyUzA0WHhIR2pPcUpHa29jdFhOZlgxVWptYVlqS0cyRlhmcG9NSy9MUlNmRGNwNVBKMlhHV3dXWmhBKytxQVVpOWJadExhWHN6WHc4WVorTmN0bExKd05wTWhPalRyaHdEZys1SEhoZ2hsdEZuVkNVY3o2Q2tQQ1FrV05QZHJXS3ZiWi9FUEhOZk1Ca2cwTk1VNHlLWEY5RXdKdzQya1FVZG5ENUUiLCJtYWMiOiJlNDJmOWM2ZDdjMmI4ZTYzYTU3NTZhYjMyMTJmMGFkNDRiZGFlNzdkMzVkODg3OTRjMzgxZDBhOGY4NjQ4YWUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFUcEtEMlFuRGlja3plNTJCbjRyeGc9PSIsInZhbHVlIjoiT1BOTzBSWTRxRzlSei9MOXllcjJVUjVRdkVEUTZ4RS9aOXFJMUk1SDViVTZEcjlTbFZSbTJ2UnRrVG55L1hVZUo3d0ZHMmFKT0dhNUtEQVlFcFEzT3p6UmwyaG90cUR0SHdUWjBTZmxMbG9yaTdvUDNCYllsT2NQY3FnNlgzKzE2MGJHTmtjYWFPK3Zqbithc0tEV1VucklIQzRNdG1PdHdNL21XWVVhNEMxcXptcitVTGhZWDhyeC9OYU5rSWsrMWpMOGN2UFhEcUU0MUJWVWlyREViUUZibU4yQ0hVdmdIL0N4SHFyMUVWUTBndFNCcEx6bWVlY05RckQvL1pXRi9iZzlkdnc0STl3ekY2anpaQUZrMExWelk5STFsVXhXd2JrRzNxNTBWbXozV3JOT3llUDRWb2ZzYklIY29ld3Vvamt4d3lEY3BFakxELzFxbGUzakcyOXVhVzJNWE9NUUxmM2l5RW9lVHpjcjRYTzhtdjhuNWJMeDNHY2JLVkEwZjBXU0YyWUdRc2dycmFHOEQyODA2cFhHR0VlS2dLUjRhMTYwK1FSdVA4TkNBY1RxMk44bWFjUFdqK0ZNa1YzUldFd2N5T1M5eFE3YWlOa3dnWWlkMm9hT1Bzb245ZkpXcUtkdHV3dEtLOUpMM2ttcllZTm4rNWFIaUhHSHJWUUEiLCJtYWMiOiI1ZTdlOGZhMGIxYzZjZjlmMDkyOTdjNTkzMjEzYjNkNjhlZDU0ZjcyNGQzZTIyMjZhMmE3ZmI2YmNhMjVkNTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:28:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZHVUkvSHBhcTNsTHlCZmJSZVlBZFE9PSIsInZhbHVlIjoiUlBCakxkdWVZNXZ2NmtucGZSL3hQOXE5dDJFdDZYZFdEKzM2cFZEa2JlcDk5SmFHaTdHOHZITVJScUhBT2xCNGRVcGUwb05mbVMzR2FnUzR6UHZLc1E1bHROWDlTVUZoOEpReHIwL2c3d1dRbGYwOEZYY3BBemFXOXl1aEV6Qjg3Qnpubk1pM3E1ekNHOTJ2Z1U1Uzg4ZHExODBjTXBkUjRRczlYd1N5WC9UL29LUDllNEd4VnFvR2Q5aWVqdVJqbWFhbkEya3cxSXJLVXlqdk82aEtiK1Rsc05ld0lYcGhLMDFJbnJlaXJpM2FpM0RNU1JKKzkvNkpZVmR2NFBWdzBjUTc1bVR3ZWtvc1dYZXJKYWVOS0hoQUFZaEFybkZQMGpjR3FBOFMxU3RrY2cwcnhNNGNDSW1sU2FxWlMvWnZHTHBlZE1RbkdLQ3dSMUs5SjN1RWkrNlQ4RUQyYmcwZjVXOEZ4d3k4ZUhxUjZYc1JiRVhkVWR4M2hUcGl1SjRhV3VzdWVHR3pRVzhBZFFRSHNBd25hRDdiU09FMGRpUWNteHExSnM1RGJTL0JJZDhHMWMzSGdhVk5jTFNBUEdvWm5JQUtpbjZ5TlFSQjJZZGMybnlQWjZrVWREYkkzZkJrZG9zN2phZjZxclNYaWV4SnE0RlBqYnNrRGhIcndCSVciLCJtYWMiOiJjMTIwNjdhMThhYzU3NzEwZTQ5Y2Y5MDYwOGMxNjFjMjhmNmM2ODJiMTNjOTcyNjU4ZDkzZmU3ZjA4YzUwZGY5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InY0U1FMMDdFcW0rREhrbGZGQU03aWc9PSIsInZhbHVlIjoicFBuNVJleGNkSTVZd2lYeW9vbkRObGtENXh0TDIyaXl5Yk9kdEk5OUp2RXF0WWlXYW9kcjJJRzRYdE1IVXdEQjRmNTcyVCtObFhUb0ZwSU1WWUlBdDRZSTdBYlZOamg3MGRJWitLUTJpak93RWM3OEtIaVUxS0xYdDk5L096Um9jNi9uWTgyNTk2cjJoWGRQU2ZuV2V4b1IrNUpRWWViK2dYbTZrUkJNdFNrZ3RZQVoxNHJ3NTRsQms0QnpuRUwrYWJNUlBlQzFWMEE1VjJjUWJPTkY0cytyM1lUaHpsWGhkcUs1REtzVTVHTHVOUlZrVFVXdlAwcEtnVzBmcE5vTmNoYnU5N3UvaVNMOWc0QXFMMzFJbU5CaUU2VXdDTXBHRjRwS1djTUVwa3Y3V29WMzl4U0pzT0FPVksxMmNCa0R2VCtsSHlLRVFhTmE3QStPTFAwWGExdWcvclpBQUpUMVkySTEyUGI4Q2NpZHNseXV1MVkxT2VDZnpZMTFOdWwraWd1aG1DWGcxWVkzWC9KMTFQSktVZlV3TCtaalJ0SFhlU09xc2wyVHNZSDZHeWYvbzZFbWFUYTd0UEhnWm42dyt2M3hyUVFqeXNsUVM5ZGczWXd4Z2VyL3dCeDduR2RaL0R2cXplVzBEUXJidFNhVUptdDdEQTAxcjhYSkR0MDciLCJtYWMiOiI1YmRiZGMzZDZkYjNiYzExYzZlNGJmOWYyMzJmZjk3ZDU0OWIwYjAwNzU3YzdlYzUwYzdjMTIzNWY1MzEwYTFkIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZHVUkvSHBhcTNsTHlCZmJSZVlBZFE9PSIsInZhbHVlIjoiUlBCakxkdWVZNXZ2NmtucGZSL3hQOXE5dDJFdDZYZFdEKzM2cFZEa2JlcDk5SmFHaTdHOHZITVJScUhBT2xCNGRVcGUwb05mbVMzR2FnUzR6UHZLc1E1bHROWDlTVUZoOEpReHIwL2c3d1dRbGYwOEZYY3BBemFXOXl1aEV6Qjg3Qnpubk1pM3E1ekNHOTJ2Z1U1Uzg4ZHExODBjTXBkUjRRczlYd1N5WC9UL29LUDllNEd4VnFvR2Q5aWVqdVJqbWFhbkEya3cxSXJLVXlqdk82aEtiK1Rsc05ld0lYcGhLMDFJbnJlaXJpM2FpM0RNU1JKKzkvNkpZVmR2NFBWdzBjUTc1bVR3ZWtvc1dYZXJKYWVOS0hoQUFZaEFybkZQMGpjR3FBOFMxU3RrY2cwcnhNNGNDSW1sU2FxWlMvWnZHTHBlZE1RbkdLQ3dSMUs5SjN1RWkrNlQ4RUQyYmcwZjVXOEZ4d3k4ZUhxUjZYc1JiRVhkVWR4M2hUcGl1SjRhV3VzdWVHR3pRVzhBZFFRSHNBd25hRDdiU09FMGRpUWNteHExSnM1RGJTL0JJZDhHMWMzSGdhVk5jTFNBUEdvWm5JQUtpbjZ5TlFSQjJZZGMybnlQWjZrVWREYkkzZkJrZG9zN2phZjZxclNYaWV4SnE0RlBqYnNrRGhIcndCSVciLCJtYWMiOiJjMTIwNjdhMThhYzU3NzEwZTQ5Y2Y5MDYwOGMxNjFjMjhmNmM2ODJiMTNjOTcyNjU4ZDkzZmU3ZjA4YzUwZGY5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InY0U1FMMDdFcW0rREhrbGZGQU03aWc9PSIsInZhbHVlIjoicFBuNVJleGNkSTVZd2lYeW9vbkRObGtENXh0TDIyaXl5Yk9kdEk5OUp2RXF0WWlXYW9kcjJJRzRYdE1IVXdEQjRmNTcyVCtObFhUb0ZwSU1WWUlBdDRZSTdBYlZOamg3MGRJWitLUTJpak93RWM3OEtIaVUxS0xYdDk5L096Um9jNi9uWTgyNTk2cjJoWGRQU2ZuV2V4b1IrNUpRWWViK2dYbTZrUkJNdFNrZ3RZQVoxNHJ3NTRsQms0QnpuRUwrYWJNUlBlQzFWMEE1VjJjUWJPTkY0cytyM1lUaHpsWGhkcUs1REtzVTVHTHVOUlZrVFVXdlAwcEtnVzBmcE5vTmNoYnU5N3UvaVNMOWc0QXFMMzFJbU5CaUU2VXdDTXBHRjRwS1djTUVwa3Y3V29WMzl4U0pzT0FPVksxMmNCa0R2VCtsSHlLRVFhTmE3QStPTFAwWGExdWcvclpBQUpUMVkySTEyUGI4Q2NpZHNseXV1MVkxT2VDZnpZMTFOdWwraWd1aG1DWGcxWVkzWC9KMTFQSktVZlV3TCtaalJ0SFhlU09xc2wyVHNZSDZHeWYvbzZFbWFUYTd0UEhnWm42dyt2M3hyUVFqeXNsUVM5ZGczWXd4Z2VyL3dCeDduR2RaL0R2cXplVzBEUXJidFNhVUptdDdEQTAxcjhYSkR0MDciLCJtYWMiOiI1YmRiZGMzZDZkYjNiYzExYzZlNGJmOWYyMzJmZjk3ZDU0OWIwYjAwNzU3YzdlYzUwYzdjMTIzNWY1MzEwYTFkIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-21******** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-21********\", {\"maxDepth\":0})</script>\n"}}