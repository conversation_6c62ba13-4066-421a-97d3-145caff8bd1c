{"__meta": {"id": "Xdfb0e53748aa713b08a23f6f7c235fa4", "datetime": "2025-07-14 22:31:09", "utime": **********.687347, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.133258, "end": **********.687363, "duration": 0.5541048049926758, "duration_str": "554ms", "measures": [{"label": "Booting", "start": **********.133258, "relative_start": 0, "end": **********.57129, "relative_end": **********.57129, "duration": 0.4380319118499756, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.571301, "relative_start": 0.43804287910461426, "end": **********.687366, "relative_end": 3.0994415283203125e-06, "duration": 0.11606502532958984, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44725184, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0021200000000000004, "accumulated_duration_str": "2.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.600483, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.434}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.60513, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 84.434, "width_percent": 15.566}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-90987076 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-90987076\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532264441%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkxZMnYrZlNzM2dkNHFHUWlRbVJZRHc9PSIsInZhbHVlIjoiL2NxRk9RMzZGdEd6eUxaMmQzTDhaZGJadzlRN01EWmlxak5oWGEvd3FxU2dqUGZvZDFzY2N3NmJ2ZmJDQ2p2UUthcC9GM05McFhzbkE1MHFEeDVSUVhGVnJVdlZ4Um13OHdqeTNvVm0wK0RsRDk1bS82M3VuRHQ2V2JKVkZDR0sxU2xCMTVuMHYyazBlZnBWZEJTTEs0VjJXRlRrdEJ4QnZzT1Vzd0dsUkFURVp3bHVHRERpM08wbEY2VHZOb2RPbFdjbng4MW5vaGJ4TVdoWVhuRXZjMDVGb2t6WEw0U3p2WTdYeW9wSkFBTU5nVkVzRGQweUlUb3Jlay9BZ3FtRkZ0cU1IeE5qSXZLczhXdFJ4Rmd5MTIrMzNmMksyN1lRaXlhTXRwSFZYaFlTZUJFazdTZkp4eGJsdzlNdHNabUNqMUlaWG1rVlh3Z1V3ekNyc29MdjBVNUV1cTNzNDc5WXQ1Vzhmc2IzZXp1K3hxQnF0K0VTK3FPSU9UM0VPQ1o3UDFBdDBCcU85ZTQwV2VtTkZyd3NsOWZwY2dORnVtcUl2YXl0NlBNazB0NVZ5cEtRQzJZVm1ZcGYwTnU1Um0rZ1FpRjhNVUxwNlQycjFSbXJGaGlvNkhxUU40QWpyZTd5VDEvK1ZVSFVDbFo5eUFyL1hFc0h6a1Q0OEFnNFM5UEwiLCJtYWMiOiI3Mzg0M2E4NzAyOGRhMTRlNGYxMDMwMGRiODM4NDMyZjIwMWFhYWMzYzQ3Y2QyNDE4ZTg5OGQ2M2Q0NzI3Y2Q1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im91cUF5QUVWOTlRdEhZN3I3YWJteXc9PSIsInZhbHVlIjoiREQzb3JKN0d4T1VWcGwycTh3NFZqaUI2d3g0SGViZjBxY3BOVEdvb2NPUUN1bXJZMlFqYmM5YVZjSmYyWXFYc2xQV1NvcU1UQm4yNVNjUnNEN0VPcHRaVDJPTW95RXBlMmZHK0pMZXZIUnVtU2tQZE9iU0pCVk5hMklZa0poQTFSb2YxUkdJc2wzWVF3RllDNncwTlg5eVBYSzFJTDd0cFp5T0k5NnJhM2ZmMy9BdGt2YkxjRzZIbER1Wnh1MlVONTN2RjRuajBidDVWZVVPM1o4d1YranFBNjRGUkx2bVZySHlsTG5nMEpyWEJvZzRsaERqQ1lnd1RoTVhoZHcvL0JjSUkyTjdqQnQveW9HTWpYYnpkR09RL2hrSkdpU2o4cUhhbXNjMm5TQkNyeEJXUlNjSFBJaFUwRXlJZmMvUEFuOTY4aldmYnM4NUl2VXVOeHNSbHZ4eVRMbk50RXJ3dEFPMnlWR1pQNWRGMU5jTXE5bkcwZk5QOUozaGNqVnhKcnlrNWdyOVBrcTU0NXhUK05PNXZtRUdxVVQ3UUVwekJwZ3llZGpTYk5Yb0ZEbU91bFFXb3Fjd0hSb3RvWS9ScHZvYkZQKyt5S0tsem9vZC8yZHJVV2FNS0hJdHNpNFFkY01mYUFnNjVEQWg3elh1ak1pSGltUUxmaTNYWVAvNjYiLCJtYWMiOiIzYmJmNTcwMmVhNzY3ODNiMGYxYWMyODY4MWVhNzI5ZmIyMmJiZGU3YTNiN2Q1ODk2M2NmZjhkYzY0ODBlMjcyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2005239229 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005239229\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1672328853 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjAzRWE0VGFSSEdrZW5uQ0RheEZUdXc9PSIsInZhbHVlIjoiMC9QY21GRHV5QXV1c2hwUkRmRVl0OU5XaEplNitMWnA1djQrNDVpTEFyK0dRZ3ZYREVOL0x6K3VvaFV2QWFlaG8yM21DMDc2M3kwSDIxcUZTVTFKRTFyenY2T1BhdDNyb0VUaTVlcEdmTzdzaFVGWmhBSEhjenZLUUJwc3kwTjUrZ1E4S2F6azYxb3JQSDVzYkJSOEtDMnk1UEFGb2Y3NDJGSCtSR1JGTXFYTWtMMGtyMkdFOVJGak42a1dzem5NT2RVQWgxT1A3azdHQTU2d2xxRWZSMElPd1ZTaW93ajdDc1FrZ0pSZFR2V0VKdGdUa0IvKzRDemk4UGVKV3QxQmxWbnNWbEJpeHRvSnloZ2dIS3IwTjYzRVRaZ2hSUFJxRlhzWFp1WGJqakRoVXpGYTlWSFVHaVFyV3dadUwxSnh1ZS9SS21mYXFSU29SNEl4OFhFemJ4SmxnWGU3Rld5ZEVleGVlVDkxSVFNc3lublI3QU1ldWNid1gwN1NvZmV1dy8vanFWQm1JWDlqcGxKQS9IdHYvZjIvY09CWWJxZzJjcHBWaTdmUmRGa1gzWkRBaktEcTJPSTJ6WEs4dEZ1VEU0ckFteGcxVmJQMzRBeHJpOFdtUWdOL2hOeTFoUmlTOHdSbmJBRXlCSkV5LzJUSkcwSzB4dFE0V0ZPSVpLZ0YiLCJtYWMiOiI5ZmJiNzg5ZWZmNzM3OWVkNWIxMDk4OWFhMGYyMTk3MzYyMjBiNzVkZjdhMGMwMzlhNWNiYWYwNTA2OWE3NjFhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ims0RDFLU2hnU3g3cGw3Zy9sdHVnSlE9PSIsInZhbHVlIjoiL0pQNXhMeDhnbWk1TVNGMkxwSy9FcVc2Rm5uUTBFcDViaFQydFRRODY2cVE4citIQUtlMENGMDRHRElCcGUyV09ENGlZcEVOd2FYaDQ2LzY1bTQxT0psZFhsYklML3h3Z056cGJpMkVSLzd4NmJCbXZwU0FselVyc2VCcXVKdTIyRm1rSE5IVU9RUDZIMWpYMmgxdCtCOVkrU3VlQ05lNUdLZTRET2RyUFpPb05aM3hhMjRpT25JWHdXTWxzeUxYRFVISkk0MTNpWFk0Rm5ubU92NnhwWU1Sb2RRY01La0ltNGVaMnp1Sk10TjRpZEc4b3lUeWtaUUpodkNQZTVxcEtCZUN6VlBXZnR6Y0R6UnVzWlAzWVFjNzByZEpTV2hiV1J2cG1FS09kSTg4NXBJNkN5dUM4NVJIMlY2WDIvRDk3TUFVamluMXRZbkFPeHpBZWVBNU8xM0dxZ01NeHcwcitpZVpIaXgyVHZSS1QrbXlVY09PZWlkb0lJamUyQUpjS0RvdzBjMC9jd09SeDNvL1RkQjlLL05hWjY3b3RybmlYa1NUS3JsbVFPTnRMOG9HNE8vMmVJNmZDK3U0OEVvOFg1YWZGK2Fwb3R1czZ6ZFZlSXhwWkNPMW90THptZ1hzQVR5dHVjeHBtblhVcy9sRTNDQlJYVFFoRkNja2JvR20iLCJtYWMiOiIxZTA5ZWFmY2JjNTM3ODExMGM3NDRkMTBmN2EyNzg4OTA4YTZlZjE0ZDQ3OWNhYjQ4OGZkOGFiYTYxOGM1N2E3IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjAzRWE0VGFSSEdrZW5uQ0RheEZUdXc9PSIsInZhbHVlIjoiMC9QY21GRHV5QXV1c2hwUkRmRVl0OU5XaEplNitMWnA1djQrNDVpTEFyK0dRZ3ZYREVOL0x6K3VvaFV2QWFlaG8yM21DMDc2M3kwSDIxcUZTVTFKRTFyenY2T1BhdDNyb0VUaTVlcEdmTzdzaFVGWmhBSEhjenZLUUJwc3kwTjUrZ1E4S2F6azYxb3JQSDVzYkJSOEtDMnk1UEFGb2Y3NDJGSCtSR1JGTXFYTWtMMGtyMkdFOVJGak42a1dzem5NT2RVQWgxT1A3azdHQTU2d2xxRWZSMElPd1ZTaW93ajdDc1FrZ0pSZFR2V0VKdGdUa0IvKzRDemk4UGVKV3QxQmxWbnNWbEJpeHRvSnloZ2dIS3IwTjYzRVRaZ2hSUFJxRlhzWFp1WGJqakRoVXpGYTlWSFVHaVFyV3dadUwxSnh1ZS9SS21mYXFSU29SNEl4OFhFemJ4SmxnWGU3Rld5ZEVleGVlVDkxSVFNc3lublI3QU1ldWNid1gwN1NvZmV1dy8vanFWQm1JWDlqcGxKQS9IdHYvZjIvY09CWWJxZzJjcHBWaTdmUmRGa1gzWkRBaktEcTJPSTJ6WEs4dEZ1VEU0ckFteGcxVmJQMzRBeHJpOFdtUWdOL2hOeTFoUmlTOHdSbmJBRXlCSkV5LzJUSkcwSzB4dFE0V0ZPSVpLZ0YiLCJtYWMiOiI5ZmJiNzg5ZWZmNzM3OWVkNWIxMDk4OWFhMGYyMTk3MzYyMjBiNzVkZjdhMGMwMzlhNWNiYWYwNTA2OWE3NjFhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ims0RDFLU2hnU3g3cGw3Zy9sdHVnSlE9PSIsInZhbHVlIjoiL0pQNXhMeDhnbWk1TVNGMkxwSy9FcVc2Rm5uUTBFcDViaFQydFRRODY2cVE4citIQUtlMENGMDRHRElCcGUyV09ENGlZcEVOd2FYaDQ2LzY1bTQxT0psZFhsYklML3h3Z056cGJpMkVSLzd4NmJCbXZwU0FselVyc2VCcXVKdTIyRm1rSE5IVU9RUDZIMWpYMmgxdCtCOVkrU3VlQ05lNUdLZTRET2RyUFpPb05aM3hhMjRpT25JWHdXTWxzeUxYRFVISkk0MTNpWFk0Rm5ubU92NnhwWU1Sb2RRY01La0ltNGVaMnp1Sk10TjRpZEc4b3lUeWtaUUpodkNQZTVxcEtCZUN6VlBXZnR6Y0R6UnVzWlAzWVFjNzByZEpTV2hiV1J2cG1FS09kSTg4NXBJNkN5dUM4NVJIMlY2WDIvRDk3TUFVamluMXRZbkFPeHpBZWVBNU8xM0dxZ01NeHcwcitpZVpIaXgyVHZSS1QrbXlVY09PZWlkb0lJamUyQUpjS0RvdzBjMC9jd09SeDNvL1RkQjlLL05hWjY3b3RybmlYa1NUS3JsbVFPTnRMOG9HNE8vMmVJNmZDK3U0OEVvOFg1YWZGK2Fwb3R1czZ6ZFZlSXhwWkNPMW90THptZ1hzQVR5dHVjeHBtblhVcy9sRTNDQlJYVFFoRkNja2JvR20iLCJtYWMiOiIxZTA5ZWFmY2JjNTM3ODExMGM3NDRkMTBmN2EyNzg4OTA4YTZlZjE0ZDQ3OWNhYjQ4OGZkOGFiYTYxOGM1N2E3IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672328853\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1024592148 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024592148\", {\"maxDepth\":0})</script>\n"}}