{"__meta": {"id": "Xeb937ab86f681f1b610f5ccb0110ea2a", "datetime": "2025-07-14 22:31:00", "utime": **********.250189, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.794723, "end": **********.250203, "duration": 0.*****************, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.794723, "relative_start": 0, "end": **********.179547, "relative_end": **********.179547, "duration": 0.****************, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.179555, "relative_start": 0.*****************, "end": **********.250204, "relative_end": 1.1920928955078125e-06, "duration": 0.****************, "duration_str": "70.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019180000000000003, "accumulated_duration_str": "19.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.206568, "duration": 0.0181, "duration_str": "18.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.369}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.233136, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.369, "width_percent": 1.825}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.242008, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 96.194, "width_percent": 3.806}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C**********094%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJpOGYxQk5xZ1V1OG1JdHNZY20zZ2c9PSIsInZhbHVlIjoieDgvQTBUSEFwLzhDdUJXTnpLL09pMmU4QThTZ0x1a0VIU1lXYTM1d3dsZWxScDdXTlNmZVRhZUhROVR6YjVhZmJFMnNyWUIycnEzU0hWbktmVzBvdXY3NG5KOUJtZXp1N3NKeEtOS3c1TWhCdUUxamIya1BHUjQ5eHF1K084YzZqZGJaUFRWbWdOMnduOWpxSGNRaHR0RVNsU0FUUkJPZjdvUlNVY1VhVUY0djQ1V0ZEWVBibFVYVkZJRGhNaHNZQTBkVEJQaTNLdTArSStiMENkNmpxaE5naDFQZmJETmFxMCtyNlF4QlJYdnY4anc5amNGdUU3TjZ2dEdESjNCL3paK3VPbVEvZ3pzL2ZmVWVCQ0dNUi9PUVJOdEVBQmdua2FNS1NwMU9yUlA2RkxqbVAzZE1mdlZYWWVRMlpJMER0UGYxUTlZc2hlTVBPK0ZNWE1LMjZEaXhzeEZGSlQwaVU1TXVBcXZMdHFGTHhKdjI5Yzc4Rk05UnhEcnIwQXNKdmU5RnNXWjlmclRudGp0Qjk5Zk02SWNwRG0wR2VXamY5MXFsWkNzWkNsdXNObXBkalc1TDJBTHNQU2FqaUkwRSs5bG1CbjBGSmdQK1pPZFVZOUcyT1NlOVpIYjAxa3l6ODVzaXBOME9JeWQ5dHlxV1dJQkNMUG91N2c1b2d1THAiLCJtYWMiOiIwYjJmNWY0Yzc1MjAzZGZlZmEyNDM5N2U1Mjc1NWYwMDgzYWU1ODJlZWZmMjI1OWVjMWViY2Q5ZGExMGQ4MTlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBwRW5HQndVSm01d3ZDTDg0MitlelE9PSIsInZhbHVlIjoid1RMVWFSM21hd3VVdFBkeHQ4RFJKVEJMbVZGNW5yOCtaZzQ5RDJ6cFlkcytDbWVjaVNhd2k3cjB5TUlQd1BZSktNVjA5WXRMQlJ4MXdzL1BFdGZDOU9rbmk3TFd4MnljUjRqQ3NkamMvY2ZYVnVzTUlmMGxCNzBUOFRUY1M5WFdHWm9XaVEySnN1YzFtYThYa0REMmtHS1Q2ZWk3MjJrOTNqNFhxMS8yaDBRV0prL1BFRlgyUTdCSVE5SGY0TTN6YmZSRmxMVzRoK1FzK25iTC9yTHBEK1FQNVhNdnRma0Q0MzBUR2I1OGExYVc4c2ZxYVVMbk9Lc0ZMRndLU2VWenYxanRBVlZPaWNSajR4STNudi9UK0FGcjRhTGFBUDFsQkpMQVNmVGptbUJpYWdKbVFoVzVuVm53cGlOc1lHQ2VSYUZ4NFNrN0hlbnkrVUR1UlUwYXNOenVadFljaDhaSTlmaUxsWmNTdjkySy93dXRlTUc0MTc2Z2p4OGlhV1RMbkxZZkxscU92VkFxR2FQdE0zOGN5UVhkUDErT2huUVFSVW5aVVRlRkJEMkM5dXNsUFpFR0wyNE9yQ3l3NVdub1pDUEdBQVU1bGNzQ3JNT1ZSUzhJaEc2Qm5uOEpFNFFzZ1ZQK2tTbGpGeDEwcHBGSHVETVhuODgwbjhkcXRlckUiLCJtYWMiOiJiNTQzZjE5ODBkNGRjNDIzOWUzYmFkYWMwYzBmZTNlNzZiM2MyMzEzYTYwZjYzMjYxYTNiZTczMzU0YjMzMjM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-121019333 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121019333\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1163884392 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZJUDNHS2VnZzVWL1VXa0tvRXZ1Qnc9PSIsInZhbHVlIjoibnNQT2xVR0tjaWxqOW1wa0NKNUlVUlBFbkdNclR0ajY5eHJZWG1ZM3BqTmR6dCsrajNpR3pGN0l3YW13VWJpeENhbXF6Zi9ZZHpFUzd5RmNUSmVsQm4rakc3OFByYndrUXh0UDJnTXIxQVJwcFk3VnAvWEhobCtYRThuL2hFMURmdnBqOWZrL3hTM2JnMWowWXZkNVR1ZTlvalF3TDZGM0pOcGx6ZHg1K1F2ZHBhZGI1ZmFBem5LbE5EZktBUjZWZ3RSOVo2QW1aOW9kRCtJNW9BMjJKSU5jcVBSNVJiRjB1KzNFVSs4a3h6TmNJcGNpRXVONGU2T3lhSnJaZ0lYeVBhVnQ5a2hsdG5TS00yQUpqeVBUbmNHMW01VE5kRWpzNU12STlYc2x3WW5rSEI5UHgvUFRqRFNYNXphSnRMU3RLcmd2OXJ6cTBJMFNNUUVqMHdsWnZNWFJiREh2MkRUWlprVGdwNEIzL25DMzhaZWt4YXVseUxvbDJOTVZORDlaY084OHlJNTMvajQrZlpIUkpnOW5id0crRzhEbmphRmFNK2VpcFZwR0h4WlJZODJQL1dqVVh2L3M4QjBzMjhxMzZRZW9McXBsc0xTbEp5ZkwrdVNkdGVHUWhSd0prNEV6NDFLaHYyWW1kbldNZ3VzQW1ZUmtUTVZ4OW12NFJOVTUiLCJtYWMiOiJhODQ1Yzc0OGRjODU5NGJlNWQ5ZDFlN2M2NzdhMDAxZDNkMjViNjU1NWM2NDdiZTkzMDNmODkxZTY2YzM4NmEwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdEUzJWK2FDdHNaTUVjY1p2Y3hXeHc9PSIsInZhbHVlIjoiZlpYOXhOZk42U0dNbi9adGVjN2ZMTE1iMFVDWGxMUG01VEwrSW5mbnNhZWhodTltKytPS29PcE9EVmV6VHhUOGtpMmdIYW1sL0dCcmNEaEJMZTZNZGNLQlRWZy9ZQUE1RTRUWnBQV3hsM1lSSmgyWGFrZUtUY0xQTFNQaTA2Z1ZOaXJySXdBNFk4T05FY3RtN3VhbFZsT0xKQ25UdHFvTDNpeXZ3ay9MOFExSWhyZkRPLzVLd2Z5d1owUnd3d0VjNHloem8vVUVPTzVqUVd1NUZiQnFDMzdJaXdEbEF0bFoyV1Nlczc5YWJDK2hQZEFZcHJpaGd0RHllT1ZoZU5XSERnd0VxNG5WcmV6OVo5VCs5bnNIMUVicEVDczZtWk1JQzI1ZFRGSEJPbVdCbGo5L0pCcE5tdndJM0F6bERWbCtxd0hhdmJmYVltS0dxZjI4dnhzZDd1UnprYTFPYjgzc25ZMW5vTWJ1c3AzZkVxb1owS0FpWXo3NnZBMHdLZVViTWNxa0xPWUtjVGgxd0Ftd3hEamUvOWVtQmJ1d1pIdDA0OUlxQ3YxUkpDeHprRnR1bW43ODg2WG9XMzMzZERtVzUrOVE4V1ZMczhJRHVUbnZ4dTBRRmkxN1JRSzRNdTZDTTNhT1BoejdyVzh0dGNWeVlhWUQwQmZIQTBxM3hoOWsiLCJtYWMiOiI0ODAyNGYyZGM5M2ViOGE3OWY0MDdjM2U0OWUwMTQ4ZmRjNzZlN2M4MzJiNzhmYTdhNmQ0YTk4NzIyZjM5Yzk4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZJUDNHS2VnZzVWL1VXa0tvRXZ1Qnc9PSIsInZhbHVlIjoibnNQT2xVR0tjaWxqOW1wa0NKNUlVUlBFbkdNclR0ajY5eHJZWG1ZM3BqTmR6dCsrajNpR3pGN0l3YW13VWJpeENhbXF6Zi9ZZHpFUzd5RmNUSmVsQm4rakc3OFByYndrUXh0UDJnTXIxQVJwcFk3VnAvWEhobCtYRThuL2hFMURmdnBqOWZrL3hTM2JnMWowWXZkNVR1ZTlvalF3TDZGM0pOcGx6ZHg1K1F2ZHBhZGI1ZmFBem5LbE5EZktBUjZWZ3RSOVo2QW1aOW9kRCtJNW9BMjJKSU5jcVBSNVJiRjB1KzNFVSs4a3h6TmNJcGNpRXVONGU2T3lhSnJaZ0lYeVBhVnQ5a2hsdG5TS00yQUpqeVBUbmNHMW01VE5kRWpzNU12STlYc2x3WW5rSEI5UHgvUFRqRFNYNXphSnRMU3RLcmd2OXJ6cTBJMFNNUUVqMHdsWnZNWFJiREh2MkRUWlprVGdwNEIzL25DMzhaZWt4YXVseUxvbDJOTVZORDlaY084OHlJNTMvajQrZlpIUkpnOW5id0crRzhEbmphRmFNK2VpcFZwR0h4WlJZODJQL1dqVVh2L3M4QjBzMjhxMzZRZW9McXBsc0xTbEp5ZkwrdVNkdGVHUWhSd0prNEV6NDFLaHYyWW1kbldNZ3VzQW1ZUmtUTVZ4OW12NFJOVTUiLCJtYWMiOiJhODQ1Yzc0OGRjODU5NGJlNWQ5ZDFlN2M2NzdhMDAxZDNkMjViNjU1NWM2NDdiZTkzMDNmODkxZTY2YzM4NmEwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdEUzJWK2FDdHNaTUVjY1p2Y3hXeHc9PSIsInZhbHVlIjoiZlpYOXhOZk42U0dNbi9adGVjN2ZMTE1iMFVDWGxMUG01VEwrSW5mbnNhZWhodTltKytPS29PcE9EVmV6VHhUOGtpMmdIYW1sL0dCcmNEaEJMZTZNZGNLQlRWZy9ZQUE1RTRUWnBQV3hsM1lSSmgyWGFrZUtUY0xQTFNQaTA2Z1ZOaXJySXdBNFk4T05FY3RtN3VhbFZsT0xKQ25UdHFvTDNpeXZ3ay9MOFExSWhyZkRPLzVLd2Z5d1owUnd3d0VjNHloem8vVUVPTzVqUVd1NUZiQnFDMzdJaXdEbEF0bFoyV1Nlczc5YWJDK2hQZEFZcHJpaGd0RHllT1ZoZU5XSERnd0VxNG5WcmV6OVo5VCs5bnNIMUVicEVDczZtWk1JQzI1ZFRGSEJPbVdCbGo5L0pCcE5tdndJM0F6bERWbCtxd0hhdmJmYVltS0dxZjI4dnhzZDd1UnprYTFPYjgzc25ZMW5vTWJ1c3AzZkVxb1owS0FpWXo3NnZBMHdLZVViTWNxa0xPWUtjVGgxd0Ftd3hEamUvOWVtQmJ1d1pIdDA0OUlxQ3YxUkpDeHprRnR1bW43ODg2WG9XMzMzZERtVzUrOVE4V1ZMczhJRHVUbnZ4dTBRRmkxN1JRSzRNdTZDTTNhT1BoejdyVzh0dGNWeVlhWUQwQmZIQTBxM3hoOWsiLCJtYWMiOiI0ODAyNGYyZGM5M2ViOGE3OWY0MDdjM2U0OWUwMTQ4ZmRjNzZlN2M4MzJiNzhmYTdhNmQ0YTk4NzIyZjM5Yzk4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163884392\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}