{"__meta": {"id": "Xf30b629614d47de256b571712b6ea364", "datetime": "2025-07-14 22:38:05", "utime": **********.840005, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.372277, "end": **********.840018, "duration": 0.4677410125732422, "duration_str": "468ms", "measures": [{"label": "Booting", "start": **********.372277, "relative_start": 0, "end": **********.743984, "relative_end": **********.743984, "duration": 0.3717069625854492, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.743996, "relative_start": 0.3717188835144043, "end": **********.84002, "relative_end": 1.9073486328125e-06, "duration": 0.0960240364074707, "duration_str": "96.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48491968, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01978, "accumulated_duration_str": "19.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.783589, "duration": 0.01475, "duration_str": "14.75ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 74.57}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.807992, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 74.57, "width_percent": 3.893}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8237941, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 78.463, "width_percent": 4.044}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.825981, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.508, "width_percent": 3.033}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8311949, "duration": 0.0028599999999999997, "duration_str": "2.86ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 85.541, "width_percent": 14.459}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1755008266 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1755008266\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.830159, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-253478876 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-253478876\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1733031127 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1733031127\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1205178188 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205178188\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9QZFQxcGpOVzUzcEZZQXppSjNneGc9PSIsInZhbHVlIjoiK3o0S0RNOWpUcHhSVVZXOG8zR3FocW4wUHFOYjJLTS8rWjg5dlVHcHI0NjV3c3F3U1M3ZXlLOWNpVXZ6cWxxSG40SEVrNmt1RlJzUEM2RC9sS2Z4d2tMNE5lSWkyeDV3NTd5b2NITXIrdU1BSVBhNVA2VnU3MnNCNzdxcysyWk05QUpaNVJvVXY3RWNqcHRQay9POWNqRzh6VGdyQitFYzNUcVE3Lzk0Q0hWbjBSY3VNSVp3aTRqT00yY3VCNmFhRjFQcEtIeUNaVXNFeTNOWXZUYjBrQ1NBOWFPNEV0bEI1ZWF1cFpNek9Rb0h0UVBJT1dvWGNnS2RaUEplVWlyYkdKZktRbjdvMXRZSTFMbEk1SmZVUjhpS0d5aGtYZVR4MnlWUXFDSTJYdHF5cGd0Mm9ZMFZnWFhoM2g3N3prT24xQk5meTJ2b3d1eHZ3Y3I2Q3JDb2o3MlVIQm5EMTduRHlxZWFqYjVDWGVmZlArcjV3bkROK1RJaEQ0ek1jU0hzVWVMMU53MEgxRVJ2b1RZUWgzVDBGeUMzZzA2Y0c1NkFpVk1CeWNvOWIxMWU2QmNKakp3bW1ZTnE0UzdtQklaZ3JoajBWdGdsbnUzSjdrRFhYcGpiUjQxZm1aME8rb29GeEt3QlI1TWl0cW1QNkZiUmNsbzN0U1RWRDJ3OWxWVnkiLCJtYWMiOiI4NTM2M2I3MjNjYjA0Mzc4OWVlOGU3NDYxY2EyZGRjMmExZTNlOTI0OGY1NGQxODM3MWM5Zjg5ZjViNjAyNjY0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdoK2VzOUpHYS85WFNCUnhUeTZFR0E9PSIsInZhbHVlIjoidnUvTFkxNmZ1bysrVE9HdWgraytsQ0JWRjlTc3M1MmdLY0oxYm9TcTVDK3BJRFA1d0tCeklhZDlob0hNM0Z4eWpUbGdKZkEzS0EwZEtrekcwQUVXRTVNSkgwejhra2xMbGtLemFHb29mZ2ZNb2tzcTBkbU1MYzNXbHl1VFhSVGQrTEdiaWZxSEkwNlJGazZPbzJnNS9FSTBZdVFpNXVITy9aYkU3NCtNRnQ2ditjOXpBQmo2ZFUxRER5dUxWaC9ScmthbUp2dmd1NVNUL0FFbG42ZFk5ZFZZQUVvNHU4N0xSSG5vSG51aFNhRnQ2UVh4SzZBNTVVaFM4enNlYnVpREkzR1VSV0dmRVI5SnBmZng3RFJla0NwQzRCMGYvOUtMR0hOUUVLUTJZdXU2Ukg3M2k3WTNxWXhka3pPUEZRWFpkc05ybU12SFhVZGYxWWdQeGIwMkpCZjI1WHpQbFlwQjNnVGI2eTJmTEVvNldwRDUyYUZ1a0xvRExxaEV4VnpuMktHV21rVlRkbzRDOUU2R051ZWtVYlcwOWpVdWM0VG4yQ3d2Ri91dUxZUHRneHhkRzlua3E0a0tka3Z6UGFQYTJJT3ROZjF0WTJSa01NVWZxdkpDZldFam1laHJSaFFqaGFaekpjeS9lZFJmUjF5TzB3MW9YZUpDNTU3emxUVTEiLCJtYWMiOiIyNWQ0ODQ3ODNlODJhYmVhOGJlYmZhNzQ2MWVhZWFiMzgxYzY1MTAxZTA2OTYzNDA0Y2UxMWRhZTZlY2M5ZjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1431081505 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431081505\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-341085409 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:38:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IldZQzBhWlFaZGd3OE1vb21ueXEzN2c9PSIsInZhbHVlIjoiRytzVjFDbU02N3A5U1hsb1Z4Y2xzdEticjZxUTJvSXRRb1Q1NXh5bkRSbVBWYzNCbHIxbkdoYnM1NjZjSzZ5OUpQa25vdjVyWk5hamF1K01NSVBWS3VMZTVHVXpIZk1YaEVqSVdpbWl2d1NqaHI4ZFlndW02NmpET1NqVXJlSSswbHpGRUk3S1NWOUNhc1RHWUxlYnM1NGZkbi9tUzdxemdjclVVSElNNjhuTm14OThua2hjeEYwejJJM0tTU3RsZk5iMDI4bWNiTnBRNkVyKzBacko5K2I1TEp2cStYZnNDNEZNOVlYV2ZZbmw1WENyQnVoNGRkVzBaUksxd0pTVis0S0twYnFsNzJnUlF3cHd4MG5kdVYvazBMZXBjSk9xSS9KTXNJNllwSzhJZjRIaEdjQU9mbzBNcXkzWGtqdk96cWtLbmlEajNmY3J4MDE5TGtmaXVsSmh3eUorNDJ6R2NkMi94QWQyR1BMSmVRNk1Nc00rakFxd1VnRlBjS3d2bWhDV2YyS3RidTFYeXZyV0NkU2hlTmx0SURyNUFHYi85cklBS0h2bGhnbm45dElBUXppMm82R0svcUozTldkYXZja0JIblo4NU5rMU9pVGR6ZVl5eWZlR0lSeU9aRHUrOXpYTVVQdEl6Y0VEbVFzUHE5aFR5VnFxZ3N6NUF5enEiLCJtYWMiOiJkZjIxMzI2ODlmZWY0N2QzZmMzNDI1YjM4MDMzZmY4MWM0ZGJkM2UwYzE0Njc3ZjIxNjViOTQ1YmEzNjNlZWRlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImloOE9mL1VUNjFBRDN3dVNKYU1JeWc9PSIsInZhbHVlIjoiWThmeHRRemJheGo1cFdDRVI3QkZzN09GSXNTejZpOTl2Qk5kT04rMjkwaTM5YnVGN0wvMHlJakFvcUI0NVk3c0ZqRUQyVXd1NXArWFFDSGVjTFVHQm1Mc0tBVDVyR0ozSDlPaFI1VytpNnI2Q2FTcjEzTVdhV20xbFBvMllNM1BMY0xoZlFRMDQ2d28ycmljbW1RWEhIZHhrNjBEeGZRa2NmU2QyZkZJZVJWb29sSThNcG92TkF4d3NzMzk2aWVIS0xtTFk3V2dFWjdCSVdvQXZZUFFrYWkvZWpWODNMakg1UnlRbG9OazkxaDNnU0RTT0xxNzUvbkNadktUVWc5UzdsK2MzbGlhdU9BQWdXSUdIL0dGbHpHWGQyRWJFKy9ELzg5Z0lMbWlQWVNDWVQ5WVhRYXdwRkxlOUtDVnV6VS8zc2VQdUVqbXBJWno5QkxUdXVXMHJqQUZqNlh2dXB0alRNNGJhTTg5MlZFVFgvRzBHRFVxSnN6U1dmKzdWYjNNNWlRbS9ZdGNFVkJIc1VMbmk4dTRJaGhuekxUQ0x2cnQrYXpDN01wVCthMDdmR0RCWjYyQ1hpdW5qYUpxMW44NnhFUEhvaFJ6dUgyNTVYdjZFR2oydDFZcytCUndRT2lYRFYzd3g5VTdXY012emtsOWFjNWw0TnRBdjJzUTMzUVkiLCJtYWMiOiI5M2Y1MjU4YjNjMGQxYTgwYTgzMDY0YzBkYmNmZDMwZmYxNjJjMWFkMzU5YTNhMTJmNjI3YjVlNTcwMzk2NWEwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IldZQzBhWlFaZGd3OE1vb21ueXEzN2c9PSIsInZhbHVlIjoiRytzVjFDbU02N3A5U1hsb1Z4Y2xzdEticjZxUTJvSXRRb1Q1NXh5bkRSbVBWYzNCbHIxbkdoYnM1NjZjSzZ5OUpQa25vdjVyWk5hamF1K01NSVBWS3VMZTVHVXpIZk1YaEVqSVdpbWl2d1NqaHI4ZFlndW02NmpET1NqVXJlSSswbHpGRUk3S1NWOUNhc1RHWUxlYnM1NGZkbi9tUzdxemdjclVVSElNNjhuTm14OThua2hjeEYwejJJM0tTU3RsZk5iMDI4bWNiTnBRNkVyKzBacko5K2I1TEp2cStYZnNDNEZNOVlYV2ZZbmw1WENyQnVoNGRkVzBaUksxd0pTVis0S0twYnFsNzJnUlF3cHd4MG5kdVYvazBMZXBjSk9xSS9KTXNJNllwSzhJZjRIaEdjQU9mbzBNcXkzWGtqdk96cWtLbmlEajNmY3J4MDE5TGtmaXVsSmh3eUorNDJ6R2NkMi94QWQyR1BMSmVRNk1Nc00rakFxd1VnRlBjS3d2bWhDV2YyS3RidTFYeXZyV0NkU2hlTmx0SURyNUFHYi85cklBS0h2bGhnbm45dElBUXppMm82R0svcUozTldkYXZja0JIblo4NU5rMU9pVGR6ZVl5eWZlR0lSeU9aRHUrOXpYTVVQdEl6Y0VEbVFzUHE5aFR5VnFxZ3N6NUF5enEiLCJtYWMiOiJkZjIxMzI2ODlmZWY0N2QzZmMzNDI1YjM4MDMzZmY4MWM0ZGJkM2UwYzE0Njc3ZjIxNjViOTQ1YmEzNjNlZWRlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImloOE9mL1VUNjFBRDN3dVNKYU1JeWc9PSIsInZhbHVlIjoiWThmeHRRemJheGo1cFdDRVI3QkZzN09GSXNTejZpOTl2Qk5kT04rMjkwaTM5YnVGN0wvMHlJakFvcUI0NVk3c0ZqRUQyVXd1NXArWFFDSGVjTFVHQm1Mc0tBVDVyR0ozSDlPaFI1VytpNnI2Q2FTcjEzTVdhV20xbFBvMllNM1BMY0xoZlFRMDQ2d28ycmljbW1RWEhIZHhrNjBEeGZRa2NmU2QyZkZJZVJWb29sSThNcG92TkF4d3NzMzk2aWVIS0xtTFk3V2dFWjdCSVdvQXZZUFFrYWkvZWpWODNMakg1UnlRbG9OazkxaDNnU0RTT0xxNzUvbkNadktUVWc5UzdsK2MzbGlhdU9BQWdXSUdIL0dGbHpHWGQyRWJFKy9ELzg5Z0lMbWlQWVNDWVQ5WVhRYXdwRkxlOUtDVnV6VS8zc2VQdUVqbXBJWno5QkxUdXVXMHJqQUZqNlh2dXB0alRNNGJhTTg5MlZFVFgvRzBHRFVxSnN6U1dmKzdWYjNNNWlRbS9ZdGNFVkJIc1VMbmk4dTRJaGhuekxUQ0x2cnQrYXpDN01wVCthMDdmR0RCWjYyQ1hpdW5qYUpxMW44NnhFUEhvaFJ6dUgyNTVYdjZFR2oydDFZcytCUndRT2lYRFYzd3g5VTdXY012emtsOWFjNWw0TnRBdjJzUTMzUVkiLCJtYWMiOiI5M2Y1MjU4YjNjMGQxYTgwYTgzMDY0YzBkYmNmZDMwZmYxNjJjMWFkMzU5YTNhMTJmNjI3YjVlNTcwMzk2NWEwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341085409\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-884757287 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884757287\", {\"maxDepth\":0})</script>\n"}}