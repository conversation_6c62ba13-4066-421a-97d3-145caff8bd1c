{"__meta": {"id": "X43d6e28c98c74a25de0f7538b1597428", "datetime": "2025-07-14 22:40:36", "utime": **********.129935, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532835.649865, "end": **********.129957, "duration": 0.48009204864501953, "duration_str": "480ms", "measures": [{"label": "Booting", "start": 1752532835.649865, "relative_start": 0, "end": **********.053716, "relative_end": **********.053716, "duration": 0.4038510322570801, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.05373, "relative_start": 0.40386509895324707, "end": **********.129959, "relative_end": 2.1457672119140625e-06, "duration": 0.07622909545898438, "duration_str": "76.23ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48506640, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.004, "accumulated_duration_str": "4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0883842, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.75}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.1004279, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.75, "width_percent": 14.75}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.116371, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 74.5, "width_percent": 14.25}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.118652, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.75, "width_percent": 11.25}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1294042177 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294042177\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.122695, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"12\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 192.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1113732005 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1113732005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-507567978 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InREeTc2blBNcXB6eTF0VmF6VVc2Unc9PSIsInZhbHVlIjoicngzNUxTQXFhcTBNZzRBWVlUa05sdS9yTENnN3d1NjN3RnZ5T3NTRzJaMG9HTEtRRjJVWFR2QWh5SnllUjRmdTUxUEJvOTlyTkIyaXk2MXZHQk55Qy9PdzZYRk1hekJob2hSa3BnblgwTDVlT094LzdYM2dNT0pXNHBYNmxXQVZpcE5DelFaZVdkUU52N2ZOSWg2ZU8vUWxjYkNXL1BXTWFzNU5VVWZXaW1hN1h1SGdWZkFwaEZiM2FmQllyYlJ0TVRnc2lucVRhZ0ZkRWNjL2Z6VjI2THczMjJWOXBHS09rWE9pZGZnS1hOYVVvRGlaQWlFR1N2bGpXcWFZTmFxVjRyT3hoUDhFUkVrNHBaeEVScVY2bG1uWGpSUGlCVjNvSnRLa2RXY2tTa3dnak1XL3ZObS83bmsxY1I4N3dOUnNyb085Rk94R3QzMWVSMWZHZGlQb1RQYmtDdEhvRWNYcUJOaEZ6RWdqYUxYRE5oYU04Y1RIa2QycEZ5NU1zbW1Hemp5dnFFWENsMGZ4LzhjZUZ0enB2RWVqSzRqMWJjc2lDV2FYQlZwVUVPRFlqVmpna3BaUUQyL0NhcVpLYlBGSmdXYzJKVXJpQ09WamtuZFkxTEducWJCNkZpakNrOXVOSkdBaEhiWWU1em1PbzZnMTBhV3FPa0V3b2NhbTdweEIiLCJtYWMiOiI1ZDYzMWQ3MjNiOTJiODIxYTRmOWJmYjJjNzAzMzMyZDY3YmFlYzUzYzZlYWUxMjE2ODA2Mjc5NTNiN2Y1ZmM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imx2RDd0RE02am1rZ1dMMjBldmpmOFE9PSIsInZhbHVlIjoicWcrckRWRkZoTVQ2S2VjdFdFZWNkdTBzenhJWWt4dVJ6Vk1OU0E5UC93R2dzdTBrR3l0dWY1cUFvRmVlUHB3N0JRdzBDd29BWjg1RGxVbGVna3VBNjlOQ1dJeFRUN25KL3JSZUNNMjcwMXhHVWZBVHdZazlRcGJVNGlrWVROK2ZhTGszTklna2xzdnN5RzZMSVRVN2hEK0pBelp0RzB0QTd0OXNEdGk2YUtCSzlxSncvbTRpbTVXcXNDdGNBS1QzM0hiTHg1VTNWbUxZaGtkM1FRTFBBMVN5MDBYVVlVQ3dwWDc1SzN5N2Q3RWpaVlc5aEhKdHYyT3pORkdXZ0RIZHB2aXlNTlA2bGZBK1RvTEZab05ZejgrZ1hRQ3FNeHUyVmUxUmFHOGN0a0toSCtmRmdvUmZyKzlPMWRoUHBDdVAydFB1SURjNWUzYTNUV0xWWTV0bng0UGthRC9YVkZteDlUN254TzRzVCtqR0RDQ0VDcHN1d2VMWGdyb3h2U3R6dlhCMHduaEpLRmdzenhqRUhmQkZvTHdYRWVxWitoWUtqNklSNmYzUWxPTkwyYkNzUTcyZHNTNXM1emx1ZVBEcHo0aFcweVRzRnZvS1VsMlZhdmE1TER3TWpiM2Mzd3VMekNpVEh0YWFVQmNuUCtXYTFiaXdRdDlGbC9jczRxVzUiLCJtYWMiOiI4NjI4OGZlZDNlNWM3OWViZGMwMDc1MjQzNDgwODQwZTY1OGJlOTZiNWEyNTdlYWNhNzk3NDliZmM1Yjg2ZjVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-507567978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1422159202 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422159202\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1373645726 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imdxci9QeTVXNzFDT1pSa0ZTdmFuOUE9PSIsInZhbHVlIjoiUG95S3FBNnVjaHVXYk8vVUV5RGl2d1A0YVNlRkgrRjBDeXJIRzJpUGhocGVYRU8rUU8yYmtPb1Q5UlkxZzdRSzc5cmVFOXROR2RZMHpUT2RzbTVUTE9UUFptZVl2UHIwNk0rU29Udmw2aGE3cjlKd1R0Z0VQdkpwQlFXNHpvVG1mTlQ0cHF5dmh4Q2FTMmQ5NHN3bmJCTS9SMGl1TXhvRzhsVzh6Y3JKTHJwaVliN2ltbEtiWk1FalZwcmRwblpRWnV5SkFmU0hZR0pVV2d3MFBMQVRnN1JyY2ZGcm9qR3htR0s5ZWJKU3NFc0dNUW5Nb01XSTNPQ3plMStPdTEvWk83ekI0MGc0M3JoS2FsTmRyTjBJY0JhZzBTTHJNditUV1R3S053WVNRTk5NYzREeHMwcllPRjhmOGU1cDhQcEpidENoMis2eGZzQ1hUeTUvSDdXVm9qbkozdHhDekI3U3MxYkFUWHJDakRCRkNrdkd0R3U5bFNLVmkzbEdyY2t0emlUT1RiNEtxdjVoK0pQNjNlRTYyY3RVQUlEbmhXWEdMSEdwUUZ1RjFLM0VQK2I4Nmk4N2h2SVJuUC9yZ0tscFZXcktiTENLVTVLT2tEM2xzZGRnTW9SNnRXcWgrOEJJNytVL0dtSFZRaEhZMGZtcGo0NEY5d0hqeE84aTdpYWUiLCJtYWMiOiJhNWQ2YTQzN2EwNGU0NThhYTIyOTAyZmE4MzJiNjNlNWZkNTllNDYxYzc2YjljY2E4Yjg3YjY3MTljNWVmNGVmIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRLNDhtMVdKMkR6OHFnN205ZGs3bmc9PSIsInZhbHVlIjoiSEFYRXR0R3lvb2orVGZHUVpnU3c4eUpYNE9CMU1uLzRpUjl0RmVzd1J4dytoTkdscnhvOUh4ZU5VcWJJUzdvZkszOXEzZFRrZnY2Mk1VTWJqRjhEQm1rL1ZUSXV4WnJSR01vNDRIZnBsSURidkFGNmkzc0RtTTZ5WndZb0Yzc3ZGZUUvVzFxRlVJT3JsMDZxeEZDcjJpQ0dPbGZYb1k0UXFhaG9WWEJMRitHWm1hTHUyTEE3TE5JRVkva0lFQ3BHUWFkelUzQ1BQN3ZiZDVKTmpKNzgyRmFOK3ZjU0NwTG10S0cwRnFFN2I4SHJlN2p5bnBYN3BSZWd3Q2tlbE56MHdYRG1xR2h2ZHJ3TXBFMGptQTRmWEZ2QjRWd29TMDJxTFRRVFBBTktobi9RYTMwVTVxYWtXS0o1K041eXRrZ2Nib2JUcTZ1cHN6Q2tDUEZZd1FrSkRBVnZacHJPOXNEM0NnYkdNaHVrcHIrYXcwRklZbEsvbmJiUkxqS1JOQ0h3NTYvNlF1TFFiamVHQmhkcDBlRGc3b2tSenJKajVENjlBVHdYNUk0TTE1YmtGZllxNWxDcmRhb3czZi9IUC9yUE5VN3FsTExpNXgvZnpuYjVxY2wwbmcvRFhpblVWKzhqaW9tUHpsUDhGTFZHaElmMjU1bDdSUFFvVW9DZ0dKNW8iLCJtYWMiOiI5ZWNhYTUyYmVjNmE0Yzc0M2I2MTFlYzM0Y2QyZGYxNWYzZjczNDBkNWFmNDljMDZhZjdjNGFhMTk1ZjY4ZTFiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imdxci9QeTVXNzFDT1pSa0ZTdmFuOUE9PSIsInZhbHVlIjoiUG95S3FBNnVjaHVXYk8vVUV5RGl2d1A0YVNlRkgrRjBDeXJIRzJpUGhocGVYRU8rUU8yYmtPb1Q5UlkxZzdRSzc5cmVFOXROR2RZMHpUT2RzbTVUTE9UUFptZVl2UHIwNk0rU29Udmw2aGE3cjlKd1R0Z0VQdkpwQlFXNHpvVG1mTlQ0cHF5dmh4Q2FTMmQ5NHN3bmJCTS9SMGl1TXhvRzhsVzh6Y3JKTHJwaVliN2ltbEtiWk1FalZwcmRwblpRWnV5SkFmU0hZR0pVV2d3MFBMQVRnN1JyY2ZGcm9qR3htR0s5ZWJKU3NFc0dNUW5Nb01XSTNPQ3plMStPdTEvWk83ekI0MGc0M3JoS2FsTmRyTjBJY0JhZzBTTHJNditUV1R3S053WVNRTk5NYzREeHMwcllPRjhmOGU1cDhQcEpidENoMis2eGZzQ1hUeTUvSDdXVm9qbkozdHhDekI3U3MxYkFUWHJDakRCRkNrdkd0R3U5bFNLVmkzbEdyY2t0emlUT1RiNEtxdjVoK0pQNjNlRTYyY3RVQUlEbmhXWEdMSEdwUUZ1RjFLM0VQK2I4Nmk4N2h2SVJuUC9yZ0tscFZXcktiTENLVTVLT2tEM2xzZGRnTW9SNnRXcWgrOEJJNytVL0dtSFZRaEhZMGZtcGo0NEY5d0hqeE84aTdpYWUiLCJtYWMiOiJhNWQ2YTQzN2EwNGU0NThhYTIyOTAyZmE4MzJiNjNlNWZkNTllNDYxYzc2YjljY2E4Yjg3YjY3MTljNWVmNGVmIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRLNDhtMVdKMkR6OHFnN205ZGs3bmc9PSIsInZhbHVlIjoiSEFYRXR0R3lvb2orVGZHUVpnU3c4eUpYNE9CMU1uLzRpUjl0RmVzd1J4dytoTkdscnhvOUh4ZU5VcWJJUzdvZkszOXEzZFRrZnY2Mk1VTWJqRjhEQm1rL1ZUSXV4WnJSR01vNDRIZnBsSURidkFGNmkzc0RtTTZ5WndZb0Yzc3ZGZUUvVzFxRlVJT3JsMDZxeEZDcjJpQ0dPbGZYb1k0UXFhaG9WWEJMRitHWm1hTHUyTEE3TE5JRVkva0lFQ3BHUWFkelUzQ1BQN3ZiZDVKTmpKNzgyRmFOK3ZjU0NwTG10S0cwRnFFN2I4SHJlN2p5bnBYN3BSZWd3Q2tlbE56MHdYRG1xR2h2ZHJ3TXBFMGptQTRmWEZ2QjRWd29TMDJxTFRRVFBBTktobi9RYTMwVTVxYWtXS0o1K041eXRrZ2Nib2JUcTZ1cHN6Q2tDUEZZd1FrSkRBVnZacHJPOXNEM0NnYkdNaHVrcHIrYXcwRklZbEsvbmJiUkxqS1JOQ0h3NTYvNlF1TFFiamVHQmhkcDBlRGc3b2tSenJKajVENjlBVHdYNUk0TTE1YmtGZllxNWxDcmRhb3czZi9IUC9yUE5VN3FsTExpNXgvZnpuYjVxY2wwbmcvRFhpblVWKzhqaW9tUHpsUDhGTFZHaElmMjU1bDdSUFFvVW9DZ0dKNW8iLCJtYWMiOiI5ZWNhYTUyYmVjNmE0Yzc0M2I2MTFlYzM0Y2QyZGYxNWYzZjczNDBkNWFmNDljMDZhZjdjNGFhMTk1ZjY4ZTFiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1373645726\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1626619027 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>192.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1626619027\", {\"maxDepth\":0})</script>\n"}}