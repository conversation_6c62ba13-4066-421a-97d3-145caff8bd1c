{"__meta": {"id": "Xf69d2fcae31a3e67810d3df40dff380f", "datetime": "2025-07-14 22:40:44", "utime": **********.06014, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532843.662604, "end": **********.060155, "duration": 0.3975508213043213, "duration_str": "398ms", "measures": [{"label": "Booting", "start": 1752532843.662604, "relative_start": 0, "end": **********.019825, "relative_end": **********.019825, "duration": 0.3572208881378174, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.019833, "relative_start": 0.35722899436950684, "end": **********.060156, "relative_end": 1.1920928955078125e-06, "duration": 0.04032301902770996, "duration_str": "40.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44844192, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00246, "accumulated_duration_str": "2.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.048485, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.829}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0534558, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 76.829, "width_percent": 23.171}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"21\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 336.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1805942986 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1805942986\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1122034421 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1122034421\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1160197034 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNCWDY1NVNPL2ZNSWdGLy9jc2VNZ0E9PSIsInZhbHVlIjoiTWhobHlMdVNOaHB0UnhDZFFIeW9zYy8wWWkxWENjWnpCQnRNRUZpSnlXalJDd0Q1WEtmUmxWZ2lINWFRb05vNlBlOFlnb2VvWGh5NExmZ1dEdE83Y3IwRmdFOWFnbU83QzhrLzF4ZEREdHB0WHhibGIrL3dHWmV1OVlYeWlaa3dlcUdyY1hEbkZ2eXNpczlFTlowZFRCdmVEbzM2Z0RsMFphaS9TN2FsUks2NTFkY2dkQ2hYdU9YTzZGeURwNXIxZ24zVk9FQTZqQ0ZkUHF1T25zNFh2d1pUNndIM0F3c3Z4QWdVaTdxbk5NOFk0V2Jpbm02RXNxOUNxc3JPNXNIajVDc3NtLzBpbXZycXV0OXlJN2kyd25VU083OGcwUnNXL0hzWmY5WE5Ddk1zakJ6SlYvV01aRDBLOFFiV3VIWXZRa3dYbGgwdnRBcnhzbTRqMDh0elV2S1IrdzlCZWRVSTNSTzZsYU9RdzF0N2lielkyT1M2dFROT25MUkUvQXVWZ254QmZ4NmVIeExLd0RnRThQYzc1SFVRV1VYVnl3SHlOYTlvSndUUm5WZ0hmYnluWWZJQUpQbWE0VlY4OGhWUXFHVEVWbGdVNGtHM1VaSGhWbUtwckNrTjFsR3ZuUlBCQXg2YWp5WHprbHlvY05zbTh4dFBYTXVObk43bVdMWXYiLCJtYWMiOiJlODc0NWEwYTU4MjllYTg0NTgzZjU2OWY0NmJkMDFlNDQ1Y2RhMDU0OGNmNDUyMmY5ZmJhNWFiNzNmZDU4NDdhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhsWW90YXNKMzF0L29DZXlKeVp2MXc9PSIsInZhbHVlIjoiVkd4azBEenNyQSszZ2xaZWVna3I4YkJrNEJOM2tTNDVnTVpaVEd4QmM1dyt2cFBIWW5YbFdPclVyUHNFU1NJUlJKYS90eEE3ak5QN3QycW1HWWlMekhPNGtWVlIxblArZkttMkJudnJDTG9ENlNMSXJuR2twZm4yS1Y0SXdqQ1JTZHNSRVJsOXgydERDNk5HeEdzUmJkc1NDdVJ3MkRyVVRGbFlKOC95WmdkVTcyY0dxMEdOYmZyc1F0ZUpVcm0rR09SWWZFOWtGcFVxSk56dklqZitHTXFONUdidkpOYzhXNk9RSmN5YzhDWGJWM2xCWVgvQ0lVVlJTSER2dzNJOXB4VEwwRCtuOGVQYWwzcmNwMkN6UHR6YVBuQ2NkY2d6ZDRRWWxaREt1NXd1SDZLZ2ZCcmtybE1Lc2FWMUlyYlpEMzg4TWJxWngxSmJsanlqME42dGdwQ0RSWmxXd0ZNa3VwT1BySDY4ajRScFppKzlwNEN1R3hMbGUyNXlCcE9Ib2ZnVTVCNmNsY0xObFJFc2w5YzdnV1Z0MHJTdFVONTBpbVFQUXhpKy83NGU0WDE4bEhPNEdKSERxSjUyeC9aMEpaQnQwQUF1dHMzUENSYlM2UWdPTWZLTnNNVnU1ZWZLMVJpbzh3TWxMUS8xVHd1MWMrTkFxRDBwSTcrSDZqUUkiLCJtYWMiOiI1Yzg5OGZlNDQyZDMxZTUwYmY4NGU1YjNiNmMwYjMxNzBjNjRlOTczYTcwNWMwZTJlMzY2YzA5OWIyYTllMTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1160197034\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-478892792 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-478892792\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1543726556 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJ6b1RqbUl1RTJ4dGdkTXlaSHFOTnc9PSIsInZhbHVlIjoiUkFvQ0IxMDMvOGhiTWJqeGNzc0xqNDhCc29NWm9mNEtoNUs3azcxVU1JZ21QTmRsZWtCdmtJRXVONVFob0xFdEhPNXlhbFVNTVMyUUVKL0xCbzdBOEtvbHlXQkhxMEp3WXp2K204TE1wd2NWbGI3WUZpaVJlemdPTHd0blovOFNtYUNXOVpaRTNFeGxDT2dGWGV3U05nUGttZDhVNkZLWFM3bitMQTBObEpxbEJOcFJ6V2tQRVQyYUhScDJqTU1zTzdyRXNMS0p6dkc1Y1ZBVTc5VXhNZVh4OGdzWHZ5Yk9kNFU2a2MyOXUwbGdXOXg2TUFFVkhvcVJCemRhWVNqZFpKSFBDVzd4MVo4bnZ6dHpJY2lSK2dneFh5WGZpZHR0WVRVNUw0aTRycmtHT0pWdlZrRWRkRndvTWd1SERRNTlzVnI5eFR2SGJQY3hxemM0cU5PQzBCc0NCU0h4bm1tekZzVjduL1NHbENHZjltOTIvWWZXL0dneW13d0k4QXBqV1JZRGxNVWNpTFZMaE4vckdHak9vUVp3UjJmQ0EzdlZUSVdWUFltcW5vbXlGdlg3TCtobDk4Rnd3U1U0Y2JBSzhqTFlyck1pMFdiVE1LSkt3UU03U2lDUFMxdFNiRkZnVFpyTFhxc09QdDQ3aGtVL2toajdvbDNuZzdGRGlCZTkiLCJtYWMiOiIyYWZiOGZmMzkzMTQ0NzU4YTJhNjExNjczNWVmYTQxY2MzMDY1NWMwMGJlNTEzYzE3YjAzOTFmODIzNTk5ZjMyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZZb0ZWUCt0Y1NPWW1qS3JkOWJHMFE9PSIsInZhbHVlIjoiRzgycDFXVnR4VjN1bmEzNGNPQS8yNDc0dTRWYXBmUVVNZ09NdmJ0MDN4ck94dlphNEZZMG5INGNUZkdBa3hTK2VDd2NjbStXd2p3TSt5VXhIRHBMblNMNTZtWERZQ0RSdVI5VHN3NkZEcDVZdlhiaHpVR0NyUVViQThNNmEvanJqTUJDdjFLVi92Q3RjNWlrNEVhVlVETytxODhUTEExOHIyano1RTlCdGZ4OEsyekV3Umg3dFpaRDgrVUpYZm9jMlQvUWd4UEdRSklwaWxkVjU1OGVpOEdMTEJRRDh3RG42OWc3NUt4cHE3MHlWRnRFT2lSRGd1M21ZbDhzMmhRMFY3RjU1VENNengxYWtCSXBXdVJoWDgrKzk4V3RjK2U2cyttRmVqMnRwQUFtUGtNMU05bk5FNmRyTzVwc3RZVVBKdHY5djRUajdxTnM4SkI0SkJ0UjQ1MmZZUlQ2ZmViTjdCRWV0NXR1ck1QSlY2SHUzUkR3bGpHSk9SWGtrS2hFeVNlWmFPcVJVU1oyVmlxODY0aW1uSnZIYk9LdGE2VWJWaTBGaGJhNHhyM1hXZm1XaU44UGR3V0RxdEt6TkhCWTZCdjBrZlNEQ0Vrbys4ZWhQZXZYaHlWR1I5a2FxMUNJeXNJRjR4eWZqMGtaQjZjYjJ1MjZobTlGZWdCd0FwWVgiLCJtYWMiOiIxYmY1MzZlYWI4NjRjY2M0MmEyZjQ1ZTlmNzgxODc4NzBmMWM3M2ZiYjEzZTQzOWU3N2Q2ZGI1NmM5ZGY5NmVhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJ6b1RqbUl1RTJ4dGdkTXlaSHFOTnc9PSIsInZhbHVlIjoiUkFvQ0IxMDMvOGhiTWJqeGNzc0xqNDhCc29NWm9mNEtoNUs3azcxVU1JZ21QTmRsZWtCdmtJRXVONVFob0xFdEhPNXlhbFVNTVMyUUVKL0xCbzdBOEtvbHlXQkhxMEp3WXp2K204TE1wd2NWbGI3WUZpaVJlemdPTHd0blovOFNtYUNXOVpaRTNFeGxDT2dGWGV3U05nUGttZDhVNkZLWFM3bitMQTBObEpxbEJOcFJ6V2tQRVQyYUhScDJqTU1zTzdyRXNMS0p6dkc1Y1ZBVTc5VXhNZVh4OGdzWHZ5Yk9kNFU2a2MyOXUwbGdXOXg2TUFFVkhvcVJCemRhWVNqZFpKSFBDVzd4MVo4bnZ6dHpJY2lSK2dneFh5WGZpZHR0WVRVNUw0aTRycmtHT0pWdlZrRWRkRndvTWd1SERRNTlzVnI5eFR2SGJQY3hxemM0cU5PQzBCc0NCU0h4bm1tekZzVjduL1NHbENHZjltOTIvWWZXL0dneW13d0k4QXBqV1JZRGxNVWNpTFZMaE4vckdHak9vUVp3UjJmQ0EzdlZUSVdWUFltcW5vbXlGdlg3TCtobDk4Rnd3U1U0Y2JBSzhqTFlyck1pMFdiVE1LSkt3UU03U2lDUFMxdFNiRkZnVFpyTFhxc09QdDQ3aGtVL2toajdvbDNuZzdGRGlCZTkiLCJtYWMiOiIyYWZiOGZmMzkzMTQ0NzU4YTJhNjExNjczNWVmYTQxY2MzMDY1NWMwMGJlNTEzYzE3YjAzOTFmODIzNTk5ZjMyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZZb0ZWUCt0Y1NPWW1qS3JkOWJHMFE9PSIsInZhbHVlIjoiRzgycDFXVnR4VjN1bmEzNGNPQS8yNDc0dTRWYXBmUVVNZ09NdmJ0MDN4ck94dlphNEZZMG5INGNUZkdBa3hTK2VDd2NjbStXd2p3TSt5VXhIRHBMblNMNTZtWERZQ0RSdVI5VHN3NkZEcDVZdlhiaHpVR0NyUVViQThNNmEvanJqTUJDdjFLVi92Q3RjNWlrNEVhVlVETytxODhUTEExOHIyano1RTlCdGZ4OEsyekV3Umg3dFpaRDgrVUpYZm9jMlQvUWd4UEdRSklwaWxkVjU1OGVpOEdMTEJRRDh3RG42OWc3NUt4cHE3MHlWRnRFT2lSRGd1M21ZbDhzMmhRMFY3RjU1VENNengxYWtCSXBXdVJoWDgrKzk4V3RjK2U2cyttRmVqMnRwQUFtUGtNMU05bk5FNmRyTzVwc3RZVVBKdHY5djRUajdxTnM4SkI0SkJ0UjQ1MmZZUlQ2ZmViTjdCRWV0NXR1ck1QSlY2SHUzUkR3bGpHSk9SWGtrS2hFeVNlWmFPcVJVU1oyVmlxODY0aW1uSnZIYk9LdGE2VWJWaTBGaGJhNHhyM1hXZm1XaU44UGR3V0RxdEt6TkhCWTZCdjBrZlNEQ0Vrbys4ZWhQZXZYaHlWR1I5a2FxMUNJeXNJRjR4eWZqMGtaQjZjYjJ1MjZobTlGZWdCd0FwWVgiLCJtYWMiOiIxYmY1MzZlYWI4NjRjY2M0MmEyZjQ1ZTlmNzgxODc4NzBmMWM3M2ZiYjEzZTQzOWU3N2Q2ZGI1NmM5ZGY5NmVhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543726556\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1175850757 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>336.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175850757\", {\"maxDepth\":0})</script>\n"}}