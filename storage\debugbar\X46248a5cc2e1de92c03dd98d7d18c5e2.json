{"__meta": {"id": "X46248a5cc2e1de92c03dd98d7d18c5e2", "datetime": "2025-07-14 22:31:12", "utime": **********.518747, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.022155, "end": **********.51877, "duration": 0.496614933013916, "duration_str": "497ms", "measures": [{"label": "Booting", "start": **********.022155, "relative_start": 0, "end": **********.443836, "relative_end": **********.443836, "duration": 0.42168092727661133, "duration_str": "422ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.443851, "relative_start": 0.4216959476470947, "end": **********.518773, "relative_end": 3.0994415283203125e-06, "duration": 0.07492208480834961, "duration_str": "74.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46003680, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00502, "accumulated_duration_str": "5.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.478951, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.538}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.497621, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.538, "width_percent": 17.928}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.507244, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.466, "width_percent": 16.534}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-595122615 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532264441%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpEWGFDS25wT2tZM2YvMFAyV0JPelE9PSIsInZhbHVlIjoiQytJdVh0QW5KSkhxdUlxUUhtT3Z2RlhlQWg3d2RETGhqY2VpQ0xscERSOWgzNFQvNXgxNjJkTEQ4aU15b3hvQkJsZnpRTVZ3cXBXWHJ2cVhadXhZeHkrMTVXM2REbWh6U2YwRmp2WW5KQnZKTDRrblowdmp0WFBmQXQ2Y1VVTXE2ZUIyYnFVOTFnMXFvc1dOZXkraWh2T0tuWDFSbFBMUnR4d1pDOEx0ek1wRm9BSW1aSkZ6c2ljUkwycE9PUER1Z0l5VnZzbGtXRDZmdm8rc1Y0Zk5TZDZ5VlFBQlcrcEU3bFRiZkdtbmFGbXFyYTF4YWpLSWlvU2p2cHk4cWhMWUEvQ1pNTUYyVFp1RWpBOHJ6Yy9PSTQ3UkZBZkpIaitRQmVNci9GUkZkQ0svSkVOQ1I3WkdScnJMemptYWpFTE1YT05pVDVSQmNna0xCa0I1U1VJL3A2aWw1aEkzVTJneDBhSWNmV29oTXdnK09PMGFDN1hCQlY1UlJBQk56SjJZb1E5bjl5SnA3cFA5M3Y5aDlIYnEzV0ZMOHhkRmZYUDYrcmUyZG9NYjgrbFpoTmZSd2JSRlQrTUd2Q1VTMVVXTzhwYktINnpYaUx1aXJ0eGxrVHR4ck8wOG9UQyt3Vm1KUE5Xdnl3L1AySUpIVkU1WFdSbHhQTnBWN2t3WlJ3RXEiLCJtYWMiOiIzNmJiM2NkYzQzYjMzMmJjZmQzZDU0ZjZmZmMwYTYxZTRjY2M4N2MyZWE0MzcyMzdhMjY5Zjc5OTdmN2JmZWU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlQTW9hUGprY2ZQZDhPdG5PdE14a1E9PSIsInZhbHVlIjoiSFpWNUk5Y0lEcXU5MERYaVdUcVlyZTI1bklhUDZCWTNoWmdtenNQbUdjdW50R1N2S21Ha2VocFBhbzlqVG5pMjhmQVlReG55Zk1ETlAwUzRMNmg2NzgwVmxLQmZvaTlacisyOFNhOHVaSmFjSk5NSWV1TnhjdzkwaldYWlBMUldDbU04em5ieU5xeDlPdUFwQXhScUhTRWxwOVdXTit4dHRiSFluTTIvdWFHeDZhMFlLRkd3UVdVTDNMNDQ4Z0NuUTV0Vm9WODJ1a3BLOGNxSkNRcUdFdTJGd1FGNCtkTmwvOEZkSjl3dy9DN3lnR3B3K09IRjFUeEpZYTJoSGtiWnJOT0hxeG4zNkVGeVJxbExMbm5uVkcxZjQ3MFFXSjFFbkQwSXdhQ0dCZGFhL3dXaHdUZ1NjVXE4R3R1QndXTkJ3bTEzRkVWd2YyS1NxNGlWZFlET25RVUdVdmZGL0tnL2N2THk3dzhLS3owRjUwWGk3eXR1RGF4d2VoeWR5SzN4V1hiYUpaWFpHUmQ3SXB6bTdVSFhKTnhyMW9qZ0hzQjIyK243bkRUN3BUbzRlYVJkbmxiT09uSWJNRjRtVU1KdGQ3VUZlLzlTaGQwQ2hkWDdOY3FZSXBGMHhiQVlpYk1adWd4d3h2OFJPTW42MXlGdHZoQ2JVWkkzdXo3c01UckciLCJtYWMiOiJjYzE2YzAwN2IzMzJmOTU1N2YzMGRhMzQ0MDVmY2JkYjgwODE2YzlmNzY0ZDJjZWVjMGNhMjEyYmJiNjc4ZjRmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595122615\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1067777826 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9tDxXjbbhjxiDJbPcLBO2bj3xslZ4VoGjLB0sApN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067777826\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1254466589 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdSUmVQR25aSUk2S29EWHRtNjcvRkE9PSIsInZhbHVlIjoiMjBGT1ZPZ1ZYWGtpR2NMR1ppUUhoMjJDRVBsVDVWWEt5bUs4dS9nSEMvT2tjQmYvbFJFRTZsd3NCdkozOXRwOFhTeGsvcmFWRENJOGVuS21wNTJIQTNWbks1WktlVHlkUW1ldmh2MEc4dkJFTGNZMWNTcHIyQzVZanBGY3ExTUY4TXNtaEJLbU9zRzhkbFBKOXBpT2poLzFpbm9YdzJ1MW4xdEdZeWxzQWh4eVd0RlBxMlR6aEVId2wrZTRWM1dzbUhWVzBNNlNkUXc4cHZYaGRsTnJpV3B5dFUwd1JIWm5XYXJLN3BjSmRUeVBUMHpEbklHbFlnRWV3eUtZbDdmYjZUMnZmNVRQN2NyQTI5cU5HWmMreUtYMEY3a1JhT1ZEeTN0ZnZ2QVE2YkN6MWhTL1RkajdGaGsvczVaei9PemZETWlRN3NLWGZUNmJLYzZDZk5EaEh0RzYxR0dEK0VFaHNjWFE3THAxRlVNWElraWVTNFA0aFM5NHFWMTk1cEl5ek1ML08vWGk0dG13K01vTGRuQUVEQ1huZ3U2VXdKWlhDM3A4cEVURk9KZkRTYUEwUkpBam4zU2hOdU1JNkdlOTZTd3JOSzJTVTA3R1g5ZjBoaHFwaGZVbXJtQm1UMVpZb0xjemxzYTZZdVByY21UZFNqK2xDUTJtc2x4ZHBETDMiLCJtYWMiOiJkNGU4ODBmZDk1NGRlN2QzNDY2NDJjNjgyZWE2MDk5ZDkzMWY2YzM4N2YzN2ZkY2EwNGMxMjkxNmIyMGEzNmU4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpmMGFYbi92bHppRDJKREdINE5mQVE9PSIsInZhbHVlIjoiYVltZjlkOXpZOXFTMzVGMmJiQmdYNEFSVy9STTlkKzhEUnBkZWF1ZUY2V3Z4L29nczlKWFA1a3lpSFU0V0NuektqS2prNHJySnRhNW9SWXJlQ1NHeG9HR3lEMnpxU0I4N1dFSUpUTTFhbTlPWkVNUlBTdDJ5d2pzWm1DNmljOGpZazdodmFhc0xoSks2L3g0cVA1RkFnRVdndkgxeFNJVVhDeEZMeHZuSVoyR2p3bFpBSjRMR0dWTm9XUE1IQ1BSZDkzVWF1bnVRUXZ3ZFNQQzRlMUpRMXlXZ25yRDQwVS9RcDRFWkVJRXJ0U3k4a3VLYmIwOEMxQUxkTHZ3cEU1UjVyd0xEQk9jNzZaZGgraG5HK1dTNG05N3NpeXhVYmJXMk9oT3dwTk1xZ2JrLzhmZENMeXVkVGo5TG5DeEkrVTVyWlNqNTVtWFE3bmU3SmI2bVJ6T2pkcjZOWXNjZHY2eUFseWxGRVl6NE5ZR2ZqYzI4dHRnci8yd1VWWHF0NlVvaXRPMHFjTTRoUHFFTGJkVkw5dVJqYUY3RXU0VHkyRzYzd0QyQXpTbWJJRW1oL2VmeDhnckVsWXhzb2RkWTlzODRNQ3VaTDd3ZmU4dWh4WFFsd1FORTltQ05pKzlSUy9iSHk0KzR6TSt6UXg1aVdLMklxdjE3K2ZJKzNOUTZ4b0ciLCJtYWMiOiJmNTc0YmZmYjE0ZjkyOTJmNjgyMjQzNTcxOTMzZDFmZTBhNjM3OGM5NzY2YmRkMGFiNDQxNmIzM2QwOWIzM2YwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdSUmVQR25aSUk2S29EWHRtNjcvRkE9PSIsInZhbHVlIjoiMjBGT1ZPZ1ZYWGtpR2NMR1ppUUhoMjJDRVBsVDVWWEt5bUs4dS9nSEMvT2tjQmYvbFJFRTZsd3NCdkozOXRwOFhTeGsvcmFWRENJOGVuS21wNTJIQTNWbks1WktlVHlkUW1ldmh2MEc4dkJFTGNZMWNTcHIyQzVZanBGY3ExTUY4TXNtaEJLbU9zRzhkbFBKOXBpT2poLzFpbm9YdzJ1MW4xdEdZeWxzQWh4eVd0RlBxMlR6aEVId2wrZTRWM1dzbUhWVzBNNlNkUXc4cHZYaGRsTnJpV3B5dFUwd1JIWm5XYXJLN3BjSmRUeVBUMHpEbklHbFlnRWV3eUtZbDdmYjZUMnZmNVRQN2NyQTI5cU5HWmMreUtYMEY3a1JhT1ZEeTN0ZnZ2QVE2YkN6MWhTL1RkajdGaGsvczVaei9PemZETWlRN3NLWGZUNmJLYzZDZk5EaEh0RzYxR0dEK0VFaHNjWFE3THAxRlVNWElraWVTNFA0aFM5NHFWMTk1cEl5ek1ML08vWGk0dG13K01vTGRuQUVEQ1huZ3U2VXdKWlhDM3A4cEVURk9KZkRTYUEwUkpBam4zU2hOdU1JNkdlOTZTd3JOSzJTVTA3R1g5ZjBoaHFwaGZVbXJtQm1UMVpZb0xjemxzYTZZdVByY21UZFNqK2xDUTJtc2x4ZHBETDMiLCJtYWMiOiJkNGU4ODBmZDk1NGRlN2QzNDY2NDJjNjgyZWE2MDk5ZDkzMWY2YzM4N2YzN2ZkY2EwNGMxMjkxNmIyMGEzNmU4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpmMGFYbi92bHppRDJKREdINE5mQVE9PSIsInZhbHVlIjoiYVltZjlkOXpZOXFTMzVGMmJiQmdYNEFSVy9STTlkKzhEUnBkZWF1ZUY2V3Z4L29nczlKWFA1a3lpSFU0V0NuektqS2prNHJySnRhNW9SWXJlQ1NHeG9HR3lEMnpxU0I4N1dFSUpUTTFhbTlPWkVNUlBTdDJ5d2pzWm1DNmljOGpZazdodmFhc0xoSks2L3g0cVA1RkFnRVdndkgxeFNJVVhDeEZMeHZuSVoyR2p3bFpBSjRMR0dWTm9XUE1IQ1BSZDkzVWF1bnVRUXZ3ZFNQQzRlMUpRMXlXZ25yRDQwVS9RcDRFWkVJRXJ0U3k4a3VLYmIwOEMxQUxkTHZ3cEU1UjVyd0xEQk9jNzZaZGgraG5HK1dTNG05N3NpeXhVYmJXMk9oT3dwTk1xZ2JrLzhmZENMeXVkVGo5TG5DeEkrVTVyWlNqNTVtWFE3bmU3SmI2bVJ6T2pkcjZOWXNjZHY2eUFseWxGRVl6NE5ZR2ZqYzI4dHRnci8yd1VWWHF0NlVvaXRPMHFjTTRoUHFFTGJkVkw5dVJqYUY3RXU0VHkyRzYzd0QyQXpTbWJJRW1oL2VmeDhnckVsWXhzb2RkWTlzODRNQ3VaTDd3ZmU4dWh4WFFsd1FORTltQ05pKzlSUy9iSHk0KzR6TSt6UXg1aVdLMklxdjE3K2ZJKzNOUTZ4b0ciLCJtYWMiOiJmNTc0YmZmYjE0ZjkyOTJmNjgyMjQzNTcxOTMzZDFmZTBhNjM3OGM5NzY2YmRkMGFiNDQxNmIzM2QwOWIzM2YwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1254466589\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}