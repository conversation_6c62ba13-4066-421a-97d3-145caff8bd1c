{"__meta": {"id": "X8a0f48c40f8117b6d834ca054cf87ba2", "datetime": "2025-07-14 22:30:57", "utime": **********.917747, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.502341, "end": **********.917761, "duration": 0.****************, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.502341, "relative_start": 0, "end": **********.848918, "relative_end": **********.848918, "duration": 0.****************, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.848927, "relative_start": 0.****************, "end": **********.917763, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "68.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00393, "accumulated_duration_str": "3.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8803399, "duration": 0.00219, "duration_str": "2.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.725}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.891031, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.725, "width_percent": 23.664}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9032092, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 79.389, "width_percent": 20.611}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532246434%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Iis2RWhxdVRZckw0QXFlb3dGSUp4V1E9PSIsInZhbHVlIjoiM0MyRlBqKzQ4d0hFUVVTdHdOMUdvcnZkcmZic00zaCs3OTFsSGpGdDlOcTVVNUlRcVRZWGpyNXU2UURKOWdiL2pyV0djN1pLQmgyWnEwSys3S2NTN0N2eHlJZ2hKbS9VaFR0b2gzSkJNZUl0cWFCMGVUbmw0SDh0N1VQVVI2WDlpNWJReEptbnZGUkY0Tm5kVDg2K0Y1WDFoVjFJMHc2MUptQkYyKzdPWEtVNnFGSDNCcUhBK2poamY3VkVQOStxSUYyd3Vhd3cydnZCWSt3dVZ1MnkyZXVzNSttVzYvRytSUml5MTRCR1VlVU14eHYrWFJXRVI5Mzh2RWRmMjVsNFpFSDA2c3hiY3B6dGlKY0VIdzZTMWtJeXZkZVBUTDUwYXhsVzlUSCttVFI5Q0ZPa0RsUmtabjlrb1JkUDJYK0FNQ1pZdmdleFk2WUZNMFVuSnJOQzlKemd5QU9GYWFVdGNOT0ROakRzZ3E3N2F4TUJLVEJ4YUpkV1hmWkZYT1NkNkNJcW9aNk03bnhuRE1rdnBkZ05QaUFXUmxtbFRmYzJJOXVwT2docThDMU9Za0VRcWlFa0lzRnJyaVlnR1h3aVZlOHZkQmpNYUhVYm4xYlVQOUNTODlkc3BmQnIxdmN1R05Jd0x4K1RZRUtSQklEWk9WTHYxMEV5cUlUK2hXMVYiLCJtYWMiOiJiYjZjYWUzNmQxYzMzMDhhNmFiNjg5ZWUzYTBhYWIyNDNmYmVmNzE3NTg2N2E0MDlkYjg5N2FhY2MzOTVkM2QxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjEwWFlaaldWbWFXVTlrZFdyZXloT1E9PSIsInZhbHVlIjoiNGM1aUprMDJMZzRCZXpmWmtYY1UyMzNDN21FT3RoSlBuWlZ1TjRBUVVhSmp5MW5GZ1hleUFGcFJtcjMvb2hEMlZjT0dnaEp5bzluTmZVVHBzaEtuQ1NsUjBabW9kRndsOEtmYmZTcmd1UDV1ZE81Yk9leHJZK0FwQkxSNDJSMWp2ZWFpNjZYd2FFRG41a2Z0TWZnUU1YYUYxT2xhYWVrK0pidGhNMGphNkdPbGVsVmVKTWRtZW05akxGdUtabG5YanI5RUhFNjFCMW9OdlcwdE4xcFNzUUNDdHl5YWVZZjQ5Nll5QnlyeXFUNCtuc3MyR3Z3TGVveW85VTB0QTJjNmNrUTdqVk1DandidVNIaTRlbnU0OWtpbkFnUkdvdXc2NGhXMDR4eGdVVU80SlRXZjc0MGk5MXI1M1hxYU82OVd5bld2a0hlQTIzOE9jUkNuWVFhUTJpTk80ZzFGUGxIV2VsYVppM2QwNFBkWmxEdDkraVhOYUpydTVueTNnMnFZUDdpcW81VzVkUlh6UDMxZ0ljR2dRU1ErQzczWXVrOGR4bWRUb2xmbXBzaFNwbjYxaFFPTE55cW03ZUdoN2FrTFZMamtKdVdkUzE1MXg1bXltU0pvL29LMFhtVXhZREo3VzN6anlzZUZxVlUwbElLcHJxcS81d2NvQWE2bEJiQkUiLCJtYWMiOiJlOWIzZGM2ZDFmYjdiMjYxMDEyZjMxOTlmYTE3ZjVmMTc0NmM1ZGJlNjU1ZGIyMjA1NzNmNjFhZjhhMDJmNDczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-123544452 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-123544452\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:30:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9OWWxWaHliblFwbWwrY3lyTmxyMFE9PSIsInZhbHVlIjoia3NiSlp1bUdCUmk3T2t1M1IvckVSOUNzem9ieCtuSzZaL1NCVHlnZlZiTHhIVkp0OEtzYklTeXdiWUE3bkRsN2FlNjNYcklaRFRGcWJ5T3RvaTlpZlhwb0Y2TWlRR3QzWXN6dXVVanBYNmxyekFpbFpSTThRaUF4ZjA0TmxQSGlFVUVjVEFDcUlvbG5DWVpCcGpwTWNoSUNoVGF0MXlrQ1liUkZEMUhVeUgxSTY0d3dkMVNXSDd0OWRJVjl3YkRxUXJLK0lQVUhpM3NuQmlrNk5EUC8rVE45TEszNkFGdDhDYkdoZzZUbTN0NG8wZHpZbjdZNTI3QnBweHhhczI4RGNxbHR4MGdSMzE2NXlMV0RyRzhNVVhrY01CNmpsNEgxeGwxNEVFYXJiTWVvR091b3owQ0o0alNobEVXTi94OTROVWJta2Vnc1hNUm9ER3UvWWVxdFh5VS9KN29yN1laSDVzVWQzZzRleDd4YjQ4RDJDbTlETmxLbEErUXB5cDBTLzRLSVFsbll2QmpUU2RVWG45RE9sUkFmY2p0TXl6TGF0YlcxZ3hJUEdmN0NrR3VjZDlBVWdvQjVGVEMxR1lVUjQ4VzZHa2NSQkZrU25GQi9XTFgzcThSa2NYME9Qak9zRjlybXdPZ0RzclVQNnFvZHMzQVVWSlZkRjU2aWo5S2giLCJtYWMiOiIyMjNjYTVkYWUwYTc4ZDgwY2NiYjQwOTdkOWE2ZGExYzc5MjBkMjNiZWQzYTg5Yjk4YmE5YjkyOGE5OGVlN2Q1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNCbytnU00yWW1WN2F4RWw0R3dGd1E9PSIsInZhbHVlIjoiU2R0WCsyMks0WGs0TE9ITlNkemNyejNhaVFkeXRhNzlqY0wyZk4wWTJXeGxXM25yN3Q4cWw3SmNQMHRRdnNyT0pqUkFpYVJ4MVRwOG5GTVEwN3YxTDNIZVRGVkpLdkxJRU1NN1hoZVBMUmhSZ1BrdUMrUkN3M2pqditQTnFyUDZpR1QxT3NyMXZXRGFLN2VGM2FaS2Y2S3hObXk5cm5ZZE9mTnZGQU13ZHlBeU9ZNVQySDJjNXRwRG8zWGRNUWVyamF2T0p5MEZmaWY4d3RzTFdockttdHBJdE44dzJoM243bkt2a3NibnROZjczaXp3SnkyWVZXQmk4YnFJQUREUE82dGZpTTJ2R3kxbU1qYmVYc0pvWVVJY0NWM2lCZ3dHcUZqS0FvakFpTDYwVU1GeTFnU3BzOUJRTDVleEpVOFpWNzA5QkVseVZkbmRTNGNaQytFN096VVJsWnI2MTJrckVrL00rNUlIcW1IYzE1QkVxQnYzTFFGUXFhcnpEUDBOQ1VBNU00UnJHSTFHM2dLZDRYNE5DL1VwUjhCM3NZM1dZck9Ra21tTmJHUVNBSWltaUpvMTc3cGhhYVJ0QlFxWG5SNFc3RktuRFNvcTBIcGVaRm9VWUhkZS9TVW8vSmVDWEVMQjVNcVlBSXVadUtwNHRnZWFtNDBlTDI3RytMSUoiLCJtYWMiOiIyMjE4N2QxNzY4MDYzOWEyZTE1NjMzN2Y4OTg5NzIyODk2YzMyZDg3OTFkZjE1MDA2YzFiNDIwMGI2YTU3ODQzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9OWWxWaHliblFwbWwrY3lyTmxyMFE9PSIsInZhbHVlIjoia3NiSlp1bUdCUmk3T2t1M1IvckVSOUNzem9ieCtuSzZaL1NCVHlnZlZiTHhIVkp0OEtzYklTeXdiWUE3bkRsN2FlNjNYcklaRFRGcWJ5T3RvaTlpZlhwb0Y2TWlRR3QzWXN6dXVVanBYNmxyekFpbFpSTThRaUF4ZjA0TmxQSGlFVUVjVEFDcUlvbG5DWVpCcGpwTWNoSUNoVGF0MXlrQ1liUkZEMUhVeUgxSTY0d3dkMVNXSDd0OWRJVjl3YkRxUXJLK0lQVUhpM3NuQmlrNk5EUC8rVE45TEszNkFGdDhDYkdoZzZUbTN0NG8wZHpZbjdZNTI3QnBweHhhczI4RGNxbHR4MGdSMzE2NXlMV0RyRzhNVVhrY01CNmpsNEgxeGwxNEVFYXJiTWVvR091b3owQ0o0alNobEVXTi94OTROVWJta2Vnc1hNUm9ER3UvWWVxdFh5VS9KN29yN1laSDVzVWQzZzRleDd4YjQ4RDJDbTlETmxLbEErUXB5cDBTLzRLSVFsbll2QmpUU2RVWG45RE9sUkFmY2p0TXl6TGF0YlcxZ3hJUEdmN0NrR3VjZDlBVWdvQjVGVEMxR1lVUjQ4VzZHa2NSQkZrU25GQi9XTFgzcThSa2NYME9Qak9zRjlybXdPZ0RzclVQNnFvZHMzQVVWSlZkRjU2aWo5S2giLCJtYWMiOiIyMjNjYTVkYWUwYTc4ZDgwY2NiYjQwOTdkOWE2ZGExYzc5MjBkMjNiZWQzYTg5Yjk4YmE5YjkyOGE5OGVlN2Q1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNCbytnU00yWW1WN2F4RWw0R3dGd1E9PSIsInZhbHVlIjoiU2R0WCsyMks0WGs0TE9ITlNkemNyejNhaVFkeXRhNzlqY0wyZk4wWTJXeGxXM25yN3Q4cWw3SmNQMHRRdnNyT0pqUkFpYVJ4MVRwOG5GTVEwN3YxTDNIZVRGVkpLdkxJRU1NN1hoZVBMUmhSZ1BrdUMrUkN3M2pqditQTnFyUDZpR1QxT3NyMXZXRGFLN2VGM2FaS2Y2S3hObXk5cm5ZZE9mTnZGQU13ZHlBeU9ZNVQySDJjNXRwRG8zWGRNUWVyamF2T0p5MEZmaWY4d3RzTFdockttdHBJdE44dzJoM243bkt2a3NibnROZjczaXp3SnkyWVZXQmk4YnFJQUREUE82dGZpTTJ2R3kxbU1qYmVYc0pvWVVJY0NWM2lCZ3dHcUZqS0FvakFpTDYwVU1GeTFnU3BzOUJRTDVleEpVOFpWNzA5QkVseVZkbmRTNGNaQytFN096VVJsWnI2MTJrckVrL00rNUlIcW1IYzE1QkVxQnYzTFFGUXFhcnpEUDBOQ1VBNU00UnJHSTFHM2dLZDRYNE5DL1VwUjhCM3NZM1dZck9Ra21tTmJHUVNBSWltaUpvMTc3cGhhYVJ0QlFxWG5SNFc3RktuRFNvcTBIcGVaRm9VWUhkZS9TVW8vSmVDWEVMQjVNcVlBSXVadUtwNHRnZWFtNDBlTDI3RytMSUoiLCJtYWMiOiIyMjE4N2QxNzY4MDYzOWEyZTE1NjMzN2Y4OTg5NzIyODk2YzMyZDg3OTFkZjE1MDA2YzFiNDIwMGI2YTU3ODQzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}