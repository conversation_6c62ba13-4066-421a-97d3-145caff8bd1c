{"__meta": {"id": "X55c1280864a57f9ee5406b02ea35bf03", "datetime": "2025-07-14 22:40:36", "utime": 1752532836.018947, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.489773, "end": 1752532836.018966, "duration": 0.5291929244995117, "duration_str": "529ms", "measures": [{"label": "Booting", "start": **********.489773, "relative_start": 0, "end": **********.927811, "relative_end": **********.927811, "duration": 0.4380378723144531, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.927821, "relative_start": 0.4380478858947754, "end": 1752532836.018968, "relative_end": 2.1457672119140625e-06, "duration": 0.09114718437194824, "duration_str": "91.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48520376, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.005769999999999999, "accumulated_duration_str": "5.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9730868, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.218}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.987154, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.218, "width_percent": 15.078}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": 1752532836.002801, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.296, "width_percent": 12.652}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": 1752532836.005877, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.948, "width_percent": 10.052}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-632835920 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632835920\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1752532836.010476, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"12\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 192.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1248159235 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1248159235\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-782468033 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJFVm9OL1YzenppUTdQMzZSVVhpWWc9PSIsInZhbHVlIjoiV2hZYllXNm9NdkpzcWR1ZVpiM1hDcHpMR3ZNWEprVGVLYW81c3RLWk1NNkpLOEUrem12RFFCQ1Jaak51UGJnVVdCbUlEaStyNWpQTlJnWFVSSVloa2tIRU9jVnM0UzRuQzRvODk4Z3NtZHBiMEw3UHBnY2hPbkhyRDl1MlBTdHlKNk1rTWhwa3FaUTNra3dXbDVIN3Fkcm0wSEN1T0pHY3FTNkdUaUppVDV4VWRmMkdYc0x0VHdERXRGdkNaZUZOT1ppcFlod2RCR2JrM0cyRWNVUW1wakJDWWQzT1N4V1BTd1kwUGZBVldJblNoNkltNUg4MkswdUNQZ1NUV1A1dzFNTXBwdGtiS2d3T2cxb0JEMlZxUmJaTjMzOXo4NmlSZzRZcCswdlNIWGREbkhpMy9GTFNaNDBlcmt5a3pQajlwUitaSTNNbDRtQnh4TDVOdHdnN1llWC82T2gvWmJPNW9RTm1rSEY5NEQ5bmxpZ1BUUG5OZmlpL202Qm5HNlBtcjcrUWdyTmtUNFI4OHhvQVo0MHE5ZlIyNHlGNnNnVTMyVXRvNDBWemxxdW43MmEzTWdPYTBlTE1zYno2ZlhVdTBwV3FET2luTHp2WTNoY1Z6MmVRQ2Z3WktuR1lISUQ3RkhYcjM5QzJkOVpaMW1hM3ZjakNSZHIyUWR5TGZZWHEiLCJtYWMiOiIyNGZlZjc5MDBlN2RlNjY2ZWYwNDY3YjBlZDcwYTk0ZGYxMDVhMDk4MzE3ZDYwN2EzZDg3ZTIxYmY0M2QxZjMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9KZ3NsUHBQZDluWlBlMUU4ZEt3b0E9PSIsInZhbHVlIjoiYmlha2FKUUdqaS9zQVk4V3hWaWdCL1oyRlFkenV5djFGOG8rZSs1WjlVWWFqYXJ2SWhQM1pXdXFHcXd2TWdiOHhlWHZtK1BMRkQvR1RoMEpLU1ZwT0ZPWlAzcGc1THhFN2dMQ2laNitCM0x2R094U29SM29mbWFnSjJ0Qno4c3lic1l1Y21RTFFWZjFpOFBRR3V3M1JuRXBwcTNJRUZOWHRPbzFHYjR0WFJXQzRlUUxwcGZETnA0c1JTNk9vNXpVTndGWUVWb2RSaWFDNE9ZcXplNGo3MzR2cUNvdFNrT1cyUktJR2d1Y1dXRngrdWViWFVjNWNzQzVORXd1d1FIeEkzaUs1UjdBZ3lDSG4wM3FCbWNzamZ3YWFEdGt5aDJGMHRwcUVNSk1rb3F0dHM0dTFTdXB2MFVmQ0loeEI5d0lCREt6Z1JJNVlmV1NQTDF1czJvVEs5UVovcXBYbWF2c053dk50c3J3dDY3bVhldzJpZndFai9ub3FEQTlpWWxhR2pqZmJXRFVNVFN0eGVCSGw2YVJWZ2dKaWM0SnRmeE5heUdLZys2a0F0eDBQWnRPNTNrRytUaEJUZE1XekVXK2hhQWVjV1BLRVdjZ09UaVVhSGFJMEFMeCtvN2hrekxCVmIydXVWRnEwOFlkVTZaL1RSMFRyaW9mRmNOZzRtNVEiLCJtYWMiOiI1OTA0NGZiMWI2OTE0OGI1YTgzNzg5MmU1ZDIxNGIzNTFiYzM4OWFhMWM2NjJlNTZhMGM1OWM0OTYzMmUyZmVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-782468033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1360050150 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1360050150\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1130118662 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IllVZHV0Wm1zUVp5OXFBNWVSWnVMK2c9PSIsInZhbHVlIjoiL1Bpc01JLzdPSytJL0JIRmU0RmtpUkZDNVJsQ084TlVBWFppNG82d29BS0RJY0RrY1hseUNTYTRlNkpUTXRodTNXTW13VU1MOGUrTUpWeG5vVFowYkpvVHR0Q3M2RE9Jcngwa2l5OGRDdHhIWm00V0E0TGdxbHNueG5TN3hFTUZaR1djYS9PM1A1WHIwUFJxM1dtbjQ1eDJVR1F1QWRYSkEyZUFjRzhxY29qQkt0bWZBTmIzNk9JRnRhTnNJRWNWTXI4VmI2bzRidTg2TXpqQXFvZFBWK2M4MTRxUkNWUHkwRUtOTWk5SUtsNGt1SDVjZFpoK2FVbHlzdHY5aW1UbGg0Rm1aRG5VWnF2eHdHaXhWMEs2Y2FVWUNFWDZ2ek9OclF2eU8xd0ZzdE5sQnUxVGNYRnkxSGY4akNxV3NFcnUybit3WHJXUGpadUZ5RWJvM2JHYTNMMHlJckhYaUJOSTFEL1hldUR0dGU3YVpKeVZGSVFmeG5acnZSUFl1SC9Hdmt5YWlFeWpUaXlxa3FveGxwRFp1MXJNT0l3ZVFWZWtSaEpuQzJmTVNKZ0I2b2VoV3Q5aGF2SjVMZlQyNXVHaGFUQ2xCZm5CRVZSSjFlR08vVUhVTUtWVGEyWC85anVPMkR4eTlTQzdZYklWVWlXSW93UmkyRnVlbE5GMXorU0UiLCJtYWMiOiJmN2MzNTY2ZGYxMGE5ZWMzODBjNzEyODg5YmQ1NzMzZGFiYjViZTY1YWNkODJlNGY0MTBhZGYzZGIyNzM3MDhiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZmVlVhdzZ5c1QwVVl5Z1V0UkxCOWc9PSIsInZhbHVlIjoibUtsTmpGVjJaZmxnN0ZWVzErV2RKN1NWenBma3hQczdqUGplVzB2emNxdUtydjJUUUhMZi9BSGkrZWEyQUFyVS9DRVErVVBDQ3k4U3lMYWdBRWlLZDBXbHRQYWRsWUxBK00xVHFBQnE2M0IwZzg4NGg2aTJOZkRYWWNLK3FReU01VUt5cEx2eThzR3pzYlRZZGlsVWhZek0yUnh1NUtOazR6WXZ1Mlhqc3cxYU1kUE80UXZTOG9OWDYwc3VnbUJJaHFXaVB0MGtnZXlhMUhIM2VrZDU1YUNRYXlRSU45RWRaeHBMY1ZpU3J2UHora3JlOHcvbFJlWWZzQ3BqQ2tnUlloelBVTGVEYnYvMSs1dU5JdVJkb3pqcGp4UURhTWVidkVLRzBHODNYZ3VGbExaNzd5aC9mRHM2cjJxa1NHb1pVc01mT1VqSVVUTmxwd1NkZVJFVEpzVFR0VUJuMlZhYndPQ2JORUJISWZHN0xQUldVbW1wemZmdEhCMnkydFdIWEI1S1E5WXBHT0tLc2Eya0pUV3kyVFJLeE5tbjlpRVZjZ0U0UWs4TWZxOW91a1dCbktCUHRGaTJ2ZHRJRGxjSTRGbGlXWSs1VWtpdVl2RWJSK0lraXgxWFBESzhvZnRpZE4yWXFNM0JOK25tcitwc2xTcDZrVHRDa0I2YXAyZlkiLCJtYWMiOiI0MGRlYzgxMzE4MGFlNDVhYjU4NjUxMDg5NWQ5ODk5MTJiMDQ4OTMzNjU0ODUzY2VjZGRjYWE1OTQyNjYzYmZiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IllVZHV0Wm1zUVp5OXFBNWVSWnVMK2c9PSIsInZhbHVlIjoiL1Bpc01JLzdPSytJL0JIRmU0RmtpUkZDNVJsQ084TlVBWFppNG82d29BS0RJY0RrY1hseUNTYTRlNkpUTXRodTNXTW13VU1MOGUrTUpWeG5vVFowYkpvVHR0Q3M2RE9Jcngwa2l5OGRDdHhIWm00V0E0TGdxbHNueG5TN3hFTUZaR1djYS9PM1A1WHIwUFJxM1dtbjQ1eDJVR1F1QWRYSkEyZUFjRzhxY29qQkt0bWZBTmIzNk9JRnRhTnNJRWNWTXI4VmI2bzRidTg2TXpqQXFvZFBWK2M4MTRxUkNWUHkwRUtOTWk5SUtsNGt1SDVjZFpoK2FVbHlzdHY5aW1UbGg0Rm1aRG5VWnF2eHdHaXhWMEs2Y2FVWUNFWDZ2ek9OclF2eU8xd0ZzdE5sQnUxVGNYRnkxSGY4akNxV3NFcnUybit3WHJXUGpadUZ5RWJvM2JHYTNMMHlJckhYaUJOSTFEL1hldUR0dGU3YVpKeVZGSVFmeG5acnZSUFl1SC9Hdmt5YWlFeWpUaXlxa3FveGxwRFp1MXJNT0l3ZVFWZWtSaEpuQzJmTVNKZ0I2b2VoV3Q5aGF2SjVMZlQyNXVHaGFUQ2xCZm5CRVZSSjFlR08vVUhVTUtWVGEyWC85anVPMkR4eTlTQzdZYklWVWlXSW93UmkyRnVlbE5GMXorU0UiLCJtYWMiOiJmN2MzNTY2ZGYxMGE5ZWMzODBjNzEyODg5YmQ1NzMzZGFiYjViZTY1YWNkODJlNGY0MTBhZGYzZGIyNzM3MDhiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZmVlVhdzZ5c1QwVVl5Z1V0UkxCOWc9PSIsInZhbHVlIjoibUtsTmpGVjJaZmxnN0ZWVzErV2RKN1NWenBma3hQczdqUGplVzB2emNxdUtydjJUUUhMZi9BSGkrZWEyQUFyVS9DRVErVVBDQ3k4U3lMYWdBRWlLZDBXbHRQYWRsWUxBK00xVHFBQnE2M0IwZzg4NGg2aTJOZkRYWWNLK3FReU01VUt5cEx2eThzR3pzYlRZZGlsVWhZek0yUnh1NUtOazR6WXZ1Mlhqc3cxYU1kUE80UXZTOG9OWDYwc3VnbUJJaHFXaVB0MGtnZXlhMUhIM2VrZDU1YUNRYXlRSU45RWRaeHBMY1ZpU3J2UHora3JlOHcvbFJlWWZzQ3BqQ2tnUlloelBVTGVEYnYvMSs1dU5JdVJkb3pqcGp4UURhTWVidkVLRzBHODNYZ3VGbExaNzd5aC9mRHM2cjJxa1NHb1pVc01mT1VqSVVUTmxwd1NkZVJFVEpzVFR0VUJuMlZhYndPQ2JORUJISWZHN0xQUldVbW1wemZmdEhCMnkydFdIWEI1S1E5WXBHT0tLc2Eya0pUV3kyVFJLeE5tbjlpRVZjZ0U0UWs4TWZxOW91a1dCbktCUHRGaTJ2ZHRJRGxjSTRGbGlXWSs1VWtpdVl2RWJSK0lraXgxWFBESzhvZnRpZE4yWXFNM0JOK25tcitwc2xTcDZrVHRDa0I2YXAyZlkiLCJtYWMiOiI0MGRlYzgxMzE4MGFlNDVhYjU4NjUxMDg5NWQ5ODk5MTJiMDQ4OTMzNjU0ODUzY2VjZGRjYWE1OTQyNjYzYmZiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130118662\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>192.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}