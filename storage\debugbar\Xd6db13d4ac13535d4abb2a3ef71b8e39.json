{"__meta": {"id": "Xd6db13d4ac13535d4abb2a3ef71b8e39", "datetime": "2025-07-14 22:41:28", "utime": **********.475248, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[22:41:28] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.468426, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.037583, "end": **********.475264, "duration": 0.4376809597015381, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.037583, "relative_start": 0, "end": **********.37844, "relative_end": **********.37844, "duration": 0.34085679054260254, "duration_str": "341ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.378449, "relative_start": 0.3408658504486084, "end": **********.475265, "relative_end": 9.5367431640625e-07, "duration": 0.0968160629272461, "duration_str": "96.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49274832, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024929999999999994, "accumulated_duration_str": "24.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4132829, "duration": 0.023059999999999997, "duration_str": "23.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.499}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4451969, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.499, "width_percent": 2.888}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4615939, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 95.387, "width_percent": 2.407}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.463845, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.794, "width_percent": 2.206}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251278150 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251278150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.467744, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1992462749 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1992462749\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-505276271 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505276271\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-863678029 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-863678029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1818181721 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2816 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InVVVEpCN25TWHpBQUYvNWdLeGdsdHc9PSIsInZhbHVlIjoiWHorVHpEUXVtMXg0R0xsM21TWStVRWdmNkp6aWlRL2FLcE9UWVNFTEtZbDJhdlRrN2RmeTRNaGZNQUk3THZEWGtHL0pEaXVPNGJ2T2RrMmtJU1ZEaFB4bmR2V29HR3lHMXhxenRmSkNWWThISGlUQThLTEZVYXRPNU8vRVhOb3BOZDRhVG5NS2M0TmlOVEg4YnVGWnhhekNpVXc0SldZQjVFV216T0kwbmZvQWpTZFUzV3VaQkg4NjAzYXRQakI2VllZMjhrUWJnYXFsTzFzZGdEUFBKdWdIVjEzMGd4cHk5cnkydWJsR0txeFAyQlU4YUxGeWhzRXo4U3dDTjVlQ1dqVm02WlUzYmdla2VKREc3cU5LQUtRd1lDRHJZSGFYZUZlNytqdnpUbndMdm9DSFVMQ2JKMzZ1ekRjMkVzc3R1Wi9ZMEQ3ZGlTekgvbzF1TjZEQ2E1M2xHZCt1cUplbk8wSHZrdUhrRElTMG9uUGhwQWtROHZwYUxFYXV3RGE4Y1pMVnh0QXpDWVdMZXVTbHlZemVMMFNBbG5zQndTNmM2TzZoS3N1MXZGU2RhM0hwb214bHNWaWJGeTYwTEx6NGgrQVB1VUEzdVFFeWlvU1NyMmJNbHdDdUVtVlY1ZE9TRkc0RXova3JvQTZhZmxuMnE4M0t3QXZnOHNUcFhRQ3MiLCJtYWMiOiI1MDU5MzYzZTMwZDc1MTYzZDZhZmExYTI0ZTg5M2I4YWI5ODZiYTUzZWE4MDRiMDY2ODUzMmQwN2Y0OTZiZGM5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNnb3dhY1krdDd1UDNYQ3kybWNkaWc9PSIsInZhbHVlIjoiTC9Qc1Y1aVIvWTdROGg3VXFkSWsxZG5BbGRrRElaZ0RwT0hrRHB1MWVtVHA0NmpqeldNcCsyYUh1S1BHYmI1UlRHd3luaytjV280YWYzOVI1OFpCclpSVmRYdTdvWlhHa1o5SkhDMDR0Z0pob2cvRzFnOS9pekJIMHFTZldaL29GTGFJWEg5T0xxdVVvVnAvMVdrWHdidERlWDhxMEw0UGpGMTBaRER2L0c1Y0ZLckloM091aVVPbDc2d21GVWQ2WkUwdEpvbEZLVStWQ0poRFVDbU01NDZzY1NMZEJMY0hYemNSaG5NNU9xYmo4TnM1dW1ROE9WU0Q3S28xdlczZlFlQ1pScXh5ekVPVS8zQzZzUEFXYmMwK0NOdDRpeklyR0QxbnhBbXFMWitzbWpvU2lCbVNjNU9rNUNHNkpOUlFMc2JoQkZKTEZJdVgwYlNXcEMrOUZpbEtlcGxneURQekxlR3ZCTjZMeithK0VaYmFCREVEd0RWQ21DdzlUVy9hYjk4dmlRV3NIZkNsb2lMUHE2OC82SzkxWWRGY2xqQTkyb3B5VnRyMk9qSU1QUkd0WXRQWm5XU3VSd1V5V2VqRWVwUG9FdG1YU2U3SXUwRllBWFRHb2RkS0pyeWdLV2w2cEZld2FwcjUxYmFyZWdackcvUzYvYVhSOW1DWmlvamEiLCJtYWMiOiJiNDI5M2NiM2E4YzRhZGEzYzQ4ZGM1N2EwMjQ2ZDA4MGZjZDY4ODM0Nzc5NTQ4NjM4ZTdlMzNmZjgzNzYzNzVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlovNEkwWURHbmRlaG5tRWVUUDVsUUE9PSIsInZhbHVlIjoia3U5SHhYR1FBME5QamU3YWpFaStZUG5FaXo5eVJ4STk0ekVMYk8rWllaMVhCRWNpRFo4SzN3cmFjRUtxemdvQ3llNWVWYU9wNUpoL3FzWWY5VVVwRzhQK3JndzliYjFTbGJjc2pHZUM4Wk1oSkhwcGxvSEJzQTF0ZFhkVlNmYkFPVUc0TWU1M1ZmYkcrUHErdXljcTFBVVJ5U09sYkExbVNtRjBIcUEzbDQvWEt4NGdleHhLV2gzMG93S2JXQ05JSy8zMkRmaTY2UDZQYzdhY21uWGFDeWZvUkErMXRJRENMQXBhNzQycE1ZaDE1eURwcHBVbmU4bmM5eXM5d08xTHVWb2l1d2JPcWJIQ2l2dG5IQStIUFV5d016S282R091dktKRDBBcU5WRkFwb2dUWWJDRGpFNzdaclRMcGVIK1V1dW1paTdXQ1NVd1cwZUtET05Cd0tJNWhxMldZVTZTZDN6M2dnSHV3akhxVytNcWJaeVlXaFpiRjZHckNDTWVYcHVwZ3V6S2NkRWU2SGZHS1Axb21yMnQrY2RUdEhmOThaTWVFK3A2YksySGhrVkVNMFZDeTV3a0Z1KzYvRG5QNGdOSTFNZGZ6YmdyQUkwREUxRGtsV0FDRVVIZzJicWxVSXhzbjFrbUVWZ25ML3Rzb2YvWTd0WmhrbFIzR3hDZG8iLCJtYWMiOiJjOWEwODk2YjU4YjFjMTA4ZjVjNTZlNDU3YjYxMmM4NGE3ODc1Y2NiNTYyMmU2Y2E3NzQxNDBjNDMzMGU2YmI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818181721\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1212232100 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">11VKLFOC9SwU4msTriW9Qe9qFQYBzUDiUZyyONvR</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212232100\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-794550729 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:41:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVkeGNlSU9uV3RkTjFYS1lPVTdINGc9PSIsInZhbHVlIjoiQkNncDg2WFkxNVpyTjZ4MVJrWmMxeVFidTdIUGJKNW80UndaT1Mwbkh2VlpVaWoyaXJyZHJ2MFp0c2ZJeXZWMEVpV251bUdtblZRZ0Jmdm05eEtycnMzQ1BIeHBTcVc1Szk4bnBpTG4vYWFqUGNxMXBwQjM3RE5QUnBXTmNqbEc5Y1BVZnBDS21qOVRENHFhMUZ5VmsyMFZmNjZaZnhuYXJzazYwUHpJdExOKzhOWUxINWhvRzkwMk43eE1XcHdrVk5ISG9lZEprQWNvSVlPcW1UUUtVTmk2ZG9IcmtEa2o5OWE3dVU5akZSU3ZZQWFTcnZXdWNUOFp1R2lOa2RML0I3d1ErandTT1dVOFBQdVFQaExkV2t1ZjdFNU1jKzZqc3JHa2xKUlcrRW95dTl5ZFFQampYT3pEelg0WGdCYmZRU3JHWVdVRlFsRmgwdkFYYTk5SDBaTUhsZmZBcmhXU3dqelpPdnBMTXpxMDBJN3ZwUzU0UVQzNWpTYThFNHpiaSs1QkhxaVRxQXEyVi9uNk1PN1F6VmpIemhzc1FKTm51bGVhYWZDdkkzNUZ1ZU5uZi94MEJNVEZUdkFJNm1keVczSGtxYVBpeklzQ2JlOEtlYnBYcHZmYmNCV1FoMDRUb1pEclBMYmwzRWtMb3BZN3l0a3lVamgrK1R5M05IL00iLCJtYWMiOiI4MzBjMTZmNzdkOTEwM2MzZGVlNmVmODFjNWFjZmJiYzY0YzA1ZThhNGQ5Yjk3MGM2NzA5OTI0Y2ExNzgyOTRlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1FTlB1RGxtMTJ5cHlLSGpTejZjRkE9PSIsInZhbHVlIjoiVUs4VzJsQi9lSW1Zb0l2TlVUbXo0UFdUdXFOblE4aVZnVVBmcTc3V0hZMGZ0cWdtN2djRXpURHYzWGZ2aVc0bExMYjdvb1JwaDNIMWIza0xTUWIwVWhHdGswcXB6dmU3aThYWlQ0OTBWOGNXazhmWWRZSXlyQldsTExidFZobTRCQzFUaTZJZml0NnZsSkFYcWpDN2wvTjZaVDRKNmlsd3lTeEMxYmRKTzJzYSs5aDdZL2srTHhZMFB4S1h6NEtuVTRnOWtoZXhPQ1BjVmRwNkkreW8rd0ZacWxBNGZ2RzMxT01xeUFQNTVySzZlN2NZd3o1c1NvOVRPSTc3TDVmelAzOHJic1JldVo3SDNEQWpwbG9TKzQ2bE80UWgzUjkrSWtiT1kvUVBWbHZMRlJUTERhUXFaQVQwdXFuOTQ4cWtxejl6L0xxRGRaY21pL1VDbE1IUzQ5WWRKb1I5NENlRHd3b3prdnJ1aUQxRTM3MzVnaTd2U3R0N3hod0NOTWlDb2NpYXB3dmFFZktpNkg0YnhwVStZUTFHYkkrd2ltM0dNUHdqaCtiS3N2NzNRYW9EK012ZURpSVJCS01RNVh4djNMSjZ3OENMRVE5ZTU3OVh2TGZzTlNJS0VIRFNHNFdUTnNieExucW85R1c4TlNpUEoxSUw2bVRoY0xPTE5abHUiLCJtYWMiOiJiNzU5OWJiNjlhODdkMjUwMzY1NDM0NzA5NTgyZmNjMjM1YTRkNjJiNTAwM2Q3OTIzYWZiMjFiNjU2Njk4NzFjIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVkeGNlSU9uV3RkTjFYS1lPVTdINGc9PSIsInZhbHVlIjoiQkNncDg2WFkxNVpyTjZ4MVJrWmMxeVFidTdIUGJKNW80UndaT1Mwbkh2VlpVaWoyaXJyZHJ2MFp0c2ZJeXZWMEVpV251bUdtblZRZ0Jmdm05eEtycnMzQ1BIeHBTcVc1Szk4bnBpTG4vYWFqUGNxMXBwQjM3RE5QUnBXTmNqbEc5Y1BVZnBDS21qOVRENHFhMUZ5VmsyMFZmNjZaZnhuYXJzazYwUHpJdExOKzhOWUxINWhvRzkwMk43eE1XcHdrVk5ISG9lZEprQWNvSVlPcW1UUUtVTmk2ZG9IcmtEa2o5OWE3dVU5akZSU3ZZQWFTcnZXdWNUOFp1R2lOa2RML0I3d1ErandTT1dVOFBQdVFQaExkV2t1ZjdFNU1jKzZqc3JHa2xKUlcrRW95dTl5ZFFQampYT3pEelg0WGdCYmZRU3JHWVdVRlFsRmgwdkFYYTk5SDBaTUhsZmZBcmhXU3dqelpPdnBMTXpxMDBJN3ZwUzU0UVQzNWpTYThFNHpiaSs1QkhxaVRxQXEyVi9uNk1PN1F6VmpIemhzc1FKTm51bGVhYWZDdkkzNUZ1ZU5uZi94MEJNVEZUdkFJNm1keVczSGtxYVBpeklzQ2JlOEtlYnBYcHZmYmNCV1FoMDRUb1pEclBMYmwzRWtMb3BZN3l0a3lVamgrK1R5M05IL00iLCJtYWMiOiI4MzBjMTZmNzdkOTEwM2MzZGVlNmVmODFjNWFjZmJiYzY0YzA1ZThhNGQ5Yjk3MGM2NzA5OTI0Y2ExNzgyOTRlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1FTlB1RGxtMTJ5cHlLSGpTejZjRkE9PSIsInZhbHVlIjoiVUs4VzJsQi9lSW1Zb0l2TlVUbXo0UFdUdXFOblE4aVZnVVBmcTc3V0hZMGZ0cWdtN2djRXpURHYzWGZ2aVc0bExMYjdvb1JwaDNIMWIza0xTUWIwVWhHdGswcXB6dmU3aThYWlQ0OTBWOGNXazhmWWRZSXlyQldsTExidFZobTRCQzFUaTZJZml0NnZsSkFYcWpDN2wvTjZaVDRKNmlsd3lTeEMxYmRKTzJzYSs5aDdZL2srTHhZMFB4S1h6NEtuVTRnOWtoZXhPQ1BjVmRwNkkreW8rd0ZacWxBNGZ2RzMxT01xeUFQNTVySzZlN2NZd3o1c1NvOVRPSTc3TDVmelAzOHJic1JldVo3SDNEQWpwbG9TKzQ2bE80UWgzUjkrSWtiT1kvUVBWbHZMRlJUTERhUXFaQVQwdXFuOTQ4cWtxejl6L0xxRGRaY21pL1VDbE1IUzQ5WWRKb1I5NENlRHd3b3prdnJ1aUQxRTM3MzVnaTd2U3R0N3hod0NOTWlDb2NpYXB3dmFFZktpNkg0YnhwVStZUTFHYkkrd2ltM0dNUHdqaCtiS3N2NzNRYW9EK012ZURpSVJCS01RNVh4djNMSjZ3OENMRVE5ZTU3OVh2TGZzTlNJS0VIRFNHNFdUTnNieExucW85R1c4TlNpUEoxSUw2bVRoY0xPTE5abHUiLCJtYWMiOiJiNzU5OWJiNjlhODdkMjUwMzY1NDM0NzA5NTgyZmNjMjM1YTRkNjJiNTAwM2Q3OTIzYWZiMjFiNjU2Njk4NzFjIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794550729\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-227672871 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-227672871\", {\"maxDepth\":0})</script>\n"}}