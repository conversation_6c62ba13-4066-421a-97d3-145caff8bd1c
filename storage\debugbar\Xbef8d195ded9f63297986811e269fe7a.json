{"__meta": {"id": "Xbef8d195ded9f63297986811e269fe7a", "datetime": "2025-07-14 22:31:04", "utime": **********.978891, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.533557, "end": **********.978905, "duration": 0.*****************, "duration_str": "445ms", "measures": [{"label": "Booting", "start": **********.533557, "relative_start": 0, "end": **********.912023, "relative_end": **********.912023, "duration": 0.****************, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.912032, "relative_start": 0.****************, "end": **********.978907, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "66.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01592, "accumulated_duration_str": "15.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9409459, "duration": 0.01481, "duration_str": "14.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.028}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.964062, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.028, "width_percent": 2.638}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.971603, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 95.666, "width_percent": 4.334}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C**********441%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFPNkdpbUcrSTNzeGFEWm1wTWNUcVE9PSIsInZhbHVlIjoiWWV2SmFVQVhLZEhsOGdQelhKeitpTUh4cVVSRnZMVGx1SGtDc1RiWGJhcStZWUVnV0RkTTZYUHJJQlhBd3VTVU52TEV4RjlUbUI2aFQ5UVlGaVN2YzNjY2V0VllpTmd1MGNId3JGUm15NzNUYThXQVArMVl2WG1yc29MVnp3YmtaUnkzQmZVdi8zN3o5MWwrVTRYT3MvaEdjSGt1Rzc1SHI5S3FRWEhoMHJ5NDhtSmdLTEgxR0hGRFhoVURIV2UwUWRKQnA5NmFuZzJhSmU4a1JxTmMrSjlUSTVDT0xCVVYrYlRib1phT2xrNUttcnpFdWQva2hLQzlyWHBmU0tRTmlzajUyTXBGeGZzTVJ6WnBLSlJDQ21HaUpXdy95MEtCSkpZeCtqaEF1MU13TUFNOTBySmdXc054TkcwZEpqUjJyVEVRaXhOUEl2OGhjaWl5K01CNnNqNzRuSGNyclpDM1BlRDdiMVg3elFIaTY2dm04eUNIb2IwdERadUQyOThneWtXRlViNkFBYjJ1RXpRZzF3UGQ0c1d1SGtFcStJdUJxeHFSdTlzUWIrSjdUWHJwNEV6Rm1WbHIwU1B4aDY1ZnArT3puTXpqaGErdkRPM2xwK0U5LzMrWW1PNWtQSzF0TklRTDU2R0JDWnVhbThlMlJvUGpKb2NwQ1VzZ3NXbGMiLCJtYWMiOiIxODE4OWQ4ODJlZjFhNjg1NzMyMDQzY2IwN2JkYjE5OTNlNDY3MWYxMzRiMmNhZDU1YzczYzljYWIxZTgyYzFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBOM2ZQZThDbW5FdGVacTJvQVdyNlE9PSIsInZhbHVlIjoiZk9sUy9kTXg1TGQvUmpyUmdaVHRaT3B3QlNxQURPYW5lOVpxWG03elRsaExld2tMOW5TYXlJdXV0aWZOdTdmcGZWRE1wSkxmWC9xdVc2OHBLWVhJNGtQaFVLL2VpdjZkQVl4Y0U4Unh5K0ErOXBySndzNUY2TmhRM3lsanFFQ0YxeVlpTkIxM01IQ1Rpb3hGekdKbU9KVmE5dTB2UmhCRnFKdWpHUWU0QktqblBWTnBITnRySWtuYU92L1ZidWRJVUdvQXZyUWc2N05qVGdaS0FDTFF5WDVQaW5WTkM0amF4R21ldnFFZUZydnFpcGgrdWoyRjNOYWsrMmVydllHZE9DcXNSbWt4aXBLbUxBL3k2YjRpRSs1OFF1RjhLcHlGejM2UnREYlQxWFJsOEErKzFxNC9BK2M1K2pqRG5HL29USW1WVndRUktVMHp5dEtsOGtRVUc5UVN0aDE1MW1QVXowclFWM1MxTno3Q3YwSGMyZ1UvSHVIbnloVnhiN3NtSlJoc05ESERlSlZjcFpkMGVwemxYTXdxeXE5Rkxsc2t1UC8vQTYwODZtQnRmQ1hqVVQ1UDdEN0hveWRrMENkV2U5RHB5RHdxancxYlI0UHRSdHB3MWgvbENYMWZOYXNHaXRPcG0zTjMwdXpDcVVJMkFQcEh6NVhwTHFOK3ZIa1oiLCJtYWMiOiJiOWIyNDQwNTNiNmEwZjMyNWY4ZmYxNGRlYzgzZTM5M2Y2NjgxOTEyZTk2ODhiYzI3YjRkMzQxNDg3ZDIzZTRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-777648286 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777648286\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVxRjYwSkhySU1Lam5hVmJMK3diM3c9PSIsInZhbHVlIjoieDIyWmxIdWp6UGJ0Tm5OYlh3MlpDS1JOMFl1RDJ1eHF3SXJVbmlRMVB2TUhZaS96ZjJYS0xHYkdSM3dTZlB0VHFMb3o2Ylp2RWVSUkdqam16anc4Q1E5RDdHZWExd2Y3SUI2YnUrNGNKU2w1b09mT3JPanFra08wV0VzWGZwTnlIc3RweFhGU1FpVDBtQW5rbWZhNTQrem5qdklONzJzMi92bkFDZG9yblhhdC91MTFJckJWZGJkNEJYSnROZnV2VW0zaENGSEJvRGRsSi91TFVBY1FuSjJobXk1cDd3ZnROTkRwcC80VzQyd0hzR2w3cDFxcTRjbXQ1Z3ZWYkFGWXdqQXphSHpRYWV5eDBlQ3FwbWg4M1c2VC9XNVh2YWc2eWdDYU83Nlh2VXIzQlpKVXFGaFJ5bDJNdHlhOUZxbDVvdG1tTGRrVC9kYVFRbDRPdG1lQTgxSVF2cXhkdngyUDVibmhaQmhZZFhTNlUzSmZPbnRJK2RsdWR3MTNaMlJicCs3bGRxc2RKdzByNEE4NHRUK1FmK0ZhVy92VEVjVEUzZ2JvUHhjTGg2c1E0NnNkQ2R3MHVRUzEwOTNMeHEwOElaZGJoT21qUXBteUZVSEJHdm9ZeWR0Sndnd2tSTWZsSkI2Vkh3U1dwSlBzYkl2U21nVTU2S1hJL2V5RCtQNkIiLCJtYWMiOiI0ZWEyMjk0MzQxODZiYWQwNmM5ODM3YjJlNTI3ZWI3ZmNmMzM4NzhhNDc1NGExZjI2YzRiYzlkMTE5ZmRiNmI4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhoVlFkUDU3UGhBVFpmRDN5STFzcnc9PSIsInZhbHVlIjoiV0dSdDZGTlp1SVc0VUhlZStwa0VTbWF4NlJPcE81a1NQelBtWW03cDMxTnJjckYzb0JVTWdDRkNQNUVVVGtmQ2MzVGZTcnhiVGZtU1BIdUZOZVVsRXg0OFAvelBQUk9FZzE2cHpLRnFZKzcvMTV3MVdDVDVGdWErcWpMTHRaWUJIaUJwcU5nY0V3M1pYdEd6UVNpYzNqYzBBbyttcWgvek9wNkJzdi96R2dJSVJlbUFTOFB3bDE3Q3NhS2I4K1JLNllqZkV5MjdXemZ1eEVCR3lzQ05nWXEybEJEWkZqRTAyemhpQk1NeTRiMkJoYWlaeWVMQWZHZjdkTVpRakwxbzNnNkc4QXJwZUdoeGRydDMwb0wxbFhud1lXSGNyeFZlTUxOSmVROVhUblBQb21KR01mNTIyMlEvbWdaVTVmL3VpaGgvME1NTkdMUFN1NThnSW5IdjZFWVl1eVQxTXNaZUd2cFRUMWFhVXlaTGYyM3hlWFNFb0RLcit4YW1ZeVlxS0tWNHE2RGdNbnovUDh2dmh6R05MTXVvR3JINTFJMzgra1hFeVlsMDExRGs1UGMySlNZZmZCdVBtNUtLT1FsemROVEJwOUNtMUxrdURZc1Y3a1J3MmUreStkbkVWNTNLbUtKODJnaVV1MTZ0eEN6TDBXU0ZDUDJEdzR1T3BCTTYiLCJtYWMiOiI0MDE3YzM2MTRkMjliZDczMmVlZTM2NGIxMTUyNGZmZDU0YTNmZWI2YzQ2NzliY2MxMWVjZjZlN2Q1ZGE0MWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVxRjYwSkhySU1Lam5hVmJMK3diM3c9PSIsInZhbHVlIjoieDIyWmxIdWp6UGJ0Tm5OYlh3MlpDS1JOMFl1RDJ1eHF3SXJVbmlRMVB2TUhZaS96ZjJYS0xHYkdSM3dTZlB0VHFMb3o2Ylp2RWVSUkdqam16anc4Q1E5RDdHZWExd2Y3SUI2YnUrNGNKU2w1b09mT3JPanFra08wV0VzWGZwTnlIc3RweFhGU1FpVDBtQW5rbWZhNTQrem5qdklONzJzMi92bkFDZG9yblhhdC91MTFJckJWZGJkNEJYSnROZnV2VW0zaENGSEJvRGRsSi91TFVBY1FuSjJobXk1cDd3ZnROTkRwcC80VzQyd0hzR2w3cDFxcTRjbXQ1Z3ZWYkFGWXdqQXphSHpRYWV5eDBlQ3FwbWg4M1c2VC9XNVh2YWc2eWdDYU83Nlh2VXIzQlpKVXFGaFJ5bDJNdHlhOUZxbDVvdG1tTGRrVC9kYVFRbDRPdG1lQTgxSVF2cXhkdngyUDVibmhaQmhZZFhTNlUzSmZPbnRJK2RsdWR3MTNaMlJicCs3bGRxc2RKdzByNEE4NHRUK1FmK0ZhVy92VEVjVEUzZ2JvUHhjTGg2c1E0NnNkQ2R3MHVRUzEwOTNMeHEwOElaZGJoT21qUXBteUZVSEJHdm9ZeWR0Sndnd2tSTWZsSkI2Vkh3U1dwSlBzYkl2U21nVTU2S1hJL2V5RCtQNkIiLCJtYWMiOiI0ZWEyMjk0MzQxODZiYWQwNmM5ODM3YjJlNTI3ZWI3ZmNmMzM4NzhhNDc1NGExZjI2YzRiYzlkMTE5ZmRiNmI4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhoVlFkUDU3UGhBVFpmRDN5STFzcnc9PSIsInZhbHVlIjoiV0dSdDZGTlp1SVc0VUhlZStwa0VTbWF4NlJPcE81a1NQelBtWW03cDMxTnJjckYzb0JVTWdDRkNQNUVVVGtmQ2MzVGZTcnhiVGZtU1BIdUZOZVVsRXg0OFAvelBQUk9FZzE2cHpLRnFZKzcvMTV3MVdDVDVGdWErcWpMTHRaWUJIaUJwcU5nY0V3M1pYdEd6UVNpYzNqYzBBbyttcWgvek9wNkJzdi96R2dJSVJlbUFTOFB3bDE3Q3NhS2I4K1JLNllqZkV5MjdXemZ1eEVCR3lzQ05nWXEybEJEWkZqRTAyemhpQk1NeTRiMkJoYWlaeWVMQWZHZjdkTVpRakwxbzNnNkc4QXJwZUdoeGRydDMwb0wxbFhud1lXSGNyeFZlTUxOSmVROVhUblBQb21KR01mNTIyMlEvbWdaVTVmL3VpaGgvME1NTkdMUFN1NThnSW5IdjZFWVl1eVQxTXNaZUd2cFRUMWFhVXlaTGYyM3hlWFNFb0RLcit4YW1ZeVlxS0tWNHE2RGdNbnovUDh2dmh6R05MTXVvR3JINTFJMzgra1hFeVlsMDExRGs1UGMySlNZZmZCdVBtNUtLT1FsemROVEJwOUNtMUxrdURZc1Y3a1J3MmUreStkbkVWNTNLbUtKODJnaVV1MTZ0eEN6TDBXU0ZDUDJEdzR1T3BCTTYiLCJtYWMiOiI0MDE3YzM2MTRkMjliZDczMmVlZTM2NGIxMTUyNGZmZDU0YTNmZWI2YzQ2NzliY2MxMWVjZjZlN2Q1ZGE0MWQ3IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}