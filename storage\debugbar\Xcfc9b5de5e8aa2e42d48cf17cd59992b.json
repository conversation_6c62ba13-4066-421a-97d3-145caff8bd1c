{"__meta": {"id": "Xcfc9b5de5e8aa2e42d48cf17cd59992b", "datetime": "2025-07-14 22:27:49", "utime": **********.089408, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532068.505506, "end": **********.089422, "duration": 0.5839159488677979, "duration_str": "584ms", "measures": [{"label": "Booting", "start": 1752532068.505506, "relative_start": 0, "end": 1752532068.993013, "relative_end": 1752532068.993013, "duration": 0.4875068664550781, "duration_str": "488ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752532068.993022, "relative_start": 0.487515926361084, "end": **********.089424, "relative_end": 1.9073486328125e-06, "duration": 0.09640192985534668, "duration_str": "96.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48491104, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.025970000000000003, "accumulated_duration_str": "25.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.029127, "duration": 0.02094, "duration_str": "20.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.631}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.058233, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.631, "width_percent": 2.503}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.072793, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 83.134, "width_percent": 2.811}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.074921, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.945, "width_percent": 1.54}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.079694, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 87.486, "width_percent": 12.514}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-70343767 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70343767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078453, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-296750844 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-296750844\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-550499249 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-550499249\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-700000305 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-700000305\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1806054672 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=1ue2vqu%7C1752523517717%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk5TUplZUthdzNmTEc2RGMrWGt5NVE9PSIsInZhbHVlIjoiQWNDcTJBaEwrOTl0K2E4eDJrYWg1MTVYd1ZNK3FmVFdUNWw2UnJUQTQ0MGhsYzRHQ3JRMzdhN2ZRd1JBYks0VG5wNEx0RFNvZmd3K1BKNVhYdUQ0aHBCeno3K0cyMVBSUmora2o3Uk9VbGFvTmYxSHV1V0I5MHludEtDUTBYb25SQmdQVkw0b2c0RG1zTUlWL3IrMmxZVC9IM3hSL2xnazhxSVMxSCtOajhYZDBzaGNkcmJkQ0FqbjUxenppaU5GK0MzT0ZIeFloWHVUUVlLUmxYZjZmSWlIR1lvNEtzZ0VhTis2NDlWWndKYmtxb09YVEYwY0lrWTRrbExOWWJDcmFHRTd5cWllQWxJYUZtdUc5WWorMmZqdHdIR0ZabFRPNVBQQi9ScTBSaVRJYzNzODhTekZwd2dyK0pxRnJCRHAySGRGcmw5dUpycXpjZWo1WENkTGllWHRjaTZOM3kwbXVIRDRwbXdhekVwMjU1TE5SM0w3ZXk5QU5SRHl6czhLY2tvd1F2Zkt1MS9QVDVVZ1RRa1p2SHlPcnhpQ2w0QVRtTmdjQys1V3c3RlVKQThBb1E2RW1WS2x6Uy8vTXdTLytZQWJNT3llNWhsSEVTUThiMTJsTXB2VnplUlVkcHp1RVdyNTNoTkdhTjJYSXJJbEM3cXZ0M3FjbkhNczY2SEQiLCJtYWMiOiI3NWRkZTRmMDBhYzVmMTBmMGU1NDBhMjRjZjdlOTJlYTM3ZjRjN2VhNTViNWZmMTFiODZmZDU4M2QxOWQ5MjAyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im0zekU1WTExOFRWbXp6SzJNM3FCTkE9PSIsInZhbHVlIjoiN294cTJ4MW1UUW1BYjg2QzBLdDBPbzQ0WEExblFqd2FtQTFiVWlseFllZ3NmeFJaUzA1WmdWY0l6UlZOR08wUi9oNEpaMzA5VmVrQjBCVUZYemRUWHE4VkhSTTlXa2F6cGRzVkpsclN4dEkrMC9hZFdSTnJZQXpicXdFWVIvdE9yZzhrS2tNdEZ5NW9RdkJncUVYMXFTK0xKSkl0Q3M0Mi9UZW1tSGtCM29ORjEyb1RzRTlZYnQvOS94d25HOVpqTXFmTm1yVjl6YU5FYkRkVnhFcTVMRkZ2RnZKOTBXZ2tWa1k2TXlHVGZROStBUlNtV2FCNk9acmhQWFRkbHhnNjZKR0dCRVVxM1pJSEp2ZU1LaytQN1dIandvdWh2RXQyNlE0Zy85WkdCWmNJbkk1d2pWNXZWRmtsdXFhQ3QxRWFkQkFVdmhPR0RVbU1FeU1EN3owK3NlM0orak8yVUExT1ZoOWVBekRYY3FnSGRIYVRrZU11MENTK1BpRDNkMnJJc2pvOGZHUHFaUDlsczJ6RjYzaEo1c2hQK2JOODNYK0Vuc1hEK0x2SWFDb2J5TkZIdnBBSjFsUG1HaERmb0s5TVRuTUlZcXhnWHVWRnVmWHd4UVZ5VFVIdk5ZWXZ5Ylc4eUNrQlVWTHJWaURrckQzaURPNXovOVJIRCtCRVdldCsiLCJtYWMiOiJiYjQ3MmE3MDU2MDZjMTExODE4NDE5ZDIxMTQ2YzlmYTFhZmQyYmJiMTViYzA4MmE1NTgxNzNkYjQ3ZDFhYjM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1806054672\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-623720621 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623720621\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-946296954 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:27:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlR4Um1mZzVMSUZtanppVk1ySlZvN2c9PSIsInZhbHVlIjoibHBMM1Q4TTE4czZuMW9mOVk3cFMyOHY5Yzc0UGJTclJrWXlFNGtTdjlZcWx0Qm1YWXlkV1dLNm1oMXhQZ2YydkJpL0QyMW1YS2pCd2lsaWJSOUZsTUZEZVRzcnVwcGx2TlhlY3dDZm9nK3diNTBxUkw2UmtOUk5SU0FJZlVpTjJDUkJsNmxSYzlEWXp4OVJrbXQzbWFjWlhGaFRQekt5STRnbi9yOTJualRIU3JtUW5HYm54YUVlVFVNYWc5OG50QXlISXlPM1lLMHFQY2xTRitITVAzMXpsa1lNTGJ2dDNrS2lRcmpYYW9lS3FDUTk5cU5NcFhHOGF3N01YR3NMWHB0d0c1cnVZOWxHUE8vN1V6NWFKMG9nYithNDdaSm1MTjZUREZZbWQ4VGJZZU45Rkk0N0ljYVk1Uk5UOXB0d01rOElDSEVqV0ZKeVF3amNOeithZGtDTmFlYituUFJjNktPeU1Scmp1RThNRkxVNTg3eFkzSGNIZnI4VHhJdFdWWFRGeTdTdjllTFRBMGd1ZW1hSlVjanNHaTZST2d3TXRWemRVcGs5bklkNHlESlRXYzBvR1pENnNrZG9XWFF4MzZOSy9IdkQyWXpSdnp3T0F4U2Y5RVBlSEdudHNSbDhZSEI0OXdZWGZ5WHIxc3pZejVmWXV2VFJaUmQ1bTVwWmciLCJtYWMiOiJiY2EyZTc2N2M4MzBlYjNmZmNhZjgyOWZiOGM0NTZiMGEyNDlkN2VmOWZhZGFmNTAwOTYxZGRlNjdmMTk4NTdmIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:27:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5mZ21tTFY1NU9wb2hobWNnZXl1MEE9PSIsInZhbHVlIjoiQURXUENwcXV0NHJodWNqb3Q1UWpvUWVBaWFvZG02YmdjL3dYcWRpNUc0T3NmWG0ybEdaY3dpTXg3ZVplQmlOdWJEVE1WeHlvMlEvKzlKQlNiQ3AwcWpTNmRLdlRGMmRCU21GaVdqR0JSWC9lZS83U04zUHFiRWpCdjIreDBZcXpzdVdveUNnbmFZUlN5dS80bkFKWHZYeHJCQzgyN0I5cmQrUzd5ZU9McEhXM1BiaHpJNHZwT1RHS1VvdDFJd0ExOVVzUGFBanNlRUM2WjVDT054ZzRmczU1N2h3SE9UdXhwbVZpZllxWW0zWnI1QWxPREc1dWtTK0syWDkrQ0Vodi9sRVo3dUxnUW93Uk5zSkhORXdJakswVk1HVUErU09ERjF2UllvZjV4aVBxbzd5YVVwcDlCZlMrL2xTN1F1aDR1Z1JMMm52c2JZYjByTDJIbUt0VjhMaTlyZmNGTHgwMlF3clpzR1VPYVV6T0hYenBaYnJxdnRuSEtlbFVZYk9McThlclU1LzVzKzVxMkgySUFiNHlmV0l0TFdWVDdRWnBCSEl5ZW9oSWZiaWJmdFNpbmE3RXI2NmFENkdmL3crU3FoRzFuYjVva1diMDV2TWtNWUVvSGlJMmVJekhsS2NpOHFTcVJsM2NRYmlEeE1mbmVIU3ZIeWJqWWdsRk9VSWsiLCJtYWMiOiI1YWUyZTI0Y2U2Yzc4MmZmOGY1ODJlMDA4ODJjZmYxY2NjMjk0MDM4MTcxZDMyMzc5N2FkNjY5OWUwM2U5NWI1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:27:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlR4Um1mZzVMSUZtanppVk1ySlZvN2c9PSIsInZhbHVlIjoibHBMM1Q4TTE4czZuMW9mOVk3cFMyOHY5Yzc0UGJTclJrWXlFNGtTdjlZcWx0Qm1YWXlkV1dLNm1oMXhQZ2YydkJpL0QyMW1YS2pCd2lsaWJSOUZsTUZEZVRzcnVwcGx2TlhlY3dDZm9nK3diNTBxUkw2UmtOUk5SU0FJZlVpTjJDUkJsNmxSYzlEWXp4OVJrbXQzbWFjWlhGaFRQekt5STRnbi9yOTJualRIU3JtUW5HYm54YUVlVFVNYWc5OG50QXlISXlPM1lLMHFQY2xTRitITVAzMXpsa1lNTGJ2dDNrS2lRcmpYYW9lS3FDUTk5cU5NcFhHOGF3N01YR3NMWHB0d0c1cnVZOWxHUE8vN1V6NWFKMG9nYithNDdaSm1MTjZUREZZbWQ4VGJZZU45Rkk0N0ljYVk1Uk5UOXB0d01rOElDSEVqV0ZKeVF3amNOeithZGtDTmFlYituUFJjNktPeU1Scmp1RThNRkxVNTg3eFkzSGNIZnI4VHhJdFdWWFRGeTdTdjllTFRBMGd1ZW1hSlVjanNHaTZST2d3TXRWemRVcGs5bklkNHlESlRXYzBvR1pENnNrZG9XWFF4MzZOSy9IdkQyWXpSdnp3T0F4U2Y5RVBlSEdudHNSbDhZSEI0OXdZWGZ5WHIxc3pZejVmWXV2VFJaUmQ1bTVwWmciLCJtYWMiOiJiY2EyZTc2N2M4MzBlYjNmZmNhZjgyOWZiOGM0NTZiMGEyNDlkN2VmOWZhZGFmNTAwOTYxZGRlNjdmMTk4NTdmIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:27:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5mZ21tTFY1NU9wb2hobWNnZXl1MEE9PSIsInZhbHVlIjoiQURXUENwcXV0NHJodWNqb3Q1UWpvUWVBaWFvZG02YmdjL3dYcWRpNUc0T3NmWG0ybEdaY3dpTXg3ZVplQmlOdWJEVE1WeHlvMlEvKzlKQlNiQ3AwcWpTNmRLdlRGMmRCU21GaVdqR0JSWC9lZS83U04zUHFiRWpCdjIreDBZcXpzdVdveUNnbmFZUlN5dS80bkFKWHZYeHJCQzgyN0I5cmQrUzd5ZU9McEhXM1BiaHpJNHZwT1RHS1VvdDFJd0ExOVVzUGFBanNlRUM2WjVDT054ZzRmczU1N2h3SE9UdXhwbVZpZllxWW0zWnI1QWxPREc1dWtTK0syWDkrQ0Vodi9sRVo3dUxnUW93Uk5zSkhORXdJakswVk1HVUErU09ERjF2UllvZjV4aVBxbzd5YVVwcDlCZlMrL2xTN1F1aDR1Z1JMMm52c2JZYjByTDJIbUt0VjhMaTlyZmNGTHgwMlF3clpzR1VPYVV6T0hYenBaYnJxdnRuSEtlbFVZYk9McThlclU1LzVzKzVxMkgySUFiNHlmV0l0TFdWVDdRWnBCSEl5ZW9oSWZiaWJmdFNpbmE3RXI2NmFENkdmL3crU3FoRzFuYjVva1diMDV2TWtNWUVvSGlJMmVJekhsS2NpOHFTcVJsM2NRYmlEeE1mbmVIU3ZIeWJqWWdsRk9VSWsiLCJtYWMiOiI1YWUyZTI0Y2U2Yzc4MmZmOGY1ODJlMDA4ODJjZmYxY2NjMjk0MDM4MTcxZDMyMzc5N2FkNjY5OWUwM2U5NWI1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:27:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-946296954\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-230792424 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230792424\", {\"maxDepth\":0})</script>\n"}}