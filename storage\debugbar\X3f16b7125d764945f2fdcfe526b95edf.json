{"__meta": {"id": "X3f16b7125d764945f2fdcfe526b95edf", "datetime": "2025-07-14 22:31:06", "utime": **********.648012, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.223884, "end": **********.648051, "duration": 0.****************, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.223884, "relative_start": 0, "end": **********.566827, "relative_end": **********.566827, "duration": 0.****************, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.566836, "relative_start": 0.*****************, "end": **********.648052, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "81.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025280000000000004, "accumulated_duration_str": "25.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5971258, "duration": 0.023960000000000002, "duration_str": "23.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.778}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.630523, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.778, "width_percent": 1.899}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.639983, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 96.677, "width_percent": 3.323}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532264441%7C4%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZYQUk2OGJyYWo1UWNKQ1AwcmpIbUE9PSIsInZhbHVlIjoiYzdoS2tsbms0ZGhGbzluc1VSeStMSVprSkJ0d0xtZVJIdlVHREF5ZFJIQTFYK2dROURLeWRRcmJHM21LZFU0ckJudmh0U0VVSVM3RG41RGQ4MEJ0c0dJSHQwZmozaG9qWVZJSGxXRmpyVXY0N2JTT3dGaFkyMHJKSXRTYjRYUlUybzB4YzM4TnNmclZaOHRNNlZMUzFBSVNYc0JMMzJCc0xXSWovZ3ZTV0tjUjNjUEs0Qm1sSEZUWkVUdFd0TzcvQng1N1g2RklVRmVhc1JReVdWMUxiNFBCVXZxV2x5bnpXckgrY3VNc1lOcVB6Nzhmb0oveFU3NzZNVkZTSzA2cE1sZHJCSnNrM2haYVlMeEFJMXlLQ0hhMEtqdmJ0eER3VytTM0VKQXU1ZVBaRE1BdTFlQjY4VitzbWNjN1hsL0RoYXJUTGxBNlVkSlBVWm5tclNva3JBQXJ3c21aREFtZmZhYy9RNFdXK3RkNmhNZW1hcDR5aWtxKytUZVdialRrVE9RaERQZmlyTXdZUWExZTFIR2lPRVBsYjhpSVdDTzgvNFo3Q1B0ekhHOWl3NUJpcFE5cTdkaDNTNkVMS1N0dUxtNDdCckpOUkd4R2I1aWpKckFLRTFTMGpQQm0rRFZUY2p0endDc3J5YjJtTk1NZmNkMXg4N2RjaTk1L216YmMiLCJtYWMiOiI1N2ZjOGU4MjBjODcwNWFiMTMzZDA0NDIwYzBkOWViYTVjOTcxYWNiN2E1MzQzNmQ3NGFjMWFhZGI0OTM3YTNlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9zZjZtc2JKazBxOGR2clcyUDZyNlE9PSIsInZhbHVlIjoieE1GQ2R2c3o5QVdURlZIQ0UrajFUTktZWnllTy9mSi9QV0sxdFFjM2NyUjJNSEhtSmVkd3pPdHpYelVLQ0ZuQzg5aTFpUmNDR1FZZjZTY2U4bms1NUhVWGR1UWZZYzhpMDhsbGhBTnluZzRnSTBodHZISUw0TGM3b1BXd0toOG1SS0dCc0pZRXVMSmExTWVid0hnM2VFRHBERGFsaE1NY2ZuR29BTFBpc1FGbE9hbE5EVlgxVGNVajJwQVRRZUFkYmJpenBUTDROeUQxejVJakcrdVlRb2l0TG9kUSt4NktMb1lZY3NKdGFmbXIzak5hOVZyaGNKU2tRc3V3dnhLK1ZvdkZ2SHRXUXkvNGdvRU4vRTFiU2x1NzZVcmFnRzJqSGVON2d3Und2NWR4VHdrZkJuNzJidUdZdkxRbDh1OGR5Uy9teXo1eXQ1b1NnL2xwN08zSHNJeVp1RkJFNUlWUGpvakxobFdyQTB6cGpHdFBXWW5DUGV1bkdRdVNnVE9sSUhmcWxiSFFaYmhyU05RUDJJNXBPTjV3ZmZmZ0ZPYTZDUGc0QmZsa1F3QittZWlrMHVkZW44aG9MU2FabnhOMmtuWnRWVG9KWTVGNGMzeGhxVjN2S0dmVkZUWmJ3TXZVWVdxOHg5S1AySW5MOEdoZHNDakVDMjdyemFVbTBZa3ciLCJtYWMiOiJlMzYxMDIxZTYxNWMyZmVjODY0MGRhYWVlM2E0NmZjNDYxMzcyZjFkNWRjNWY4YWU0NzRjYzAyZjllYmExNTk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-8505176 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-8505176\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1144620633 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF2NHJaZWJxeDJYanJyZDFpTUo2SHc9PSIsInZhbHVlIjoicUZEc01DUEgvRld6YW9tc2g0VG9nTzdVRzFqUnNuODNzZ05FamNYbnFiM0xqMWtVVzFrNVJxTVY3ZGkxRGhWUFNmWm8wVzBSeXpsNkdlWVBZL2tIMXNpaUtDb3pPL1YxdnRsR2tEdFd0bVp3ZlNzNndENUVTMmVFSzFEYWZkdGczWHRVYUVIbWZGUmp1WUMxWWVSWTNqRjY1Z3VvNElETGd6SkZwMjhtb1BZZXVoTFlocUZhcWZyZmFheUZvRDE2VXVZcmVIWnVrRytKZ3hjcGxseHIwUm9LTURrTlQ3ZW5WS0R5blV5c1BlRXV3aDRJYlNnYUZiMHJqQWZ5dmFGZlMyK1FsMXEwTk5RUkdxdWpoeFdGRU5wVyt5NUJwZGhRU21TT3dEL1V1cVo5OFRVcnBWbEM3U3c5aWJxaEt0akFZUnFBZzZvb0dDRGZxTXIybHZyNmJwbjFKbFlTNGpVV1pWR3kvQ0FFRUJ2dGtrMXpuTTVXNWNNdzI2ZUlSYW1yc0RYQ2Jubm9SOWJYZ2ZqRkwzS21VU2orZE11bjlFOWZSVmRick5DM0oyWXp5TUNzNFBQVWovK1E1UWt4V2FjczQvK3UzL0kyd3V6dkFaUlNQUGhhT3Uzby80MmNWWGRwSXptQk9KS3JNOEhxRVRyeUdDdE1pYzN5b2Zvc0VNbmEiLCJtYWMiOiJjM2Y4NzNhNmMyMWViMmRiY2I5MjRjNWNjZWMyZWMyZGZhYzQ4MDM5MmQ2NDBkODc4NzEwZmJhM2U5MTJlZDg0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0xRGFwME1ockNadVF2MEp1eEdDaHc9PSIsInZhbHVlIjoiRHVBbjZCcWtxVDZuamZyaC9FbUNhQnpMNm91MFNPd2NhQjdVY3ZqOHBIK0MzZk9VWXZqbUlvSFRYU2VzL2R0U1BCelVKRDBZdStBaVFmR0NuSmJ4NERZbmx4WENqMEl5VHlSVFZ1WW4zV2c5Tjl5ZHEzZm1MKzdsWEhCaDNXc2xUUGRkV1VMOVBqR29jTXRNb1RuRzBDeG0vUWsxekQ0S0hxKzFhTXYvNTkrUi9jQlBXZTVnUmRZQUZEMXM2ZTMyUnAvenlYMmdjNTlod0xoTzBCVG50eWtFWEZaTFRxeFN4a2ZqZW9mLzBZdzZ3TEczbjNhZTM3TmRZSDFBYXNEOXptL0k2ejJ2eC9IajdrVGY4NHVqSjdJVkNmWEJidnU1RExMT04rT0F0Y2FFZEE2SFAvVG1vY0p6dEVJWHl4dXlMME9LSlNYUXJFRzhHM0QvNkNIQVBpRmYrQStLY1dGOEJPYU4xeDFjVHdGYWxBY1lPRW1CMG9BTWdlZkV0SzJHaGhla3dHRlh3aTk2a1V4YW1OTDlVc2dIdEdRcEpVdFowUW9xckUwNEY1SWxyOFF4ZklEWEp1Q2hsM3NEeVExZ3dmQnZxQUpyeWtkeUM1em5BTjM3VW5FK3ZlYXlobzVtL0d4L2VJbEh2bUE1cXRzYnFXcXFEVlRwS1ZTZVpQUXIiLCJtYWMiOiI5OGJjNjkwMGVmMzkwNmIzZmUzYmUwY2Q1Njk2ZWZhNjZiZWY3MDUxOGY5NTNhZWQwYzQ1Y2M3YmM5MDIxODEwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF2NHJaZWJxeDJYanJyZDFpTUo2SHc9PSIsInZhbHVlIjoicUZEc01DUEgvRld6YW9tc2g0VG9nTzdVRzFqUnNuODNzZ05FamNYbnFiM0xqMWtVVzFrNVJxTVY3ZGkxRGhWUFNmWm8wVzBSeXpsNkdlWVBZL2tIMXNpaUtDb3pPL1YxdnRsR2tEdFd0bVp3ZlNzNndENUVTMmVFSzFEYWZkdGczWHRVYUVIbWZGUmp1WUMxWWVSWTNqRjY1Z3VvNElETGd6SkZwMjhtb1BZZXVoTFlocUZhcWZyZmFheUZvRDE2VXVZcmVIWnVrRytKZ3hjcGxseHIwUm9LTURrTlQ3ZW5WS0R5blV5c1BlRXV3aDRJYlNnYUZiMHJqQWZ5dmFGZlMyK1FsMXEwTk5RUkdxdWpoeFdGRU5wVyt5NUJwZGhRU21TT3dEL1V1cVo5OFRVcnBWbEM3U3c5aWJxaEt0akFZUnFBZzZvb0dDRGZxTXIybHZyNmJwbjFKbFlTNGpVV1pWR3kvQ0FFRUJ2dGtrMXpuTTVXNWNNdzI2ZUlSYW1yc0RYQ2Jubm9SOWJYZ2ZqRkwzS21VU2orZE11bjlFOWZSVmRick5DM0oyWXp5TUNzNFBQVWovK1E1UWt4V2FjczQvK3UzL0kyd3V6dkFaUlNQUGhhT3Uzby80MmNWWGRwSXptQk9KS3JNOEhxRVRyeUdDdE1pYzN5b2Zvc0VNbmEiLCJtYWMiOiJjM2Y4NzNhNmMyMWViMmRiY2I5MjRjNWNjZWMyZWMyZGZhYzQ4MDM5MmQ2NDBkODc4NzEwZmJhM2U5MTJlZDg0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0xRGFwME1ockNadVF2MEp1eEdDaHc9PSIsInZhbHVlIjoiRHVBbjZCcWtxVDZuamZyaC9FbUNhQnpMNm91MFNPd2NhQjdVY3ZqOHBIK0MzZk9VWXZqbUlvSFRYU2VzL2R0U1BCelVKRDBZdStBaVFmR0NuSmJ4NERZbmx4WENqMEl5VHlSVFZ1WW4zV2c5Tjl5ZHEzZm1MKzdsWEhCaDNXc2xUUGRkV1VMOVBqR29jTXRNb1RuRzBDeG0vUWsxekQ0S0hxKzFhTXYvNTkrUi9jQlBXZTVnUmRZQUZEMXM2ZTMyUnAvenlYMmdjNTlod0xoTzBCVG50eWtFWEZaTFRxeFN4a2ZqZW9mLzBZdzZ3TEczbjNhZTM3TmRZSDFBYXNEOXptL0k2ejJ2eC9IajdrVGY4NHVqSjdJVkNmWEJidnU1RExMT04rT0F0Y2FFZEE2SFAvVG1vY0p6dEVJWHl4dXlMME9LSlNYUXJFRzhHM0QvNkNIQVBpRmYrQStLY1dGOEJPYU4xeDFjVHdGYWxBY1lPRW1CMG9BTWdlZkV0SzJHaGhla3dHRlh3aTk2a1V4YW1OTDlVc2dIdEdRcEpVdFowUW9xckUwNEY1SWxyOFF4ZklEWEp1Q2hsM3NEeVExZ3dmQnZxQUpyeWtkeUM1em5BTjM3VW5FK3ZlYXlobzVtL0d4L2VJbEh2bUE1cXRzYnFXcXFEVlRwS1ZTZVpQUXIiLCJtYWMiOiI5OGJjNjkwMGVmMzkwNmIzZmUzYmUwY2Q1Njk2ZWZhNjZiZWY3MDUxOGY5NTNhZWQwYzQ1Y2M3YmM5MDIxODEwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144620633\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-652715893 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-652715893\", {\"maxDepth\":0})</script>\n"}}