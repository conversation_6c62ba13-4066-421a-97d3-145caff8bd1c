{"__meta": {"id": "X3d8da7400370961523ba6cbdc8a7b316", "datetime": "2025-07-14 22:40:37", "utime": **********.745943, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.290862, "end": **********.745957, "duration": 0.4550948143005371, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.290862, "relative_start": 0, "end": **********.660288, "relative_end": **********.660288, "duration": 0.36942601203918457, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.660297, "relative_start": 0.36943483352661133, "end": **********.745959, "relative_end": 2.1457672119140625e-06, "duration": 0.0856621265411377, "duration_str": "85.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48521352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02281, "accumulated_duration_str": "22.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.689441, "duration": 0.02143, "duration_str": "21.43ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.95}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.719625, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.95, "width_percent": 1.973}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.733259, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 95.923, "width_percent": 2.455}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.735183, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.378, "width_percent": 1.622}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1297473796 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1297473796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738996, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"14\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 224.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1031880840 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1031880840\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045804948 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkRGby93VGF0VkFJYmZON3JkZE5EQ0E9PSIsInZhbHVlIjoiNVhpclF3dG9VekRlOXg4L2tqY1ZmdzFLUkJKRE5ieGdoQVRpa0ZNVGtZUm1ZMWxqUmdWUmJEY3ZvWFY3M2NqTXJuQVYveElDWkJoZSt3V1MxZGlqRW9WQXEvQmc5Q0ZoVDJyN1Y5cU5ZVHpvaUhtZzNCa3V1RCtqSFlERzVxRGhEZFUyNS85dDUwdnRVTHdONTFRY2Rhb25mRHhiNG9od3FiR0tEbGdWemZDTGg0by9mS2RJTmkrN055Wi95QUFHMzBPTmJ0R3oyWUlpeXlwNVRzM3hhTUpuL3pxYmo0QkNCNmJESjBnSUwxVFAzcC9pRE5nVDJYVURVZ1BtaVAzYWpBWjRaenpURmFzSUdrVWx4TmU2aFB4VThWTFJmd3JyUmVmQWJPUDZENHpSa0txMXBLQVBuTS9OUjRxVGlTM2h2Yi8zdEtuZVNuaHdIRkdLMTJiVkFPRWRrQlVncXl5U0JxNDliSWd5Sk1hY0sySStzaXV3N0pNcXlSem5zZ2xxRk9FREpCTWhZVkJVbjR0Z1ZzWkw3Wkx6bSttZGRWd3YrSjhQTXNnNWJkTTRwSUNUT24vaWRlM0ZtMjdQQ09ueGtHUWVDL0ZSOXJGS0xPVmdzS082d0tvKzJMSzRVUUdyZ1lNK0FOdy9BVXVSUEtEbUxRcHNZc1FPZzNSY2VHckkiLCJtYWMiOiJkZTBiODkxMGFkNTRlMjhhMzQ4MzNhZjgyYjdjYzE0NDMwZWU0MDVjOTk2ZjZlMDVlODZkZjE3OTJkNzU1YTMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9oNjFxaXhtL3l1MlB3TkF6d1FhNHc9PSIsInZhbHVlIjoiTitwa3RDOE1XZkZqRGlwUjhrMldRMXd4VXlOTUU5T2JZT0REdFd2TTlZdmtYMWNwWlpFZnYrWDArTG00MjdUZkErTFpiSjdscVJCZHNRNHZsYm5VRVUzUEVKVUN6SkVXSWZSbTRxbU9FaU9JTSsrZ3RIVlVVYVVWd3hLRk5vZDRoUytDZEFhMGxzM3RyVlREa2xUOUFHNnUrL09CQnI5Njhyek9ncXB4Q3RWcjdQb3dWSXR1YVhnRmNkZTdRZmFCREFzMnNYMmFodVdZYlk1QlVtclVIbllUcy9NTDNPakRqOUd2elp5UkZaOGpnVldPWG5rZGQzVGp4VjJmakQ5cWQrTENybFZDbHo3UDIwN1NxT3JjeUN6NzdQNE9mZzByaEQ3UHZQMGdyTHlUK1lmUHNETDB5RnFHYUhSWThpeS8xbTJDNUFJN0l0RGxqbzc2aHU3S3BweDBsYUFVVlJxSjZCQ1RwTTJLNzQxRW5YWGNQdDlqSHNLOEtxOHBzYjZ4aUJIMTdHNktXd2FxREgrN2tDb0xmb0hZa2JPNUZTTGRlZ250Qm9hdWtmS2NuZWJzcDUyc2ZRSnJQZ0xWbTBIOFlpT1BmMGtPRVZ1K3lacm5HWEhSTURmV2NMOWFubVNlc01WNisyMDZQTXBGckpJUnVzU1gycDRIUTZjMjlCTGUiLCJtYWMiOiIyYWQ0NDZlNTg2ZjA0NTg0MGU0NDgyZjgyMmM1M2UwZjRiYTgxM2NlMmMyZDEzMGY3OTAwY2Q0Y2NkNjY1NjYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045804948\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1890929160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1890929160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-808247613 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZCYWQ2U2dIVk9VajhSeUpmeTg3UlE9PSIsInZhbHVlIjoiNS9nMlJGUTlueGFNWW9HSDhtRzFCSnJlcTRuWHRqRjdHTTBSQ1FLcTI4QmFtT3VhREc3aDVjNkVyQWcyMTBSVlFKUlZkeUNDODMzRG1INGxDcHVkM3JiTWVpODZqSlIwMHJVVGpTL1ppWlFDRjFPRlAvYldPQVB5Q1BRbG43ckxnaE5SNUdHMktteGx5c1QraTdGbXJmTXVUU3M0STNGZDBDamhXTERidEIwcTM1bEYvMnhYUmo3NU9DUVZDNFg3bXZNUGFZQlh2U084N1dWT1ZmQXc3dE5uS0Q3N1RVKzBFUUVvT1ZlMEhTOEpHQVdrMyt5NUhjTHdZbnJNMFI5VXp5eU00dGUyelA4bkkwb3JSdWxyVGxocGdxWUR3QXAxU2RpaTlNQmNrNjhlWWNYSGw3QTYybjBoMnBhMkhYM1FhVGR6bmpGeE1USlcyNGtFRFAyazRSK1NIb2VaRHBjVzlQWVpwZHArTko4QW52TWdwRUNNZVBzWHRGeWl3VVRHSVNhNDdJN0xMbkpwNFQ4Y0J2ZkovVFJKSlYrenhlSHhyQXFlY3h1bU1TQ1ZBRjhiOFFLN3FpYkUrY3dtYmtMNzZlZmg2QklkQkUrSmh0L0kvQ1dqNjRJMUJLY1JBc2dza24yRkQxMzBRY1pGVjhtVDV0WkhrVk1LWGc0MkVTUEEiLCJtYWMiOiJjNzY3ZTUxM2Y0MzczYTU1MTliYzRlYjBmMDQxYmM1YzcxZGUyMGIwODliNjg0NGE0ZmFiNWI3ZTYzZjE2ODgzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpjMGhIc2JvRzFRRmhDbUd3d3pUSXc9PSIsInZhbHVlIjoiZFdmVTZzdWI4ckgrKzNQUjB3S3hEdW04VmpSbktvak9sMGtiazZEc1MzcHVNOThtU3Fza3F4eHFpQk1GNmtUUkdMMTRZY2g3MmdqcDR6Yi9xQ25HRi9PL3FvQXBlalBrdXA4NVJQWWFVa0ZXY2dXTFlDN0p5VjhrU3RVYTUzNXI0VCtYU0tZQzdmbkFOcWxLSXVRdERuaVlOTGRseEtNbnVvb3NkWU52SHROQVllZ3NZZ2xxZnVDc254L1hYOHNDUGRvTzY5UllEbnJHMU1CcE9vSmNLdXZySzdDcktxWlhQODk3c1ZmQmQ3bnZvRkpWTXMzZHZLSWI5UzUreEp1S1VEa0ZZbmV4YXUvbjdmSzBGU3ZMWUNiWlMrNWxiZ21wbm50TUlWM0plREtPRVY1VGdGaHdpVWFHbG1oY25qc215Z0psZWZBcFdwYXhTU0VjdUlManFJMjJTYUE1Nmo5YmMyTlE4YzVUZENRY1lqTmR3em5lb1JvWXNkaUtxZklaVzl6ZG5Fc1ZkdGN4R0VtamZMOU95YnVleDBvdXlrcWN3cFZrNjN1NjBkMWdJWkFrTGVXNTc2UlNFdUlNeHlwN3loUlJodDNtakszYzdWdHdqZ2U5UmxuRU1CNWNOTlVIa01BekoxRjQvb3JlL3U0NFVUcU04VUlTUlg3TUwxZnMiLCJtYWMiOiI4ZmQzMDE3NDU0YmY2ZGI1Yjc4MTE3ZjdmOGNkOTU0ZjdkZmFiNWM4ZGRlNDY2OGQ2NDViMWFjYmVlODA5YTYxIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZCYWQ2U2dIVk9VajhSeUpmeTg3UlE9PSIsInZhbHVlIjoiNS9nMlJGUTlueGFNWW9HSDhtRzFCSnJlcTRuWHRqRjdHTTBSQ1FLcTI4QmFtT3VhREc3aDVjNkVyQWcyMTBSVlFKUlZkeUNDODMzRG1INGxDcHVkM3JiTWVpODZqSlIwMHJVVGpTL1ppWlFDRjFPRlAvYldPQVB5Q1BRbG43ckxnaE5SNUdHMktteGx5c1QraTdGbXJmTXVUU3M0STNGZDBDamhXTERidEIwcTM1bEYvMnhYUmo3NU9DUVZDNFg3bXZNUGFZQlh2U084N1dWT1ZmQXc3dE5uS0Q3N1RVKzBFUUVvT1ZlMEhTOEpHQVdrMyt5NUhjTHdZbnJNMFI5VXp5eU00dGUyelA4bkkwb3JSdWxyVGxocGdxWUR3QXAxU2RpaTlNQmNrNjhlWWNYSGw3QTYybjBoMnBhMkhYM1FhVGR6bmpGeE1USlcyNGtFRFAyazRSK1NIb2VaRHBjVzlQWVpwZHArTko4QW52TWdwRUNNZVBzWHRGeWl3VVRHSVNhNDdJN0xMbkpwNFQ4Y0J2ZkovVFJKSlYrenhlSHhyQXFlY3h1bU1TQ1ZBRjhiOFFLN3FpYkUrY3dtYmtMNzZlZmg2QklkQkUrSmh0L0kvQ1dqNjRJMUJLY1JBc2dza24yRkQxMzBRY1pGVjhtVDV0WkhrVk1LWGc0MkVTUEEiLCJtYWMiOiJjNzY3ZTUxM2Y0MzczYTU1MTliYzRlYjBmMDQxYmM1YzcxZGUyMGIwODliNjg0NGE0ZmFiNWI3ZTYzZjE2ODgzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpjMGhIc2JvRzFRRmhDbUd3d3pUSXc9PSIsInZhbHVlIjoiZFdmVTZzdWI4ckgrKzNQUjB3S3hEdW04VmpSbktvak9sMGtiazZEc1MzcHVNOThtU3Fza3F4eHFpQk1GNmtUUkdMMTRZY2g3MmdqcDR6Yi9xQ25HRi9PL3FvQXBlalBrdXA4NVJQWWFVa0ZXY2dXTFlDN0p5VjhrU3RVYTUzNXI0VCtYU0tZQzdmbkFOcWxLSXVRdERuaVlOTGRseEtNbnVvb3NkWU52SHROQVllZ3NZZ2xxZnVDc254L1hYOHNDUGRvTzY5UllEbnJHMU1CcE9vSmNLdXZySzdDcktxWlhQODk3c1ZmQmQ3bnZvRkpWTXMzZHZLSWI5UzUreEp1S1VEa0ZZbmV4YXUvbjdmSzBGU3ZMWUNiWlMrNWxiZ21wbm50TUlWM0plREtPRVY1VGdGaHdpVWFHbG1oY25qc215Z0psZWZBcFdwYXhTU0VjdUlManFJMjJTYUE1Nmo5YmMyTlE4YzVUZENRY1lqTmR3em5lb1JvWXNkaUtxZklaVzl6ZG5Fc1ZkdGN4R0VtamZMOU95YnVleDBvdXlrcWN3cFZrNjN1NjBkMWdJWkFrTGVXNTc2UlNFdUlNeHlwN3loUlJodDNtakszYzdWdHdqZ2U5UmxuRU1CNWNOTlVIa01BekoxRjQvb3JlL3U0NFVUcU04VUlTUlg3TUwxZnMiLCJtYWMiOiI4ZmQzMDE3NDU0YmY2ZGI1Yjc4MTE3ZjdmOGNkOTU0ZjdkZmFiNWM4ZGRlNDY2OGQ2NDViMWFjYmVlODA5YTYxIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-808247613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1708399081 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>224.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708399081\", {\"maxDepth\":0})</script>\n"}}