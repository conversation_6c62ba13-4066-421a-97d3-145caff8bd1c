{"__meta": {"id": "Xbd87797cc8eb7255f6ed5c8f25ca8c7a", "datetime": "2025-07-14 22:28:24", "utime": **********.237187, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532103.773064, "end": **********.237201, "duration": 0.46413707733154297, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1752532103.773064, "relative_start": 0, "end": **********.160238, "relative_end": **********.160238, "duration": 0.387174129486084, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.160247, "relative_start": 0.38718318939208984, "end": **********.237202, "relative_end": 9.5367431640625e-07, "duration": 0.07695484161376953, "duration_str": "76.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48491968, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00692, "accumulated_duration_str": "6.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.194418, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 35.405}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.205464, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 35.405, "width_percent": 8.237}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.219689, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 43.642, "width_percent": 10.405}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.22185, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 54.046, "width_percent": 5.347}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.226894, "duration": 0.00281, "duration_str": "2.81ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 59.393, "width_percent": 40.607}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1074233168 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1074233168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225912, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1893103827 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1893103827\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1083956898 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1083956898\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-416712571 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-416712571\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-955488015 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVRcnNlM1BQMmYyZ2Jha1RuRjZVS3c9PSIsInZhbHVlIjoiRG9tdmVnWW1veWhlYTlKaHhmVkJhVGM2MXkrMzJ2NWxVMDk2UllEaWhOVHZmYlRNMHBxQU50NUhNOFo2R2NpcjBiZCtlcXVvN0V2VE9RQlhxOWc4Y2pXQlBjcHIweGJtSVFiTkN3VFBOaUxKbm9CTmd5dUZyamhyNjQ0NUo4aVFvREd6MHh6TUo0L1B2OHNPK2E0ZTE0cVU0OS9hWEZ4R2NudmFOOTgwOGYrUG02dFExN2ZvSlgxYmNvUGUrSENxZ1Q4VU5vRFg0NTVyK0h3RnUvYS9TeE55eEZRWkVQN1ordk1RWE9HYWY2SjdpZ1V0akhPTUo3bDFKODcyQ21qSDlrZVNxUFgvaFRSUkhMd2NXa2Vpc2F1bEJWZFdVQUNpK2RXeVFqdGxnOFkzdmxXdkRQd1REcHBtc0xiblFldURsNW1wRGlqejM3TGxBQW1yRDJ0NHpkQXRBMXJGcjZqSDlaZzVJLzNseTN2MnFuM3A1eW80Q254MnhNVFZ5QVQ2LzNMR3dhalFDWVpkczRHMFRuWEZEcXhxYmdXUThCS2toNHlaTzFkQTBQKzB4QUhkSXNJTm1kZld3MjA0WERCS2k3dlFER1FId1gvdEpTc2RtMjNPbThUUGFqUWJ3MTVzVlNHMUtBN1R4NzlsRk5hRXY4RjhRVHhtTTJRUXg1czEiLCJtYWMiOiI3NGY0ODVmZWQ0OWZjZGRmMWFmMTFmOWQ0YjVkZTk5ZTI3Y2YwMDg5MWQ0ZmFiYzZiNmE3ZGRhNzdjNDIxYzQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imc4blEyTk1JY3YyckpjL1pROGRuWVE9PSIsInZhbHVlIjoiWVQrS3ZvVDN6YVA1MU1sY0dLVWo1eXBSQXozSjRkR2J2SlF6cEwrKzU3VGlXN1FmWjIwTnR3RmpRY25tcjR5NWw4bVFIZXRRYm9GYTRuUndVVyt1RnVIKy9HZ3RQVlRZeTZYTDdCWHIzVXBHRlovYUFwWFE4QzlVM1dTdlRtK0lUTk9WeUU1TGg3THdMNFVJOVUzMk14WDZ3ZjhHZFUvcmE2QXh4UVloUFdlTFBCalRHL3lFUHBKMk5JSTRrc2NCUDk4a0prRlZQNzBZQTltK1E2cjhKQXI5M2ZiakdYV2RoUnpuQjhMRjd0b3h0V0VkaVN3S0h5OGJpcG5GWHR3ZEU2dXhqbEQwckthQUJmZFIwVkNsemg5YWNMWGNSejU4ZFdJWWNNQnEzQWZ0dGRoS2hrYlphMUdCK0dYWFNZVTZmNlgyWVFxSzFhN29EM3NZYUplaG4wRzNCY3RreldzdzVyQ0o4dGo1M1ZCM1hEQXlSS0JzQitGLzBQU2JXOGpIMHdhcXZBYlZoK0JaVEh6WmE1YUc5RjIrNFZFNDBqYWc1M1lLYTZRUlpqcllpSUo4SGtTbzNsbjV6akZCZG5hcjdqMm52b0YrN1N5aWpHcENKQ0dNK0lTQmpWbWNkemowQmJDNlRwVFhpejhYcnNXNlljVm5FejJrVVRkUU1yTkEiLCJtYWMiOiI1NDViYzdhNjcwZjMzMDM4NGE2OGYyOTBhYWZkODFiYTFlMzQyNDg2YzUwYjM1NjIyMmZjMTRjYTlmMzhhNWE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-955488015\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-71796745 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-71796745\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1736927537 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:28:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktFSisvZm1NYm1sMHNYSWswTitzTFE9PSIsInZhbHVlIjoicnNsUzdGSlhucVhJL0o3MXRWNjlPZk1tQVlUMkV5VFhIQTBRU3lrRGlpMFpHN3RKQ3pXOS9MNVJIOStNYThyTEdIbURtZWVWNzNBR0paTW5XMVlEVVA1N2ljQlpUZktIS2FKSFJFYWt6L1BYRld0eDdrTGtna1ZkeHVnMkNTY2ZPRkFETkJlU2JCN0k1TU5OTitadVZYT2JtV243Wjk1Zlp1R2g3RkJwTFRXNm91WFBKaGxCT1JtQWVBbis4WUZtZVZDNWtrQ0htK1hycnorL1pmckhJbmhoS3F3Q2VMeERjdkJTaHVtQlV0QlRUbkNHSHZ5eEpDQWVTbko0eGZEUlREOXpFNGtvaG5VQ2Yydml4eXpsdXVPYVdubXpYN1NRV1FLcm02NzJpN2F6UmRtNUF3WGRlZDN3emZyWnVCOGdDOHNoRTZFREpCTzhjU1M1bVdNaFBSaW1IRTlLbFJZR2d4aE5DS3lMZlVDR3RmNHRaK2FvKzgvZ1A4MXl5MlNiSWd6YWJvTG84UXZVQVlnM1VMMGlLWitkVk1aek5PQjRwUlZuWXc1M3N2RGhvZTdGekNYOVZCSXV0SlhibjVRVHo4UFlKYTV4bytUTWdxekdqYzM1TlhpZm5GTTZaQzloQS9oRUJtZXIzbGRYekd4dXhFelltQ2lHTmMzSzhBQmkiLCJtYWMiOiJkOWU4MzViMjMzNzk1MWUwZWM0MTY4M2EzOWM4ZGM5MWMxZTJjODZjNWRkZjRkMzI1NjUzZjdmMTQ3OTU3YmViIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkE1V2hRVWU4NU55OWFGSzIyK0hPQUE9PSIsInZhbHVlIjoiZnBPb3Qzc01JNFR1RXJkWlBZRFViSzhvUytXZG1ReWZxUnF6SG5hWUFRMG1WTEE4R1hVcFh0L0dNdU9QVnRCd1kxWVRCZHJsN2hnVElWNEFkMkcxeUsxTTZNZ3F0VXVBczBXWXVJb3lwZUlNRmtkWlphTnZkQnlMOXBvNFJVV0dhQ2ZUb1lEZjRiTW12MllYTGZkR2duTERiUmhvNnRZT1RaNTAxSk1ZZVlPVVYyTnpSUkhhc2lFckZmQXBrRmZ2R1BobVN2M21HaVE0L0wyVGhhRjhyNFJWY0lxampGbHd2L09QOXZDY2ZNV2FmK2tqdmZhUEdTU0VxSUtyOTluNGFTMnE2VzNaSzVxQ21oY1RpUXBKajE2UUVaZTlMSGR5b1V6dXFySzBKUXdkRmtLNGY0M2d1QmkwMHFVaTR4Vk5SK1RCaWoxUmJSMkJERW10cEVROVN2MEFTNlFwS0NPT2hHeWVGTVRzYWptc1lEYnd1QnNCV1lCNGJCWXA0ZlhyZUpjUVEvREFzK2lJcFRNMFRHV0F2cHU5TWdkTlMxSGlyNUo0UVVjR1ByclFTWm8vVVlWTVFuV3lQODgremJqdGhqSkd4UEU5bU9CTjBEdTBwOTI5bktRZEMxbVUvTTQ1Si85TmtvSW9yTWsyZEZYeloxbitZSFNRQXpLdGlkWlciLCJtYWMiOiIwYjFkY2JmNjY3ZDViYjY3MzUwYTBhODM2ZDAwODVmODE5ZWEwMzA4YzMxNDRjODYzOGQyNjU5MTlmZDYzMzdiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:28:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktFSisvZm1NYm1sMHNYSWswTitzTFE9PSIsInZhbHVlIjoicnNsUzdGSlhucVhJL0o3MXRWNjlPZk1tQVlUMkV5VFhIQTBRU3lrRGlpMFpHN3RKQ3pXOS9MNVJIOStNYThyTEdIbURtZWVWNzNBR0paTW5XMVlEVVA1N2ljQlpUZktIS2FKSFJFYWt6L1BYRld0eDdrTGtna1ZkeHVnMkNTY2ZPRkFETkJlU2JCN0k1TU5OTitadVZYT2JtV243Wjk1Zlp1R2g3RkJwTFRXNm91WFBKaGxCT1JtQWVBbis4WUZtZVZDNWtrQ0htK1hycnorL1pmckhJbmhoS3F3Q2VMeERjdkJTaHVtQlV0QlRUbkNHSHZ5eEpDQWVTbko0eGZEUlREOXpFNGtvaG5VQ2Yydml4eXpsdXVPYVdubXpYN1NRV1FLcm02NzJpN2F6UmRtNUF3WGRlZDN3emZyWnVCOGdDOHNoRTZFREpCTzhjU1M1bVdNaFBSaW1IRTlLbFJZR2d4aE5DS3lMZlVDR3RmNHRaK2FvKzgvZ1A4MXl5MlNiSWd6YWJvTG84UXZVQVlnM1VMMGlLWitkVk1aek5PQjRwUlZuWXc1M3N2RGhvZTdGekNYOVZCSXV0SlhibjVRVHo4UFlKYTV4bytUTWdxekdqYzM1TlhpZm5GTTZaQzloQS9oRUJtZXIzbGRYekd4dXhFelltQ2lHTmMzSzhBQmkiLCJtYWMiOiJkOWU4MzViMjMzNzk1MWUwZWM0MTY4M2EzOWM4ZGM5MWMxZTJjODZjNWRkZjRkMzI1NjUzZjdmMTQ3OTU3YmViIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkE1V2hRVWU4NU55OWFGSzIyK0hPQUE9PSIsInZhbHVlIjoiZnBPb3Qzc01JNFR1RXJkWlBZRFViSzhvUytXZG1ReWZxUnF6SG5hWUFRMG1WTEE4R1hVcFh0L0dNdU9QVnRCd1kxWVRCZHJsN2hnVElWNEFkMkcxeUsxTTZNZ3F0VXVBczBXWXVJb3lwZUlNRmtkWlphTnZkQnlMOXBvNFJVV0dhQ2ZUb1lEZjRiTW12MllYTGZkR2duTERiUmhvNnRZT1RaNTAxSk1ZZVlPVVYyTnpSUkhhc2lFckZmQXBrRmZ2R1BobVN2M21HaVE0L0wyVGhhRjhyNFJWY0lxampGbHd2L09QOXZDY2ZNV2FmK2tqdmZhUEdTU0VxSUtyOTluNGFTMnE2VzNaSzVxQ21oY1RpUXBKajE2UUVaZTlMSGR5b1V6dXFySzBKUXdkRmtLNGY0M2d1QmkwMHFVaTR4Vk5SK1RCaWoxUmJSMkJERW10cEVROVN2MEFTNlFwS0NPT2hHeWVGTVRzYWptc1lEYnd1QnNCV1lCNGJCWXA0ZlhyZUpjUVEvREFzK2lJcFRNMFRHV0F2cHU5TWdkTlMxSGlyNUo0UVVjR1ByclFTWm8vVVlWTVFuV3lQODgremJqdGhqSkd4UEU5bU9CTjBEdTBwOTI5bktRZEMxbVUvTTQ1Si85TmtvSW9yTWsyZEZYeloxbitZSFNRQXpLdGlkWlciLCJtYWMiOiIwYjFkY2JmNjY3ZDViYjY3MzUwYTBhODM2ZDAwODVmODE5ZWEwMzA4YzMxNDRjODYzOGQyNjU5MTlmZDYzMzdiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:28:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736927537\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}