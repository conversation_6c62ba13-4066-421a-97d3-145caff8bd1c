{"__meta": {"id": "Xb602e65bf15e7f417fb6c2849b7ebff0", "datetime": "2025-07-14 22:31:04", "utime": **********.546865, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.074854, "end": **********.546881, "duration": 0.472027063369751, "duration_str": "472ms", "measures": [{"label": "Booting", "start": **********.074854, "relative_start": 0, "end": **********.455941, "relative_end": **********.455941, "duration": 0.381087064743042, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.45595, "relative_start": 0.38109612464904785, "end": **********.546883, "relative_end": 2.1457672119140625e-06, "duration": 0.09093308448791504, "duration_str": "90.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46018112, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02374, "accumulated_duration_str": "23.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.491503, "duration": 0.02172, "duration_str": "21.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.491}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.522392, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.491, "width_percent": 1.98}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5324821, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.471, "width_percent": 3.538}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.538894, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.009, "width_percent": 2.991}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-517785198 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-517785198\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-904174741 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-904174741\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-919153159 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919153159\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-67367743 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532259094%7C3%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlNuWTFXeS9wOTZJWUJ1N3cxU0ZjYVE9PSIsInZhbHVlIjoiMXllZG94WnBiTHNMaHBxTXN4QTN4cjJkV3EwSEVQRVdvSUtMNHFUdHRIa3RxaWZTRUhCUXJGVUl2YzVvU2FxcndvRHpPUXhLTExXdzRPV1dRUnhMMm1QTUp5K1dkeVBuazZpamJienM2eEc4ZmpweVo4YXNDa1RDbFFhdE83UnB0TmgvOFBMSlNNbG1YM0VDWGFVMjdSUjRONng1TkthY1dqZWFzOXdmbGNnZDNFNUFGRC9GYzJvNTkwdEszRWFObm42UVB6OEJtb0R1cTQrRERRYzRZM2NtMFVPMEpJdmFIaHF2Ry9uSFcwTVh5UUwrT1ZPcytqUXJNRkJaclRvNWlybzJqdDVzWDlYVXpOVnBoa3VCNXNzYm01Mk5xQktBdEVzbWtCbmxCaGtuL3Bod0tlb3FOZ0lWT0JEbmc2ekFPbjk2dkQvUW40TEdWT2F4cmtZYXRrNHRid2JaOENIU2lwRTVOREdVVG4zOUQ5bmEzazRudWF4SzV4dUhaNHEzTTNwbGlWdnVWb1l5T09ZQVdMc1JMT1UyUjQ0K3pwa2JWY1FsbUh3QU5WTTc3RUdQbjlmQmVpS0tCRGFOZkpKQ3lkc1N6dDVsQWRTUC9za3dwem0zVVEzb1gxRHBYcThoN01OVjRKeUIreEliVnZBdE9ZTU5adVBJUVpDa0lHUzUiLCJtYWMiOiIwYzc1NjdhZmRhNGVhMTEzODQyYjAyYmJiYzA3MTcwM2UwZWUyN2QwMzg3YWM0YTJlMmI3Yzk5MGQ2NDUxYjM0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImUzLzB5TDFQcFlGditpUDVNQk9Obmc9PSIsInZhbHVlIjoiSzFZTktlMXVJYTlWc2l0STYveEhSclZPZENad3ozRDV3dy8zU1ZMZmF1Mm15TndMQVJrQXZxNkR5ZjhSV0JEUEdVM1F1Sk41TjRsNTZZdHJ1Q0hhRVYxZGtSM1p2RXFRYnlQZkM2NE5Ec2g4Qy9XanRKblYvV1lERlRrTEpZNUhzY0JjRE4xNU9Mamt0UytvU2toVEdIRktiNmRGUVNYUXRZOHJoRmVMUUh1NHBpa0hLMkpZYjhneG1BdGluREFtc3ZWeFFPNFpMZGVIVkxsdGZEQ0ZsWXc3Wk0rNlJEbENabytNUmV2b01sak55cmxGYW4rTnVVczFId2xiMGpzTitKZ01xTWVFMDgvWWQwQXMyc3VMT0h5azY4UStwTDdVbXJVbHlOcDR5cHhwMndwckhzUkU1NmNBMDZsTjZHaS91d2JBaklyNm1lNXVPT0x0Q3lVV1k5c3ptYi9FUXozTnlCM1JPbXExa0p4dmpkSmxqbU5udHhWeWNyRTltWStNTlBITTFsdWpkcWJWdWwwVE9QWERKQU1VV0JJdXhhNlRQSXdVYm9yVmtQWWFXTUsyN0dOTG54dE03QWFJYkI1Z1VDOG5EV1BZNUtiV2ZtcGNobGV6MnZnUzVVZWsvbmlZSDhnSW5oT0xJeTE4ZXJiTzBOcENPdjFRK0ZmeFlhb2QiLCJtYWMiOiJkYTZiNmVhNmYxMmY5YzFlNmZiYzg4MTMwYWE3MTNlZGE2MmIyNjcyYzE1MjkzMThhNmJlNjM4NDk5NTljMjYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-67367743\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-53514120 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-53514120\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-635693316 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZYQUk2OGJyYWo1UWNKQ1AwcmpIbUE9PSIsInZhbHVlIjoiYzdoS2tsbms0ZGhGbzluc1VSeStMSVprSkJ0d0xtZVJIdlVHREF5ZFJIQTFYK2dROURLeWRRcmJHM21LZFU0ckJudmh0U0VVSVM3RG41RGQ4MEJ0c0dJSHQwZmozaG9qWVZJSGxXRmpyVXY0N2JTT3dGaFkyMHJKSXRTYjRYUlUybzB4YzM4TnNmclZaOHRNNlZMUzFBSVNYc0JMMzJCc0xXSWovZ3ZTV0tjUjNjUEs0Qm1sSEZUWkVUdFd0TzcvQng1N1g2RklVRmVhc1JReVdWMUxiNFBCVXZxV2x5bnpXckgrY3VNc1lOcVB6Nzhmb0oveFU3NzZNVkZTSzA2cE1sZHJCSnNrM2haYVlMeEFJMXlLQ0hhMEtqdmJ0eER3VytTM0VKQXU1ZVBaRE1BdTFlQjY4VitzbWNjN1hsL0RoYXJUTGxBNlVkSlBVWm5tclNva3JBQXJ3c21aREFtZmZhYy9RNFdXK3RkNmhNZW1hcDR5aWtxKytUZVdialRrVE9RaERQZmlyTXdZUWExZTFIR2lPRVBsYjhpSVdDTzgvNFo3Q1B0ekhHOWl3NUJpcFE5cTdkaDNTNkVMS1N0dUxtNDdCckpOUkd4R2I1aWpKckFLRTFTMGpQQm0rRFZUY2p0endDc3J5YjJtTk1NZmNkMXg4N2RjaTk1L216YmMiLCJtYWMiOiI1N2ZjOGU4MjBjODcwNWFiMTMzZDA0NDIwYzBkOWViYTVjOTcxYWNiN2E1MzQzNmQ3NGFjMWFhZGI0OTM3YTNlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im9zZjZtc2JKazBxOGR2clcyUDZyNlE9PSIsInZhbHVlIjoieE1GQ2R2c3o5QVdURlZIQ0UrajFUTktZWnllTy9mSi9QV0sxdFFjM2NyUjJNSEhtSmVkd3pPdHpYelVLQ0ZuQzg5aTFpUmNDR1FZZjZTY2U4bms1NUhVWGR1UWZZYzhpMDhsbGhBTnluZzRnSTBodHZISUw0TGM3b1BXd0toOG1SS0dCc0pZRXVMSmExTWVid0hnM2VFRHBERGFsaE1NY2ZuR29BTFBpc1FGbE9hbE5EVlgxVGNVajJwQVRRZUFkYmJpenBUTDROeUQxejVJakcrdVlRb2l0TG9kUSt4NktMb1lZY3NKdGFmbXIzak5hOVZyaGNKU2tRc3V3dnhLK1ZvdkZ2SHRXUXkvNGdvRU4vRTFiU2x1NzZVcmFnRzJqSGVON2d3Und2NWR4VHdrZkJuNzJidUdZdkxRbDh1OGR5Uy9teXo1eXQ1b1NnL2xwN08zSHNJeVp1RkJFNUlWUGpvakxobFdyQTB6cGpHdFBXWW5DUGV1bkdRdVNnVE9sSUhmcWxiSFFaYmhyU05RUDJJNXBPTjV3ZmZmZ0ZPYTZDUGc0QmZsa1F3QittZWlrMHVkZW44aG9MU2FabnhOMmtuWnRWVG9KWTVGNGMzeGhxVjN2S0dmVkZUWmJ3TXZVWVdxOHg5S1AySW5MOEdoZHNDakVDMjdyemFVbTBZa3ciLCJtYWMiOiJlMzYxMDIxZTYxNWMyZmVjODY0MGRhYWVlM2E0NmZjNDYxMzcyZjFkNWRjNWY4YWU0NzRjYzAyZjllYmExNTk5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZYQUk2OGJyYWo1UWNKQ1AwcmpIbUE9PSIsInZhbHVlIjoiYzdoS2tsbms0ZGhGbzluc1VSeStMSVprSkJ0d0xtZVJIdlVHREF5ZFJIQTFYK2dROURLeWRRcmJHM21LZFU0ckJudmh0U0VVSVM3RG41RGQ4MEJ0c0dJSHQwZmozaG9qWVZJSGxXRmpyVXY0N2JTT3dGaFkyMHJKSXRTYjRYUlUybzB4YzM4TnNmclZaOHRNNlZMUzFBSVNYc0JMMzJCc0xXSWovZ3ZTV0tjUjNjUEs0Qm1sSEZUWkVUdFd0TzcvQng1N1g2RklVRmVhc1JReVdWMUxiNFBCVXZxV2x5bnpXckgrY3VNc1lOcVB6Nzhmb0oveFU3NzZNVkZTSzA2cE1sZHJCSnNrM2haYVlMeEFJMXlLQ0hhMEtqdmJ0eER3VytTM0VKQXU1ZVBaRE1BdTFlQjY4VitzbWNjN1hsL0RoYXJUTGxBNlVkSlBVWm5tclNva3JBQXJ3c21aREFtZmZhYy9RNFdXK3RkNmhNZW1hcDR5aWtxKytUZVdialRrVE9RaERQZmlyTXdZUWExZTFIR2lPRVBsYjhpSVdDTzgvNFo3Q1B0ekhHOWl3NUJpcFE5cTdkaDNTNkVMS1N0dUxtNDdCckpOUkd4R2I1aWpKckFLRTFTMGpQQm0rRFZUY2p0endDc3J5YjJtTk1NZmNkMXg4N2RjaTk1L216YmMiLCJtYWMiOiI1N2ZjOGU4MjBjODcwNWFiMTMzZDA0NDIwYzBkOWViYTVjOTcxYWNiN2E1MzQzNmQ3NGFjMWFhZGI0OTM3YTNlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im9zZjZtc2JKazBxOGR2clcyUDZyNlE9PSIsInZhbHVlIjoieE1GQ2R2c3o5QVdURlZIQ0UrajFUTktZWnllTy9mSi9QV0sxdFFjM2NyUjJNSEhtSmVkd3pPdHpYelVLQ0ZuQzg5aTFpUmNDR1FZZjZTY2U4bms1NUhVWGR1UWZZYzhpMDhsbGhBTnluZzRnSTBodHZISUw0TGM3b1BXd0toOG1SS0dCc0pZRXVMSmExTWVid0hnM2VFRHBERGFsaE1NY2ZuR29BTFBpc1FGbE9hbE5EVlgxVGNVajJwQVRRZUFkYmJpenBUTDROeUQxejVJakcrdVlRb2l0TG9kUSt4NktMb1lZY3NKdGFmbXIzak5hOVZyaGNKU2tRc3V3dnhLK1ZvdkZ2SHRXUXkvNGdvRU4vRTFiU2x1NzZVcmFnRzJqSGVON2d3Und2NWR4VHdrZkJuNzJidUdZdkxRbDh1OGR5Uy9teXo1eXQ1b1NnL2xwN08zSHNJeVp1RkJFNUlWUGpvakxobFdyQTB6cGpHdFBXWW5DUGV1bkdRdVNnVE9sSUhmcWxiSFFaYmhyU05RUDJJNXBPTjV3ZmZmZ0ZPYTZDUGc0QmZsa1F3QittZWlrMHVkZW44aG9MU2FabnhOMmtuWnRWVG9KWTVGNGMzeGhxVjN2S0dmVkZUWmJ3TXZVWVdxOHg5S1AySW5MOEdoZHNDakVDMjdyemFVbTBZa3ciLCJtYWMiOiJlMzYxMDIxZTYxNWMyZmVjODY0MGRhYWVlM2E0NmZjNDYxMzcyZjFkNWRjNWY4YWU0NzRjYzAyZjllYmExNTk5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635693316\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1248748465 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248748465\", {\"maxDepth\":0})</script>\n"}}