{"__meta": {"id": "Xb95f33cf193ea1f2323dda79b2317827", "datetime": "2025-07-14 22:38:51", "utime": **********.468755, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.031336, "end": **********.468769, "duration": 0.43743300437927246, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.031336, "relative_start": 0, "end": **********.409547, "relative_end": **********.409547, "duration": 0.37821102142333984, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.409557, "relative_start": 0.3782210350036621, "end": **********.468771, "relative_end": 1.9073486328125e-06, "duration": 0.059213876724243164, "duration_str": "59.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46115584, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034599999999999995, "accumulated_duration_str": "3.46ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.44346, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.607}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4557881, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.607, "width_percent": 13.295}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.459445, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 78.902, "width_percent": 21.098}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"originalquantity\" => 211\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1805281903 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805281903\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1691347054 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1691347054\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1910797354 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFtTmxycmN2bFF4dWpXZi9ZV0t0Q0E9PSIsInZhbHVlIjoiZVh0Wk0zOEpSRXlZMlBxTWVSWUphQzFmVGU5MzFUc29sTFlWV3ZaeHdQQnNDRWJjL2Jrb3VKRCs1Wm9kbGJKWFFUMGZwaHN1dk5zMjM0RElUdVpTdk1zT1orTHF3OFdSOXdIakticVk2Nkl2MWtxdXMwWnpZVnhXdHFxaWg0OTdYbVhJY3NVRE1SVXlZWklDdkZNWDdVam1RT1NDT2tiRmZMb3M3UTltcnZYSndVMzJpN0tNREQxQnN1aXRoNEhqeHVIQUdqeTlNVkZVemVSYVNkZjl6UmdLN1crYjJCRGpIeDFIR20zVDhsTHN0eUJLblR4ZW5xdmZnd2xJM0JSWGsrMW5ZcmZSN251SUpWZldVY2IyRHdSUWNQbjl5Z1NqbjhZQ2sxK0E5TFN5d0hteHhpc0NMZjEzVUxwdHFNWUd6aDRSa3FBWnJXLzBzeloxbUViZC84VnVITktrRjBWWjUvNmpvSWVocEpLUldBZk9SaVJuL29yZDdyVjIxVVF0OGVWUExubTIzOEJBTHNwZzl2blY3L2FKeFlPMkN3NHpna095dmdIbjcrQ2JYSjI1eXFSSXZWelg1L2FDOG43K3VNcC9Bd0NPbWdUWjZoVVhkYlJLcDFJMWhSUnpHUW1JYVdZV3Z0SUc4WG01VmRpRnVRbGV5UVlVT1NOWis1ZS8iLCJtYWMiOiJlNWM0YzlhYzczMzFjYTBmOTk0OTQ5ZDMwZGI2M2ViM2Q0Y2Y4NjgxYWVmM2U1NDRkNDAzYjUxNGI4MTI5MDIzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFjdkJMY0d3L2xaTjE1R2x3R0dqbGc9PSIsInZhbHVlIjoiVXBpNHhLdVdUV0F3VTlxMzBiSTFIaVE1RW1lY0hkdzUrUTFJcytBQ2RiYW8zam0zbHZqMHJJaXRVTzh0V1paKzNyRW5OdDUwMkJMQjdDZXF6WjFFMnUwVkJuT3QrUVVzVWZBWlRlQkFwZllTVVIrbDVuVmRJeElhdmdsQWI3eUxtNlE2V3QyMlRLTzRVYnBzaHdLeWdwK0VYbXlEelZ0ZEdGNEw2V0ZqdzUwNmVVeHNYcDZEVU5vYk5OSEJzTS9Nb0VQVEtlR2NIMGdWS3pSRGpQQVF6MllKVmtnUEUrQVpMTWhGT0F3U2w1eTdoRjZidllKaEVkZUl2b2EyVjErZmRFWEtHbFRqcjRFVC9SdEUrbFNteUZ5dXEyTFFwQmlEdVZPcm52SlVDd1F2ZndSRUwzekdSdUkxb3I5YjlaSlcxMGJUVm95bmJsMVp2Y2hBTTNidXVQV0piaHhwdDhzQnkzZjZrdFNDYXdIR3pDcGNJQXJtc3ppTEo2Y1pIS3UzSEdNWmE0RkJML1QrbWxhdTdKcGJSRzRmMmo5M2l4alEvRVpkRzFuc01BdXNVRjJRZy9ld0E0ck5OZXpjYjMwQnBVQUNZUi9uTTZiRXlqWUdDTjRocjRnbXJsYVpoanV2Y3ZsdTlydFpndHpIa3BjZmNMeGtvS2M5YVVadEtJYkgiLCJtYWMiOiJhMzVmM2QzMTk5YTU2NGRjYzZkYTBlMzM1NTFjNzdhZDU0NmM2YWVhNTBjYTMzZWE1NWYxZDMyNDkxOWQ5ZWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1910797354\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1349229321 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1349229321\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1756725928 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:38:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJoMlIrM05RYUo1dGhuai83bkJ5eVE9PSIsInZhbHVlIjoicytDeDlPcUxDMzNiak9xUUVHdWtBc1FiNmlTV0F3QitNWDUxckt3dUVmSVpHWFNPL3kvYXYyejdXeEUwRytzeVJLOWVTc01sWVJlNUFORC9SZi9rZVJxSVJFRUxsaDBpNG9GaDVqbE9tM1JqZnFzK3FhQVdQWXB3RWRKWTFHdTlZRnN1cElnNnJQYXp2bzFaWHJ0MEVCT3ZoVXhhc0E0WDBRVjQrbW9EbXRRSTRNM1k3MjBOODBTMU9pd3hKNEFOdms2QksvaGtUWDJVUEt6RGpIbGU1SmFWMGxEN0ROUEVadjExbHdKYWxOTEp2dVV4UzJYWFQvZ05aZ2NobUhxNkpXUEJBZlpZRlhaODc1VzdQZmRtaUI3ZStvWlFqcXh3dk5jcjZEaTRPM0dFVjhueWk2Z2x5S25aeWhRMm03WVh3andsQnZGNTZHOCtDT2IrNjlGUUQ3bG1VRlN3Q0did0RJNW5nZmJyKzRDVWZJb0dDcUJjaWtiTk5CTWlIWS80QWxva0JwblR3TE43WlZldkpGRnFGVEhJVFpjK3lxSkN3RndLQ1k4K1B2MDY4N1JOYXhHQmg5bmpjckViSEEwNmw4UURKVDllWGMwS2N3S3F0UHpOdEJmVW1neUJVZS92V0dMSVR1eW84d3NLdlNnMDNiOVNyeWY5OUIxNXlhdW0iLCJtYWMiOiIwZWU2MGQyODM1YzljMTBiNGY4YmZmNWZhOGY0MzBhOGYxODJjMWMyMmQ3NDc3Mjc0Y2JkZjU5MDU3NDI5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFBRnM2dndyd2dzKzVBZk8rZlQvTkE9PSIsInZhbHVlIjoiTjE3MHhKL0NJR2xxVlpFbTY1VXcvQWVTWEdUelJ6Y0J3bWhhZ1FaTklBMjNNMmV6NXZFU1VnUkVBWlVtM0lDVnI4Ny9hNjlIdmdxN3I1WERuK0pJVnQ3eWVRYWFqVk5TTjJsbTQ0VDRXNVhXd01ZWDRPcFAwQXNiV216U2xSVmdWVUVQdFo0a1F3dTluZ1ZDSjJnWm1TMjROTHZiNStZNGV4Z0lHYkVvSXU5bW1QNjBtQkE0OVVGd3lIa3lBSmFQcWg2R1ZGZ1MzSWRzMlIrVWk5Zjg1aHkxUDFkVnpwQ0Ezd2dIQWpvU2dWa2xXVEZWdkZpand5WFBVVFN5cHFxUmdQejdiK0ROZkF0d05IektlYndHZmxLVE82L0VrbmxnNWtGTlg1UUpDdHdxa0pNLzRQUDlWQ29TSGpnVEFDZCtGYVNmQ3ZYSHNWMjBQZDZDOFVOVE5YMWtKNmdKdGlFdENYTkJSMnZBbVlKTVg0dm1DQnBHQ3F6ei9mNmtYSEx5N2QzbUxJdFk3bjYzZ3Q2Wm9haWYyekUzdytWeUNBL0h6Um52Y2JQSXR3ZGtrM2hoREZsL1Zmdm03alk5ZTRIdE5hbGIxMFZyREJqR1JPWTA4TTFkamRveXFCSXNlaDhDNlJad0dMQTUwNTlWSXpFZWNNemwvNHFERTNSUDZ4enIiLCJtYWMiOiI3MWVhZjkwZjliNTkwZTU4ZTdkYTZlYTVkMWU2NjgwYzMwOWM5ZmY0MzA1Y2NjYjZjYThlNzYyYTA3MmQ5MzU0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJoMlIrM05RYUo1dGhuai83bkJ5eVE9PSIsInZhbHVlIjoicytDeDlPcUxDMzNiak9xUUVHdWtBc1FiNmlTV0F3QitNWDUxckt3dUVmSVpHWFNPL3kvYXYyejdXeEUwRytzeVJLOWVTc01sWVJlNUFORC9SZi9rZVJxSVJFRUxsaDBpNG9GaDVqbE9tM1JqZnFzK3FhQVdQWXB3RWRKWTFHdTlZRnN1cElnNnJQYXp2bzFaWHJ0MEVCT3ZoVXhhc0E0WDBRVjQrbW9EbXRRSTRNM1k3MjBOODBTMU9pd3hKNEFOdms2QksvaGtUWDJVUEt6RGpIbGU1SmFWMGxEN0ROUEVadjExbHdKYWxOTEp2dVV4UzJYWFQvZ05aZ2NobUhxNkpXUEJBZlpZRlhaODc1VzdQZmRtaUI3ZStvWlFqcXh3dk5jcjZEaTRPM0dFVjhueWk2Z2x5S25aeWhRMm03WVh3andsQnZGNTZHOCtDT2IrNjlGUUQ3bG1VRlN3Q0did0RJNW5nZmJyKzRDVWZJb0dDcUJjaWtiTk5CTWlIWS80QWxva0JwblR3TE43WlZldkpGRnFGVEhJVFpjK3lxSkN3RndLQ1k4K1B2MDY4N1JOYXhHQmg5bmpjckViSEEwNmw4UURKVDllWGMwS2N3S3F0UHpOdEJmVW1neUJVZS92V0dMSVR1eW84d3NLdlNnMDNiOVNyeWY5OUIxNXlhdW0iLCJtYWMiOiIwZWU2MGQyODM1YzljMTBiNGY4YmZmNWZhOGY0MzBhOGYxODJjMWMyMmQ3NDc3Mjc0Y2JkZjU5MDU3NDI5YWE3IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFBRnM2dndyd2dzKzVBZk8rZlQvTkE9PSIsInZhbHVlIjoiTjE3MHhKL0NJR2xxVlpFbTY1VXcvQWVTWEdUelJ6Y0J3bWhhZ1FaTklBMjNNMmV6NXZFU1VnUkVBWlVtM0lDVnI4Ny9hNjlIdmdxN3I1WERuK0pJVnQ3eWVRYWFqVk5TTjJsbTQ0VDRXNVhXd01ZWDRPcFAwQXNiV216U2xSVmdWVUVQdFo0a1F3dTluZ1ZDSjJnWm1TMjROTHZiNStZNGV4Z0lHYkVvSXU5bW1QNjBtQkE0OVVGd3lIa3lBSmFQcWg2R1ZGZ1MzSWRzMlIrVWk5Zjg1aHkxUDFkVnpwQ0Ezd2dIQWpvU2dWa2xXVEZWdkZpand5WFBVVFN5cHFxUmdQejdiK0ROZkF0d05IektlYndHZmxLVE82L0VrbmxnNWtGTlg1UUpDdHdxa0pNLzRQUDlWQ29TSGpnVEFDZCtGYVNmQ3ZYSHNWMjBQZDZDOFVOVE5YMWtKNmdKdGlFdENYTkJSMnZBbVlKTVg0dm1DQnBHQ3F6ei9mNmtYSEx5N2QzbUxJdFk3bjYzZ3Q2Wm9haWYyekUzdytWeUNBL0h6Um52Y2JQSXR3ZGtrM2hoREZsL1Zmdm03alk5ZTRIdE5hbGIxMFZyREJqR1JPWTA4TTFkamRveXFCSXNlaDhDNlJad0dMQTUwNTlWSXpFZWNNemwvNHFERTNSUDZ4enIiLCJtYWMiOiI3MWVhZjkwZjliNTkwZTU4ZTdkYTZlYTVkMWU2NjgwYzMwOWM5ZmY0MzA1Y2NjYjZjYThlNzYyYTA3MmQ5MzU0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1756725928\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>211</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}