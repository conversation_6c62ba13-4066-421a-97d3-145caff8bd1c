{"__meta": {"id": "X30f093781672aabffd475f774df42b6d", "datetime": "2025-07-14 22:40:43", "utime": **********.650821, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.221837, "end": **********.650833, "duration": 0.42899584770202637, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.221837, "relative_start": 0, "end": **********.577098, "relative_end": **********.577098, "duration": 0.35526084899902344, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.577108, "relative_start": 0.3552708625793457, "end": **********.650835, "relative_end": 2.1457672119140625e-06, "duration": 0.07372713088989258, "duration_str": "73.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46101832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01839, "accumulated_duration_str": "18.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.608713, "duration": 0.01723, "duration_str": "17.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.692}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.63928, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.692, "width_percent": 3.589}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6436331, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.281, "width_percent": 2.719}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"21\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 336.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1391312456 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1391312456\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1094244112 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094244112\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1011479312 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlowRyt2Y2k0d1dHNk93c2NBM1FmSVE9PSIsInZhbHVlIjoiVVN3UE9UN0JqMFBjTjN1TUxPYy9aZW9DRGhmbUJQR0tnN1JuVkxlZ2lSVmU5enMxQmxzaFlMREF6RG5sbjdJUVYzRlF1SWFneitwNlFrSm80WWVRL0xlVVhYVTlRVjhZZEpRUk5YbkxkYkE4QXFtNjFGd1dMV3dCTVlSaVM3bTgrazlCc1JwWHd3bXA2NE9nQnBOek1YQlVwUUIxSjBpRFhybE9ITUZiM21uQ3JMVjhYQU5nZncvZWhteG9FMUwra3Rzb2hkaU1oc1hJcmhRNEIwUlFCSkpWZWFpSm4zbmE5Rk5DOEhVSmVrOHQvQ1BFU2tNTlYzM0VJVEFzeEtNM0tTRnBNcmdaYUtwU1ZGcE1OdXlkc1d0YUtuZjhxR3c0YUlRMGhtSVdLd2VZdXFSa2xOUXJyMEtUTlN2VTNvT2ZXbGVQK05YOFZaS0xQZlN3ZmxzbkxDaGxBTzBRSTcxNkZhVE5NaEJHbEUwM2xWMmRRVjVCeTFCSUZ3SHB5dWFwSm5LSC96N1kwMTN0WHJsTWtMdXJqZksreFJHWUZEOG8vWUR3YmlpWTMxeVlYVzRlNWF6SXFudkhTbTMyMWg5WTJmTk9NRDhHbldVLzRhNVdlWkEwM000RVIyeC9Bb3ZLSmEveStDMlk5T09Hb3hBeGNXekdRYUdKZE9wVWVzODEiLCJtYWMiOiJjZjBhYTRkYmY5MzMzYTcyNTI2ZjU5YmJjYmE2ZTNmZjI1MjE4NjY0YmM4YjhlZjkyMjA3ZGZjYWI0YzZlODY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImQvSGZMMUF3b3hCVytEaEtQckhzV1E9PSIsInZhbHVlIjoiMGhlWCszVXowNVR3SFJ3U0xWVFVHVGovZjNiVWVvb1QvcnJySU5CaUQzY29YbzR3WFBQVE9SM1NWU3dTYWswTnhwUmtKNlF3UGJScm1DTDEzSG5jK1FUdFcvZlU0MnhLcUVMRGlncDlrWjNZcHRzYnRNK0NxbFpHZlp3VE1uMEh5Q1kzL1d6dndkSWsyMU4vTDFrWEhmbXErRnZjZjR6K0g0UnduR2NZdGM4cy9FZ0JWOVpKK0FNdlVWdFpHSG1PWSt6RlpmbnpNYktmM001aHg4VlA3MXNFSFp0UkpJMnM2d3Zpcm1ha0loRFVsOHZXK3k4VHQwTGdacWQvdHBDcDVlRURGVXg2NmIrbGgwQXluUjVqOUU1SWJ1NHBtTzVyTzVoMGp1aGIxQkRvSGNpVm1EcWVYZFU5bU82VitpTXoreEoyQlVhcEZCdmRnR2JxUExpVDV1WmlPMlhrY1RkcFBsMm5HRld3UWxLZDUwQ2M2UFFWS1o5Qk9JVkN2cXU0SE0xZWoyaWlMMTAyd0xzWW9BTnRySWRuZWNqazE3NEdEM0FzRitPU0hxWC9SLzh6NHVFMGhDNGNRdUlxTWpPK0oxZUo2SFVxZk1BVnJrdkN4NDF6Ly9IMWFMUHdsQ0JsWFFEcGRvdElqdVlXMTc1SDhrMjk3ZFhnRG5XYmxMS3giLCJtYWMiOiI2ZmVmYjU0OTVkYmQyNGY0NmQxN2IzZjkwMDM0NTBlYjBkYTNjZTA2ZTJjODAzZjMyNDAyY2NkMzdmMTA1NzJiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1011479312\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1157935608 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1157935608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1594770773 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNCWDY1NVNPL2ZNSWdGLy9jc2VNZ0E9PSIsInZhbHVlIjoiTWhobHlMdVNOaHB0UnhDZFFIeW9zYy8wWWkxWENjWnpCQnRNRUZpSnlXalJDd0Q1WEtmUmxWZ2lINWFRb05vNlBlOFlnb2VvWGh5NExmZ1dEdE83Y3IwRmdFOWFnbU83QzhrLzF4ZEREdHB0WHhibGIrL3dHWmV1OVlYeWlaa3dlcUdyY1hEbkZ2eXNpczlFTlowZFRCdmVEbzM2Z0RsMFphaS9TN2FsUks2NTFkY2dkQ2hYdU9YTzZGeURwNXIxZ24zVk9FQTZqQ0ZkUHF1T25zNFh2d1pUNndIM0F3c3Z4QWdVaTdxbk5NOFk0V2Jpbm02RXNxOUNxc3JPNXNIajVDc3NtLzBpbXZycXV0OXlJN2kyd25VU083OGcwUnNXL0hzWmY5WE5Ddk1zakJ6SlYvV01aRDBLOFFiV3VIWXZRa3dYbGgwdnRBcnhzbTRqMDh0elV2S1IrdzlCZWRVSTNSTzZsYU9RdzF0N2lielkyT1M2dFROT25MUkUvQXVWZ254QmZ4NmVIeExLd0RnRThQYzc1SFVRV1VYVnl3SHlOYTlvSndUUm5WZ0hmYnluWWZJQUpQbWE0VlY4OGhWUXFHVEVWbGdVNGtHM1VaSGhWbUtwckNrTjFsR3ZuUlBCQXg2YWp5WHprbHlvY05zbTh4dFBYTXVObk43bVdMWXYiLCJtYWMiOiJlODc0NWEwYTU4MjllYTg0NTgzZjU2OWY0NmJkMDFlNDQ1Y2RhMDU0OGNmNDUyMmY5ZmJhNWFiNzNmZDU4NDdhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhsWW90YXNKMzF0L29DZXlKeVp2MXc9PSIsInZhbHVlIjoiVkd4azBEenNyQSszZ2xaZWVna3I4YkJrNEJOM2tTNDVnTVpaVEd4QmM1dyt2cFBIWW5YbFdPclVyUHNFU1NJUlJKYS90eEE3ak5QN3QycW1HWWlMekhPNGtWVlIxblArZkttMkJudnJDTG9ENlNMSXJuR2twZm4yS1Y0SXdqQ1JTZHNSRVJsOXgydERDNk5HeEdzUmJkc1NDdVJ3MkRyVVRGbFlKOC95WmdkVTcyY0dxMEdOYmZyc1F0ZUpVcm0rR09SWWZFOWtGcFVxSk56dklqZitHTXFONUdidkpOYzhXNk9RSmN5YzhDWGJWM2xCWVgvQ0lVVlJTSER2dzNJOXB4VEwwRCtuOGVQYWwzcmNwMkN6UHR6YVBuQ2NkY2d6ZDRRWWxaREt1NXd1SDZLZ2ZCcmtybE1Lc2FWMUlyYlpEMzg4TWJxWngxSmJsanlqME42dGdwQ0RSWmxXd0ZNa3VwT1BySDY4ajRScFppKzlwNEN1R3hMbGUyNXlCcE9Ib2ZnVTVCNmNsY0xObFJFc2w5YzdnV1Z0MHJTdFVONTBpbVFQUXhpKy83NGU0WDE4bEhPNEdKSERxSjUyeC9aMEpaQnQwQUF1dHMzUENSYlM2UWdPTWZLTnNNVnU1ZWZLMVJpbzh3TWxMUS8xVHd1MWMrTkFxRDBwSTcrSDZqUUkiLCJtYWMiOiI1Yzg5OGZlNDQyZDMxZTUwYmY4NGU1YjNiNmMwYjMxNzBjNjRlOTczYTcwNWMwZTJlMzY2YzA5OWIyYTllMTE5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNCWDY1NVNPL2ZNSWdGLy9jc2VNZ0E9PSIsInZhbHVlIjoiTWhobHlMdVNOaHB0UnhDZFFIeW9zYy8wWWkxWENjWnpCQnRNRUZpSnlXalJDd0Q1WEtmUmxWZ2lINWFRb05vNlBlOFlnb2VvWGh5NExmZ1dEdE83Y3IwRmdFOWFnbU83QzhrLzF4ZEREdHB0WHhibGIrL3dHWmV1OVlYeWlaa3dlcUdyY1hEbkZ2eXNpczlFTlowZFRCdmVEbzM2Z0RsMFphaS9TN2FsUks2NTFkY2dkQ2hYdU9YTzZGeURwNXIxZ24zVk9FQTZqQ0ZkUHF1T25zNFh2d1pUNndIM0F3c3Z4QWdVaTdxbk5NOFk0V2Jpbm02RXNxOUNxc3JPNXNIajVDc3NtLzBpbXZycXV0OXlJN2kyd25VU083OGcwUnNXL0hzWmY5WE5Ddk1zakJ6SlYvV01aRDBLOFFiV3VIWXZRa3dYbGgwdnRBcnhzbTRqMDh0elV2S1IrdzlCZWRVSTNSTzZsYU9RdzF0N2lielkyT1M2dFROT25MUkUvQXVWZ254QmZ4NmVIeExLd0RnRThQYzc1SFVRV1VYVnl3SHlOYTlvSndUUm5WZ0hmYnluWWZJQUpQbWE0VlY4OGhWUXFHVEVWbGdVNGtHM1VaSGhWbUtwckNrTjFsR3ZuUlBCQXg2YWp5WHprbHlvY05zbTh4dFBYTXVObk43bVdMWXYiLCJtYWMiOiJlODc0NWEwYTU4MjllYTg0NTgzZjU2OWY0NmJkMDFlNDQ1Y2RhMDU0OGNmNDUyMmY5ZmJhNWFiNzNmZDU4NDdhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhsWW90YXNKMzF0L29DZXlKeVp2MXc9PSIsInZhbHVlIjoiVkd4azBEenNyQSszZ2xaZWVna3I4YkJrNEJOM2tTNDVnTVpaVEd4QmM1dyt2cFBIWW5YbFdPclVyUHNFU1NJUlJKYS90eEE3ak5QN3QycW1HWWlMekhPNGtWVlIxblArZkttMkJudnJDTG9ENlNMSXJuR2twZm4yS1Y0SXdqQ1JTZHNSRVJsOXgydERDNk5HeEdzUmJkc1NDdVJ3MkRyVVRGbFlKOC95WmdkVTcyY0dxMEdOYmZyc1F0ZUpVcm0rR09SWWZFOWtGcFVxSk56dklqZitHTXFONUdidkpOYzhXNk9RSmN5YzhDWGJWM2xCWVgvQ0lVVlJTSER2dzNJOXB4VEwwRCtuOGVQYWwzcmNwMkN6UHR6YVBuQ2NkY2d6ZDRRWWxaREt1NXd1SDZLZ2ZCcmtybE1Lc2FWMUlyYlpEMzg4TWJxWngxSmJsanlqME42dGdwQ0RSWmxXd0ZNa3VwT1BySDY4ajRScFppKzlwNEN1R3hMbGUyNXlCcE9Ib2ZnVTVCNmNsY0xObFJFc2w5YzdnV1Z0MHJTdFVONTBpbVFQUXhpKy83NGU0WDE4bEhPNEdKSERxSjUyeC9aMEpaQnQwQUF1dHMzUENSYlM2UWdPTWZLTnNNVnU1ZWZLMVJpbzh3TWxMUS8xVHd1MWMrTkFxRDBwSTcrSDZqUUkiLCJtYWMiOiI1Yzg5OGZlNDQyZDMxZTUwYmY4NGU1YjNiNmMwYjMxNzBjNjRlOTczYTcwNWMwZTJlMzY2YzA5OWIyYTllMTE5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594770773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1083393701 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>336.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083393701\", {\"maxDepth\":0})</script>\n"}}