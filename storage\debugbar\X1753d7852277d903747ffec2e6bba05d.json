{"__meta": {"id": "X1753d7852277d903747ffec2e6bba05d", "datetime": "2025-07-14 22:40:39", "utime": **********.951678, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.473169, "end": **********.951691, "duration": 0.47852182388305664, "duration_str": "479ms", "measures": [{"label": "Booting", "start": **********.473169, "relative_start": 0, "end": **********.881087, "relative_end": **********.881087, "duration": 0.40791797637939453, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.881098, "relative_start": 0.4079289436340332, "end": **********.951693, "relative_end": 2.1457672119140625e-06, "duration": 0.07059502601623535, "duration_str": "70.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48505904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00322, "accumulated_duration_str": "3.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9148412, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 58.696}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.925333, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 58.696, "width_percent": 15.839}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.939127, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 74.534, "width_percent": 15.217}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.941206, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.752, "width_percent": 10.248}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1522038371 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522038371\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944983, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"18\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 288.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1917276754 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1917276754\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1025129163 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNVZ0FXODcrQXdTNmNGenB6L1A4SFE9PSIsInZhbHVlIjoibGIxM09Zc3FDdTZCVnBnOFhNNGw2cVFCTTNOSGRMNEdIbnV6QXYranN5bXdSV1JDMnYyOG1HMDg4TVpmbGN4azZDUnZ3QmdFdkxrS1RQSjlUbUgvWTJoSjdoa1U5dzNZK0VNSlZWVmVLeERCZjdVbnhiUG5XOEY3YkFWZ1FDRGJLNXpZOW5PVGl2Y2lCZU5xdllQSlhLYjZxZ3ZOK3NITjRYRGZXbGtlUExQbnhBY3l5aU1FUk9NaVlPNFFrR3NxajRRaVNpQTFwUWxzK0dPWVNMTjk5RmZYS3hvRUxlUGlSS1dLQVZKYTc5UmlkaHFQVEo0R1I1eHI3a204ekd5clBnNEg0RGNJWjFXVmkxWmhGOEtnQlJGalZKeFhmZGNJRWZTT1B5ajVqVUxoMTZPd2s3cXIwd0VDazFvTTFQaGxBdXlTM0svT21OQ2dRL0NvNzB5Uk01T3hBQjBRV1FQdFpOZFVvSWVvYVRXK0RnK0IybTRxM0J5Mm5DUjZqWURsK1IvZkhqRk42RXlJaXprZHBxRHFnbVkxWnVRNnlWRFRwdFFwRGQ0RnRqYjJnVmxxaVRrQzdVK1hSNCtZVEYxckRNc2YvQ1JxZHE3M01vd0NHREJxZFhCTGU5NGhRL0ZwT0tBc3p3YkJmeGFuK21VZ0dlcGdoK3A5N3FOQ2pIQ0wiLCJtYWMiOiI4NmM5ZWIxNDZlODRkMzFjYTYwOWVkMDU1MTNmY2FiNDUwODlhNWNkZTc0NTcyN2VmZGEyMTlhZWM4NWQ4ZWVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllaMmdydjgxbHlOL1RERUlBMGhCSmc9PSIsInZhbHVlIjoiOWJPTnBEeFVHa0M3Q1VIaXUxbDVOSmhNYUNnZWxYWlhsZTVLQzlsNXZITkhCTDdJT0dFb240cm1SenVRZDc4eHdXeWF4c3l5WXBRb0ZlVUpobmFrMXdPSnRBdGRWVjhjOHFGaFdDTHkvTzJiUmw2UmJxUHlsSXdwYmNsUWVyWU1Nc0FXZ0xkZ1BmcUdlbDF0RTBYaFdJWko0QjdYb0dVdGp3K2tNa3N3Tk9QUUM0VTVLSlZzUTFPT3NPMUxFYmxKdUNZd3pweE1nUUttQmJtcGtVRzVVT3k4cUpTb0ZPWUlSNW13WWhpQ3hSdjQrb284OC9XdEJVQ1hYTlVDdTRFcll4cDVYaXoyV1pydWhSVmlrbDBHVlNMVGpQYktLTWdEZnE5cTJBQkMvMnFHc25FNm42SUczQ0tGUWFhZThFRlV1b0Fpb3V5ZWRVVXFxZUtDRDQ1UWFHVVpSVFJEWmhSQU9TbXI4NEhIQ2ZXazJNd1VCbWJyNnNBclFIbjlEK0hwQ051VEVZSzZQRTJNRHhZQXN1RkR6KzluL2o1Y0E0bkUxZGl2elkvTW5ZYTZubEpsVExReEsvQ0lMVksvRll6aUtZS2YrcHJHTmNDVHQzK2EzbTBMMWxuTVhqV3dUK2Jmb2VnRFNJMWNUNzlpOU5GYmY5RE1uZ2NyME1sNDVGcGciLCJtYWMiOiJiMTNmMTk4ZGRhNDc2Yzc1MzljZDU2NzRiYjZiNTNjMDA0ZDZmOTYyYTg3MzlmNTgxMWM5N2VkMzdhNDc2YTAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025129163\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-720490056 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720490056\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1654303146 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFDUWRNb3c3S21PNVZ1U3YveC9KZUE9PSIsInZhbHVlIjoiOG1FbDQ1dlVTYzZQajJPVHU0SUJiRWp3bVc4L3drMlIxN0dSRVNxZGdSREdFSk9Bc3hFeitZcjRCa0lQemZHOE0rVDZPSFNVbFhyT0JvbmRFMC9GS01vSGhSS1pYRkZCcFRES3gyUnlJMnJVOXBuYXJMOFlqTWthUWtPYnc5R1JqLzNLQ3lxY3k2bytmNGREbEg5Y3RiNXFUMlFmV2psQjN0M0RuL1VhcnJyUXBERWtKa1UzSFZqQzI2dGNqalFsZVFCaEtJYytSK2I3QXFZT21SZGFGTEtMcCtzUG5VdVJRclJUZVlYVWh3VithbHl6bTdLODZvdVd5SUxlcytEYWcxVnBMVlRDOUxwbjJvMW5IaFg1dnZVTEdXa2NXajg3a2lhNVJrWkdaN3ArYWNFeWZHb1JZN0dtaVhjbTIyQ2ZVdUpVQ1RHREdtb0t6Qi9QdkJ4YjdoR0pyZktYNjVzenJ2d25iV3kxNUFNR3ZLS2NuR1hINFNJZkQvREdnYlpkbldiSXlCSmNYY2dmVG5wZEFKTitCajFzeS9ISFQzWGNobDZVZko5N3hrRTFpU3Mxc2I2V2NRbDZ6aitjSllOY0tQWkFCbGlpbzlWbFBIdzFTMlJKYXNBaGEyamtoWG1TcnZpSWcvdDNud1ZldEp0MTBkSWhIZWtsZFhVU21uVFYiLCJtYWMiOiJhZTllZTdmNWJmNTNiODgwMjQ3YmU0N2YyY2Y3N2JlOTdkODYxZDI4NmJhNzFlM2E0YTVmZGUzMTE4NjEyNzY1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitQK2JTeUZHb1ZEV0piZi9RQnVFOGc9PSIsInZhbHVlIjoiUlBsbWlYUjhPa29lQStBOEdHb0xiUFBjcnFGenIyTWNoNEFYTUYzVW45L284NENTeWd0dkxUM0NxZHJDUGx6MzY5eGZBWnJ4UWZzRHptS1g2WnpBQ3hKbzRUV2R2cUhLNjVwVFVOMHNMUndoTHcvWENneGVlNkFFUUYvNEd5SXhmZUQ0TXVVVGovaVM5aU1Mc0dJVXNCbjQ1UXlYYm5PbFVOZzFIVjVCWjJ6UENMak9sVVJIK1dQVjVNR0tpWFErZFQwNGpXRUFyOUlpL0RuMWllZTZxak43UUVuWWQybDJZMFM2Nks4TDB3RDZnYWpUa3E4STJmM0dzU1hvRXo2dzNLdzh3VXU0QlplOVB1SDNhN1lsNWJuVllIWVNxaGhNUGdPQzFjcmhWRWtxRnY1cldEbFA2b3F3T1dkYlhZeDROZTdscWtrWTNmaXpMQlJTZFZxUVd6UmthNzFSRlhCZjRrZ0F3dzl0azlkV3hESVBNNndIZFlLNEFTekNYWk9UVWhZdy9IYUpHa0Zoa2lmWmt6RmJIczI2VTdtd3Z1NWFuWFFTeUs0dWZRZk1meUpvMHBQanpGWjlCZExzWFdmTzhXUnNoVjhhSVZIV2hpTUxjZGxrQjlra2pFS0Fmb3VjcER0WG4zTTZTRWJTemdnWjNOMHIxdjZoWUV5Q2RCUW0iLCJtYWMiOiI3OTA2MTczMGZkZTA5ZmZkMzQ1M2EwMGI4NjM2NjE5MDlmOGJjY2QwODc5MTk5ODE3MTczYTNlNGE3ZGY1MmMwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFDUWRNb3c3S21PNVZ1U3YveC9KZUE9PSIsInZhbHVlIjoiOG1FbDQ1dlVTYzZQajJPVHU0SUJiRWp3bVc4L3drMlIxN0dSRVNxZGdSREdFSk9Bc3hFeitZcjRCa0lQemZHOE0rVDZPSFNVbFhyT0JvbmRFMC9GS01vSGhSS1pYRkZCcFRES3gyUnlJMnJVOXBuYXJMOFlqTWthUWtPYnc5R1JqLzNLQ3lxY3k2bytmNGREbEg5Y3RiNXFUMlFmV2psQjN0M0RuL1VhcnJyUXBERWtKa1UzSFZqQzI2dGNqalFsZVFCaEtJYytSK2I3QXFZT21SZGFGTEtMcCtzUG5VdVJRclJUZVlYVWh3VithbHl6bTdLODZvdVd5SUxlcytEYWcxVnBMVlRDOUxwbjJvMW5IaFg1dnZVTEdXa2NXajg3a2lhNVJrWkdaN3ArYWNFeWZHb1JZN0dtaVhjbTIyQ2ZVdUpVQ1RHREdtb0t6Qi9QdkJ4YjdoR0pyZktYNjVzenJ2d25iV3kxNUFNR3ZLS2NuR1hINFNJZkQvREdnYlpkbldiSXlCSmNYY2dmVG5wZEFKTitCajFzeS9ISFQzWGNobDZVZko5N3hrRTFpU3Mxc2I2V2NRbDZ6aitjSllOY0tQWkFCbGlpbzlWbFBIdzFTMlJKYXNBaGEyamtoWG1TcnZpSWcvdDNud1ZldEp0MTBkSWhIZWtsZFhVU21uVFYiLCJtYWMiOiJhZTllZTdmNWJmNTNiODgwMjQ3YmU0N2YyY2Y3N2JlOTdkODYxZDI4NmJhNzFlM2E0YTVmZGUzMTE4NjEyNzY1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitQK2JTeUZHb1ZEV0piZi9RQnVFOGc9PSIsInZhbHVlIjoiUlBsbWlYUjhPa29lQStBOEdHb0xiUFBjcnFGenIyTWNoNEFYTUYzVW45L284NENTeWd0dkxUM0NxZHJDUGx6MzY5eGZBWnJ4UWZzRHptS1g2WnpBQ3hKbzRUV2R2cUhLNjVwVFVOMHNMUndoTHcvWENneGVlNkFFUUYvNEd5SXhmZUQ0TXVVVGovaVM5aU1Mc0dJVXNCbjQ1UXlYYm5PbFVOZzFIVjVCWjJ6UENMak9sVVJIK1dQVjVNR0tpWFErZFQwNGpXRUFyOUlpL0RuMWllZTZxak43UUVuWWQybDJZMFM2Nks4TDB3RDZnYWpUa3E4STJmM0dzU1hvRXo2dzNLdzh3VXU0QlplOVB1SDNhN1lsNWJuVllIWVNxaGhNUGdPQzFjcmhWRWtxRnY1cldEbFA2b3F3T1dkYlhZeDROZTdscWtrWTNmaXpMQlJTZFZxUVd6UmthNzFSRlhCZjRrZ0F3dzl0azlkV3hESVBNNndIZFlLNEFTekNYWk9UVWhZdy9IYUpHa0Zoa2lmWmt6RmJIczI2VTdtd3Z1NWFuWFFTeUs0dWZRZk1meUpvMHBQanpGWjlCZExzWFdmTzhXUnNoVjhhSVZIV2hpTUxjZGxrQjlra2pFS0Fmb3VjcER0WG4zTTZTRWJTemdnWjNOMHIxdjZoWUV5Q2RCUW0iLCJtYWMiOiI3OTA2MTczMGZkZTA5ZmZkMzQ1M2EwMGI4NjM2NjE5MDlmOGJjY2QwODc5MTk5ODE3MTczYTNlNGE3ZGY1MmMwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654303146\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>288.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}