{"__meta": {"id": "X89be5915a0c1013cb8334dcb375c8cef", "datetime": "2025-07-14 22:31:36", "utime": **********.663145, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.193333, "end": **********.663163, "duration": 0.4698300361633301, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.193333, "relative_start": 0, "end": **********.606165, "relative_end": **********.606165, "duration": 0.41283202171325684, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.606177, "relative_start": 0.412844181060791, "end": **********.663165, "relative_end": 2.1457672119140625e-06, "duration": 0.05698800086975098, "duration_str": "56.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46004624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00315, "accumulated_duration_str": "3.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.636045, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6473389, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.54, "width_percent": 17.143}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.65365, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.683, "width_percent": 20.317}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-432501375 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-432501375\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-52582003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52582003\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-702252902 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702252902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1491407341 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532272261%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRKT3FSRkNOekZQU3VIejlFVnJsOEE9PSIsInZhbHVlIjoiU09oTDltNFU4NlhsanhHZ0xjcmloUUJUOCtOd3pSa2xEVDFGWjlKcUE0c2lUNDI5MWtzTmM1OFpCejFycXBjaG9VamVqVFFISlJFUW9kc0hMSEhyRitNR251MStpdmIxZU5kSmFCTmR4UUNZb2Z3dktBdm12S0x0c29CWURKWU5XUUJ6MXFub2lqakMvMFhldmV2QlE4YjN1VnVkM1o3aVZEUGpQWTQvU2pTS0JqYVhGQWhpeFVZc3FZRk5scTBTejlOT1REbCtFa3NzRU1CZlF0QnBoSlBUejhCOTR6dWFjcHpQaDBJa2lUS2sxTWc3VGJjdGZycEJxL1U5eFliWWFhb1RJMlNrbnNsY0ovaFBkT2pDZ1MzaGdnLzd3SU82Y25wb3RRRERpa0YvTUYzTFY1THZRWE5JOXVEQUNhTnZGL3RJOTFGdkptZDRsR2RHbHRmbUc3QjRZaXNYcTdONW1lcWJ5aEhrakNWQll1SThzZjgxbFl2THgySnZVY1FJTURDNVFUUmJkTVkxbFJqMFJpU0UzQzRjWXdkNkQyeGtUamQxRXlaTXk5YUpDWTcrNHhjbk5MeFhONkdYVG5vQUlMNkJuSjBmdUFzaGovMlY2bi82VWh6R2tjVFFvbnE5blFyUmFHbGZROUdLSUhqNWg3Qmlodm1HeHBVWGxQNlQiLCJtYWMiOiJkN2NlZWVjZjU0ZDNkNmJhMTNiZDcyNjNkZmRjYjRiNjk5NmY0ZTJmNWQ2ZmFiZDg3NmZjYTE0ZjhlZTVkZTA0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZHR3FvTWJtWmd1cGRLYkpoS0NrSHc9PSIsInZhbHVlIjoiQ2xiR0ViZG9NUzdoNHMwU1Z1cHhORmZ5OHJwTjZNck52NzJVQ0VhbXlPQXMyaVJ3dXF0ekRtOTlMWm9VTEg3cktYdkJ5bW1ZUS9BREhrVWpMVEljaGMxckE1SDVWYmFMYkZDS25nK0FURzlnRm8yV2tIOWJPU2lDSUs1KzZ5Smt5WUUyMVhYVXRHaFc4MDY5VTEvc0hDd2xTNG9xY2llMmFRckNGcDFrWTYxeUYxdVR4MUFIZkx4Rm1oa1dDSmdSVzhramNiU1pKb0ZnOGhja0IrNjloYk15dE9UbUtrQmZlUHE3RStvemY1VGVzRFNmaFZIOFg4dllTVk5mUEFMMm8zREJpZTIxMVRIRTVNL24wUWdDQnBMRFAxVmd5M3Y4dHR4NTNybzUrSzZrUVBFUG5GckkyZTM5Q05mUUtoeVNvbGZMZ00rVjN4QzJ4alFSNnRmSXRaOFlrcldzTGFTNHNMNEU0U211NUhuZm1pZzliVDhLdnhPbC9jT2doU0p1RUt2MHd3VFp3T09CL0NrdlBXNkZpVkQzN0U5Zzc0clVQeG5xZ2xaWmdCdnY4UHBGcVloQ3p3NWpTcCt3UUl2L3dxZXBpY3pmRFl5VFpIVFkwVnBSSUZmNEQ5aXhRWStzcWZRbjdJaU1MN1dlRXBIZHpJNlU3ejhSbitpcFhSZ20iLCJtYWMiOiIyYWVmY2NiYzBkZDBhYzMwZWJmYWVlMWQzYTdhNWYyMmQyZmUwYTFhZDQ0MDBiMzc3ODUzMTZmOGQ3MTlhNzdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491407341\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-401054967 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9tDxXjbbhjxiDJbPcLBO2bj3xslZ4VoGjLB0sApN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401054967\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1385750747 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBKRWJ1eklGQ2R4NEQ3QTIyRmxRdVE9PSIsInZhbHVlIjoiZ1VNSE9OQ0RRd3ZzZEJqVDNUa1Z6ZG03VDZMRFQxQkZkOFRCaU9LTFNIWmo2RXkrK0RmM3JiRDFtQ0dRQjVRdlJkbG5MbE41cVlWdE1BSDRZa2pXZFBycU1aN1hmaUR3VE92a3VjV25xVWtBSmVQUWh3OUxWM3Zuek9ZazlXVXVxUVhOUUhmWDBnUWYzbWJvdnRvZ2cwU0doMnR2bnRFcWN4UnZyeGVhcnlzbFRrbGc4OEowR2dWV3pkOGs1bVAwMWJXV0RFM1BKank2QnJLbFNma3pWYytRTTBVUDV2TlFRK1BKNy8zYTF6eXZjMWF4dE95THJ6UzdBN3FaelRjelExd1QwVjdkaWNzUXAzaHJJYU1JNHI0cXJEajVTR3hjSWo5clhkMm4yOVRhc3E0RDdnQ2N6WUxhcnZkWldwanVHdjBKa28zWkxzL0dVWFJSQy8vTjVvYUh5OEdXeUVOcjRXbmZVTW03T2UvZHJwOHZoa29QMjdYN1pGU3g4MGdHSDkyYjZuMmw1ZzJGc3JNTTdnc3F2bjcvWThzSzhTdWp6d3BreXZRSGd0RGE0WTlweHVnc1NDQzFwWnRhakluWmkwN1BoVWZ6dHI4bWpyaDZUOG9iTnNkYkI1QVkrYVFvSzhvZzNHZlNpK28wVmhvT1BGWEFXMUIyYmlZVmJZUUkiLCJtYWMiOiJmOGI4ODU2OGY1MGVmNTQ5OTRjYWI3ZDQ4ZjllZTI3YzM0MGZmNzNlN2RiZjZjMGNmOWFlMmUwYWQxOTZiMzU0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im14dm9jckpNYXNMSGVqNENHMWZNRWc9PSIsInZhbHVlIjoiYVFWa3FCMU8xMmY0cXIydGMxRXRxcCtKaGFwQUdjSUdvMjB2cmZqOXJIV3dubTNvT2hsZHVjT1F5S1Q5SzdjazNDQUtvQUpxYy9jQjBlTEhBSmt5eHk1SkVEcjB3ZkdaOUdveXZ3emJPdnR4ZjJTMzA1aTN6T21BNS81UWVnVFhEMnRvTk0rbzlYT2xyaWtPemhvdUpxdDZjUWpSU0l1VnZBOU1JY0Z4SnJGZ3FDanN3RGNuanJ4ejhNSHhSVmt3emF3SDN6Q0ZBNkNDRFpIQ0pINFZDQnJyR2hhdGovSjcwelpJeE1KVTQ1U25zR0Rsd04yeG9haDVJQVdOdTFCQk1zc2l3WGkzS1k1N3FFUW9QSktUaHduamtBZmdyaGJEckFHb2VDL2FYQmFrdkJSWHgzL1FhNk5GT05aNVoxZUJBcmxkRVoxTFQ4NTJiZ21HNEpyRndwS1huV1BrYllON2FqVEdEQ21HcnoxYVRlak4vVmsySElTc0txQ1UyYnUxdlQ2TjlkVkd1cmhnYWNOZDdESHVHdGlFcVBBUzBUSkZqaHdtdDVJN1NMWTFPQS94TS9HNkZIdnd4anRZU2kzUmtUZzZEczFBWmVXSkQ0UHhkQ2pycmNxOEdvL2E1UUJ0bVNLQWZJVzU4TnA3ajJxY0E2U1hBQUQ0TGQyU0NUaXAiLCJtYWMiOiI3YjY1YjRjODE4NTZmMjcyYjQ2ZjNkYjMwNzNiYWMzMDIwOTBmN2Y1MGY1YzlhNzdlNjA3M2Y1MGRlNzEyYjA0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBKRWJ1eklGQ2R4NEQ3QTIyRmxRdVE9PSIsInZhbHVlIjoiZ1VNSE9OQ0RRd3ZzZEJqVDNUa1Z6ZG03VDZMRFQxQkZkOFRCaU9LTFNIWmo2RXkrK0RmM3JiRDFtQ0dRQjVRdlJkbG5MbE41cVlWdE1BSDRZa2pXZFBycU1aN1hmaUR3VE92a3VjV25xVWtBSmVQUWh3OUxWM3Zuek9ZazlXVXVxUVhOUUhmWDBnUWYzbWJvdnRvZ2cwU0doMnR2bnRFcWN4UnZyeGVhcnlzbFRrbGc4OEowR2dWV3pkOGs1bVAwMWJXV0RFM1BKank2QnJLbFNma3pWYytRTTBVUDV2TlFRK1BKNy8zYTF6eXZjMWF4dE95THJ6UzdBN3FaelRjelExd1QwVjdkaWNzUXAzaHJJYU1JNHI0cXJEajVTR3hjSWo5clhkMm4yOVRhc3E0RDdnQ2N6WUxhcnZkWldwanVHdjBKa28zWkxzL0dVWFJSQy8vTjVvYUh5OEdXeUVOcjRXbmZVTW03T2UvZHJwOHZoa29QMjdYN1pGU3g4MGdHSDkyYjZuMmw1ZzJGc3JNTTdnc3F2bjcvWThzSzhTdWp6d3BreXZRSGd0RGE0WTlweHVnc1NDQzFwWnRhakluWmkwN1BoVWZ6dHI4bWpyaDZUOG9iTnNkYkI1QVkrYVFvSzhvZzNHZlNpK28wVmhvT1BGWEFXMUIyYmlZVmJZUUkiLCJtYWMiOiJmOGI4ODU2OGY1MGVmNTQ5OTRjYWI3ZDQ4ZjllZTI3YzM0MGZmNzNlN2RiZjZjMGNmOWFlMmUwYWQxOTZiMzU0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im14dm9jckpNYXNMSGVqNENHMWZNRWc9PSIsInZhbHVlIjoiYVFWa3FCMU8xMmY0cXIydGMxRXRxcCtKaGFwQUdjSUdvMjB2cmZqOXJIV3dubTNvT2hsZHVjT1F5S1Q5SzdjazNDQUtvQUpxYy9jQjBlTEhBSmt5eHk1SkVEcjB3ZkdaOUdveXZ3emJPdnR4ZjJTMzA1aTN6T21BNS81UWVnVFhEMnRvTk0rbzlYT2xyaWtPemhvdUpxdDZjUWpSU0l1VnZBOU1JY0Z4SnJGZ3FDanN3RGNuanJ4ejhNSHhSVmt3emF3SDN6Q0ZBNkNDRFpIQ0pINFZDQnJyR2hhdGovSjcwelpJeE1KVTQ1U25zR0Rsd04yeG9haDVJQVdOdTFCQk1zc2l3WGkzS1k1N3FFUW9QSktUaHduamtBZmdyaGJEckFHb2VDL2FYQmFrdkJSWHgzL1FhNk5GT05aNVoxZUJBcmxkRVoxTFQ4NTJiZ21HNEpyRndwS1huV1BrYllON2FqVEdEQ21HcnoxYVRlak4vVmsySElTc0txQ1UyYnUxdlQ2TjlkVkd1cmhnYWNOZDdESHVHdGlFcVBBUzBUSkZqaHdtdDVJN1NMWTFPQS94TS9HNkZIdnd4anRZU2kzUmtUZzZEczFBWmVXSkQ0UHhkQ2pycmNxOEdvL2E1UUJ0bVNLQWZJVzU4TnA3ajJxY0E2U1hBQUQ0TGQyU0NUaXAiLCJtYWMiOiI3YjY1YjRjODE4NTZmMjcyYjQ2ZjNkYjMwNzNiYWMzMDIwOTBmN2Y1MGY1YzlhNzdlNjA3M2Y1MGRlNzEyYjA0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1385750747\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}