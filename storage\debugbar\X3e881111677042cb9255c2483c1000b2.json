{"__meta": {"id": "X3e881111677042cb9255c2483c1000b2", "datetime": "2025-07-14 22:40:35", "utime": **********.706965, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.169594, "end": **********.706979, "duration": 0.5373849868774414, "duration_str": "537ms", "measures": [{"label": "Booting", "start": **********.169594, "relative_start": 0, "end": **********.603727, "relative_end": **********.603727, "duration": 0.43413305282592773, "duration_str": "434ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.603735, "relative_start": 0.4341409206390381, "end": **********.70698, "relative_end": 9.5367431640625e-07, "duration": 0.10324501991271973, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48505664, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02301, "accumulated_duration_str": "23.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.64093, "duration": 0.02131, "duration_str": "21.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.612}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6734622, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.612, "width_percent": 2.868}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6901052, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 95.48, "width_percent": 2.694}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.692986, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.175, "width_percent": 1.825}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-545006758 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-545006758\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.697631, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"10\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 160.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2049270042 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049270042\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-851332627 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ikp5WURwek5tM283cGRlTlpkRWxXU3c9PSIsInZhbHVlIjoidkVuYThZS2Q5NVRvUnBIRlR1b2VLN1pQNTJOeGRPdFNKT2xiQldqRUF5SmdVMGxIL0lvOXRZNStGbndhUjRFN1pwd2lhMEh5NFhxREsxMXpSd2xDV2ZpZlVYK3pGanFablRLUjBCL2hHUDdUSnRjNnY0Zm5kL2pkQ0ZhTFdxaklCbFh5aGZsRG9uTzBSWGJ2VnJnOXVhaC9PK1V5MXZIZWtOTm00Z3RheWh0bGtrdEpXaVQrMHEySjZZeTlQU2FLY3lEb0tVdC8xUmVXa0hKbkxiUmcwMFdPWWl3Tnhxc2RSNzJwRjVtUmh2ZElKV3orVkk1OTJDNTNRN1o3Y3NTbWI2eXVaV0VlQ3dzdS9EaWt0MUhYdzgvblNZbXNzaU9VODBFWFNxUmNieWl2S2wzNEVFTXVWVW90cCt2ZlU3WjNuNzJFRUtHb0YxV2FDUmlFTnFnWnZFRHNOS0lzQVRkR3Z2SWdwVGV4Qy9FMWtCMWJEdjFaLzRsU0toV29nTDY2Z2M5OUczbXo3T29EUlhnYkZFdGs3UmI2WFBxUHZxWjBPR0hLUHFUQ3JMSkY2ZUVXeWw2TmNRdjBlb1FUVS9RTkZIZWpFdVFZQ3lhRTRJMVJRbkxiN2h6eXA3T01BcUdUQ2lxMzNHaTFiVU5DZXF1a3Y3cEtRVmxpRWw0bVNQS0MiLCJtYWMiOiIwMmU2YTNhMmRmMjE2YTBiMmI3MjlhYzEwYTM4YWIyMTFlNzc0ZmVmYTE2ZmI3OGVjNjIyMjQzY2UxOWEyZTBmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBYckF1ZEpiVldKUmRPckg0eUVKa3c9PSIsInZhbHVlIjoiRG9adEZtU0ZDK2tjZHVHck9JdFJSakZKZ0dsS05rNEQ1bGE2L29rZXVLUWVQYytiMHpQT1gxTGErTUpvREtPRjVPM1REOEtaQlorZzNlL1NCV0dGYlk4bi9ab0hGU2NPbVloNVRyNE10S2FqQ0RtWDJyWFFKUVlHYW9YN3NBcG5YLzg0clVTSXBITmNBQWFJTDI4T0RHSlpZRXNpUmVjaGtrNVpGMndST2FVRFFqejRhSUVvUkt6Vi83NkRQRUh2ZUNCOUVoNDhudnBHQlVZQ1Z5MXk2ZGZtOEFzVnNxV29qaWpFZk9yeG1aczRsR0U1UktVcytrMjRUSEk5RGJnemFzdlVLaHhZelM5Wm9vaGV2YlBvYll0Rld6a2dXN2x1VU45Y1ZDYlc4RExBaVcvUkZwMitCQkV4OFRiVTFBR3EvenF5N0NOTW9EQjlrR3h6TVlvWGhKZ3ZZS29OaEFWNm9na09ZVHYxOXhNSS9FRUpxa1ZTdkRHNXRhdmNlS3NmNGQveFVkUGEraEZNUlduR2tqQS9mZVBWMzV6TzdsdXB6Wk9ZNHZocTA4anllWkp0VjRJRjJiU0xYK3kvdUFFWGw0QTVCZEYwWEJBUi9nS2tlek82TzhFZWJhRlhVSzRzY1VqMHZNL3Nvc3NIY0ZWNEk0RE8wNTlKT0tSY0VtTHoiLCJtYWMiOiIzNDEzN2U3ZDYxMGQzMGVhNWRlNjVjMTQ4YTNiMzFmOTNiNjYzMjY1YzAwMDc0MjM4MjMzNWYxNGVhNTAzY2M5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851332627\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1920277521 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1920277521\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-755279233 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik4zT1FFRUh2NDU0bk1GR0Z0dnhVYmc9PSIsInZhbHVlIjoiMGQyaVRiN2NXVmRWVmM1TzgxcXpNeTVyKzFwLzJ4bjJOMkd5YlVWUUJFZlh4bDFWQWRMS1RuaEN4SWVSN2NRRHU5dTh3MENMUjZRa1NIR21IWUVWc1hIVTIwYWM3R0hXaWVack1VL1Z1TTQ2TVhUVTJVUjB6ZmNNMk1NWXFsSTlHZW9tS0VsSytGTFhLYmRkMTBGWCtoMmkvdG1CQXhOTmVuNzlNdUpnQlQveDRhdXIyUWN4YUpUSDJuMU4yZGRHSEloQ3l6KzNMbEMvT21ZbUlwOGgvY3dFRUJTRmZQTi9qWk1rM1ZrWmNSWm1hZ0Z6L2NScSttMUowOE1wOXRESmRERzhwd2pBZUR4TEp4eTRydDdjaFVvekYvbW84NjJPTDFVMlFXNVZaRFFzd0NFSFd4MFpOdGY3N1hpNzNNQkhvYmtHd0pjL3RrUHFnUWluVWN0Zmt6WkRjOWxWT1p5dVVPZ0FyZEh2WmZMOG1lSHFPSTdnKzhVVFd1U0pBWFFCOEJobXhNR2Jpcm13NEp2a1Vwak5vNlBEWkdzNE1IVGZPc25FNzkxR09DQjdpU3VhRFFtYnN6MllCOFFJYW5BQTZka0xyL0pCa2NsUk9UbE9oaHZHQkRqdDh5SUxOUzVSYVplcFo5ZHFSMzFsMXhPSFZER09Lb3ZLaWk4S0sxSEciLCJtYWMiOiI3Nzk0YTA2OTM0MDQ2MzEwNzRiMzFkMjA1YzkxMWYxOTUzNDQzZmQzMTY4NWU1OWVlMTQyZjcwZDAxMjczMmUzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJ5SDYrS0ZlREpFTk12YWZJbW5uaFE9PSIsInZhbHVlIjoiOGtOTmJET3hJY0VFdFFCb0hJZ2JpQ3Qwa0x0OEJJekdrVlBOUnRaUGlXWElpWkJTRURhbEpQMDhpdXQ0YkhkMEg0dlhXNjVPaHM3M2pJcTZkYWVOZUkyYVRqVGdjV1JMd1YvUXo4cDJKTUVFckxkakhEbHFnYnRyMGdFZkpTcGZQMUJUcWRtbUFrbHVjSHhKdHpzOXUwSFNnZFpoU1A1bXR6d2wvNXdmNGh5T3piOU5uNXhETGRzNkFoUk53NUZrNTBnYTNMeHpsSnpLYWNHRFdvc0FDYW9YQmFYT0RFWmh3Y0djOE9wcm1RZ0pyVmRhenFXUlEwYWhWSkZZNUZZcGk4T1k1V1crd1dXQXJveTN0cGh0RktvRjUxK3F5UFhyREp1VUxOais3V1VORzY0Z09rSzJUV2Z0SFN1VEt5ZzlnRjMxdkFOVWNsQmhQcWtTKzhzV0pUL3ZUYWNEY0lVVFVVVlp3SHVrbXpwT3MyamJVSG1DTW0zbnc5UUQxblNKSTZ4eHp3TEhiZXl2OXAwWmlFWW1UWUIza0FNSjBSSTZzTm94UXA4K3N2Z0IrNEh5ZkdyMDJFMU1KbGVMVmJCQ1FXd2pXSmU4YlM0SzkxbEZpWE1tcVZmTmFOUDBiZ1lVbGVjQTNTTkRTZXA4cm03TmsyRDluK3NFNEgwUlVPQlgiLCJtYWMiOiJiNDE1NmVmY2VmMzU3YTQwZGM2ODdkMTA5N2IzMjBlOTgwN2UwNzIyOGQ0ZTBhNWJjY2Q5MWQ0NmQxMjlhMmE0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik4zT1FFRUh2NDU0bk1GR0Z0dnhVYmc9PSIsInZhbHVlIjoiMGQyaVRiN2NXVmRWVmM1TzgxcXpNeTVyKzFwLzJ4bjJOMkd5YlVWUUJFZlh4bDFWQWRMS1RuaEN4SWVSN2NRRHU5dTh3MENMUjZRa1NIR21IWUVWc1hIVTIwYWM3R0hXaWVack1VL1Z1TTQ2TVhUVTJVUjB6ZmNNMk1NWXFsSTlHZW9tS0VsSytGTFhLYmRkMTBGWCtoMmkvdG1CQXhOTmVuNzlNdUpnQlQveDRhdXIyUWN4YUpUSDJuMU4yZGRHSEloQ3l6KzNMbEMvT21ZbUlwOGgvY3dFRUJTRmZQTi9qWk1rM1ZrWmNSWm1hZ0Z6L2NScSttMUowOE1wOXRESmRERzhwd2pBZUR4TEp4eTRydDdjaFVvekYvbW84NjJPTDFVMlFXNVZaRFFzd0NFSFd4MFpOdGY3N1hpNzNNQkhvYmtHd0pjL3RrUHFnUWluVWN0Zmt6WkRjOWxWT1p5dVVPZ0FyZEh2WmZMOG1lSHFPSTdnKzhVVFd1U0pBWFFCOEJobXhNR2Jpcm13NEp2a1Vwak5vNlBEWkdzNE1IVGZPc25FNzkxR09DQjdpU3VhRFFtYnN6MllCOFFJYW5BQTZka0xyL0pCa2NsUk9UbE9oaHZHQkRqdDh5SUxOUzVSYVplcFo5ZHFSMzFsMXhPSFZER09Lb3ZLaWk4S0sxSEciLCJtYWMiOiI3Nzk0YTA2OTM0MDQ2MzEwNzRiMzFkMjA1YzkxMWYxOTUzNDQzZmQzMTY4NWU1OWVlMTQyZjcwZDAxMjczMmUzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJ5SDYrS0ZlREpFTk12YWZJbW5uaFE9PSIsInZhbHVlIjoiOGtOTmJET3hJY0VFdFFCb0hJZ2JpQ3Qwa0x0OEJJekdrVlBOUnRaUGlXWElpWkJTRURhbEpQMDhpdXQ0YkhkMEg0dlhXNjVPaHM3M2pJcTZkYWVOZUkyYVRqVGdjV1JMd1YvUXo4cDJKTUVFckxkakhEbHFnYnRyMGdFZkpTcGZQMUJUcWRtbUFrbHVjSHhKdHpzOXUwSFNnZFpoU1A1bXR6d2wvNXdmNGh5T3piOU5uNXhETGRzNkFoUk53NUZrNTBnYTNMeHpsSnpLYWNHRFdvc0FDYW9YQmFYT0RFWmh3Y0djOE9wcm1RZ0pyVmRhenFXUlEwYWhWSkZZNUZZcGk4T1k1V1crd1dXQXJveTN0cGh0RktvRjUxK3F5UFhyREp1VUxOais3V1VORzY0Z09rSzJUV2Z0SFN1VEt5ZzlnRjMxdkFOVWNsQmhQcWtTKzhzV0pUL3ZUYWNEY0lVVFVVVlp3SHVrbXpwT3MyamJVSG1DTW0zbnc5UUQxblNKSTZ4eHp3TEhiZXl2OXAwWmlFWW1UWUIza0FNSjBSSTZzTm94UXA4K3N2Z0IrNEh5ZkdyMDJFMU1KbGVMVmJCQ1FXd2pXSmU4YlM0SzkxbEZpWE1tcVZmTmFOUDBiZ1lVbGVjQTNTTkRTZXA4cm03TmsyRDluK3NFNEgwUlVPQlgiLCJtYWMiOiJiNDE1NmVmY2VmMzU3YTQwZGM2ODdkMTA5N2IzMjBlOTgwN2UwNzIyOGQ0ZTBhNWJjY2Q5MWQ0NmQxMjlhMmE0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755279233\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>160.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}