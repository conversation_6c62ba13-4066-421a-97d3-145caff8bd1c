{"__meta": {"id": "X57e419228196c5841697350664a85675", "datetime": "2025-07-14 22:38:06", "utime": **********.361534, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[22:38:06] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.35429, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752532685.874967, "end": **********.361549, "duration": 0.48658180236816406, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1752532685.874967, "relative_start": 0, "end": **********.279969, "relative_end": **********.279969, "duration": 0.4050018787384033, "duration_str": "405ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.279978, "relative_start": 0.4050109386444092, "end": **********.361551, "relative_end": 2.1457672119140625e-06, "duration": 0.0815730094909668, "duration_str": "81.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49255424, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0049900000000000005, "accumulated_duration_str": "4.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3180032, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.723}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3305361, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.723, "width_percent": 13.427}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.346912, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.15, "width_percent": 10.822}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.34938, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.972, "width_percent": 14.028}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1482273741 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482273741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.353552, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-686817211 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-686817211\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-938404339 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938404339\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-258472923 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-258472923\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2126575270 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldZQzBhWlFaZGd3OE1vb21ueXEzN2c9PSIsInZhbHVlIjoiRytzVjFDbU02N3A5U1hsb1Z4Y2xzdEticjZxUTJvSXRRb1Q1NXh5bkRSbVBWYzNCbHIxbkdoYnM1NjZjSzZ5OUpQa25vdjVyWk5hamF1K01NSVBWS3VMZTVHVXpIZk1YaEVqSVdpbWl2d1NqaHI4ZFlndW02NmpET1NqVXJlSSswbHpGRUk3S1NWOUNhc1RHWUxlYnM1NGZkbi9tUzdxemdjclVVSElNNjhuTm14OThua2hjeEYwejJJM0tTU3RsZk5iMDI4bWNiTnBRNkVyKzBacko5K2I1TEp2cStYZnNDNEZNOVlYV2ZZbmw1WENyQnVoNGRkVzBaUksxd0pTVis0S0twYnFsNzJnUlF3cHd4MG5kdVYvazBMZXBjSk9xSS9KTXNJNllwSzhJZjRIaEdjQU9mbzBNcXkzWGtqdk96cWtLbmlEajNmY3J4MDE5TGtmaXVsSmh3eUorNDJ6R2NkMi94QWQyR1BMSmVRNk1Nc00rakFxd1VnRlBjS3d2bWhDV2YyS3RidTFYeXZyV0NkU2hlTmx0SURyNUFHYi85cklBS0h2bGhnbm45dElBUXppMm82R0svcUozTldkYXZja0JIblo4NU5rMU9pVGR6ZVl5eWZlR0lSeU9aRHUrOXpYTVVQdEl6Y0VEbVFzUHE5aFR5VnFxZ3N6NUF5enEiLCJtYWMiOiJkZjIxMzI2ODlmZWY0N2QzZmMzNDI1YjM4MDMzZmY4MWM0ZGJkM2UwYzE0Njc3ZjIxNjViOTQ1YmEzNjNlZWRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImloOE9mL1VUNjFBRDN3dVNKYU1JeWc9PSIsInZhbHVlIjoiWThmeHRRemJheGo1cFdDRVI3QkZzN09GSXNTejZpOTl2Qk5kT04rMjkwaTM5YnVGN0wvMHlJakFvcUI0NVk3c0ZqRUQyVXd1NXArWFFDSGVjTFVHQm1Mc0tBVDVyR0ozSDlPaFI1VytpNnI2Q2FTcjEzTVdhV20xbFBvMllNM1BMY0xoZlFRMDQ2d28ycmljbW1RWEhIZHhrNjBEeGZRa2NmU2QyZkZJZVJWb29sSThNcG92TkF4d3NzMzk2aWVIS0xtTFk3V2dFWjdCSVdvQXZZUFFrYWkvZWpWODNMakg1UnlRbG9OazkxaDNnU0RTT0xxNzUvbkNadktUVWc5UzdsK2MzbGlhdU9BQWdXSUdIL0dGbHpHWGQyRWJFKy9ELzg5Z0lMbWlQWVNDWVQ5WVhRYXdwRkxlOUtDVnV6VS8zc2VQdUVqbXBJWno5QkxUdXVXMHJqQUZqNlh2dXB0alRNNGJhTTg5MlZFVFgvRzBHRFVxSnN6U1dmKzdWYjNNNWlRbS9ZdGNFVkJIc1VMbmk4dTRJaGhuekxUQ0x2cnQrYXpDN01wVCthMDdmR0RCWjYyQ1hpdW5qYUpxMW44NnhFUEhvaFJ6dUgyNTVYdjZFR2oydDFZcytCUndRT2lYRFYzd3g5VTdXY012emtsOWFjNWw0TnRBdjJzUTMzUVkiLCJtYWMiOiI5M2Y1MjU4YjNjMGQxYTgwYTgzMDY0YzBkYmNmZDMwZmYxNjJjMWFkMzU5YTNhMTJmNjI3YjVlNTcwMzk2NWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126575270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-257438967 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257438967\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:38:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhBdFgwdS9xZlZuSXBBRUgwTTJYNXc9PSIsInZhbHVlIjoiclBCRnp1MnFNVjB0SWhZc2dFUkVPejFEcUYzV2J0WEtqZ1ZYaWpLNFgzMHVoMGs0Y1hQZHh6WTM4TjRPcTJhSTdIRVkveGR0ekRkUFJNK3k4amhIMzlVdlNweGRmbXd1akNmUFJnWnJYWERPTkh2bG1USHB3RUpqODU5UTh6NGI1eWdwSUtZWUtVOGx1SEt6bWJ5RXArMWV1dmhhU1dHM2tNcjhwSHJBbmxHUFJuMWlmcy9yRGpsVVN5SFBEa1RzT3RNaHVOMGRYY0VoL1dwUVM0KzF4Wm5yOXZNLysxQXlGcEVXNEY3d3U2dm9tVUFwbDdjMDd4bkY2RU9BejlzdEJEdTFzTGFDZ2VtbVdZWmxOSjJYZElsWGgvcVlpdHhnY253ek9hUE9mYzZua01WNld1cS82eW1rZ1ZPcmh4V2RrZXlnU0E3WDErNnRTa0pMWUNqSWRzOFJhMGJVTk5wUUczbmRZZS9kejVyVURqSGJTVzl6OTNXeVFWVjQzeEthbDZCV0xxdncvaVgxOE9NemE5NTVOMVNzaVAxSWtzNmxvWVFmbDNmSHJNUDQ0U2hDYm5QRTRzeVVENGlROFBBK282SDVtSlBRa0U4cmNkRXU5c1lpNWlKYUdlSGpJL0xzcUZ3NlJJNTc4RTkzbktkMExWRjdpeml6VmVYd0dkSkEiLCJtYWMiOiJkOGEyMjkxNTYwM2FkZWU0Y2U2MjlkMTgwNGYxMjE2MzgzYzE2ZTUyNjc3N2UxYmFkMzYwMDc5YTQwODdiZDNjIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InoxZElRREJrV0srTkpJTlRrZkJ2c0E9PSIsInZhbHVlIjoiTFBrUWVDTU9PMUNGbDUzYzNCSVB3bHRjMnk0Szhtd1R5MkRSRjFHNTRzb000TUM0S09tN25sWXFyc2V2YzdQdG8zK3ZiQnNmbzhwL2ZFZVVIMmRJQ1V0YWp2TDhab1lPNTZVd0M4ZzdsWjdtYnFnSTg3REdjWlMvZzdBL0drczNYZGFhT2pBZVhZYkYycHlTaXB0cUZiRXFhM1J2UEJCbWxjQ2I0UVpFQXFpWXdOUTR3S3ZreU5TeHJaSHhlR2lCMXZiaVB4eUdZbVVKTE5HeEVZcEJsWWhHRWlpY3FaOVdQdHVmVnB0R3BFT1dCaTlrL3VLbXhjMUFsUStOTHE5c0N5cnhVVlduWWYrS0NhS3lVS0JRcjRxTUx1MDhNem5YZm1vMnV5L0UyemtpQmx4c3VRMnE4UnRqNlBsdTNlUFhIaHNQWXJ4VG45Vno0Q3RCM2Y5dzRuK2VhaTA4UmlxM2ZvQytMNm1tMytndmJqc0E1bjVNMXBONy9iSzJxbGVzblJqWVlwcmszUGdIV0ppZEFCM0prS1k5UVdqbzY3VWthTHhRRWJYVFNaM04xdis5aGczS1o3U2R5Z3ZvamliNlMwNUUwMSttZlVaMHU4T2NHbnpQVHFhc3UrVEx4VjNJZmVnQkpSMVBjOVJ3MG1FdWlPRXFycVpuczNEWmVPcWoiLCJtYWMiOiI1ZjU1OTc4ODNmMTJhZGQ1YTQyNDkzODkxNTczOTllZmJjZjdjYTYzMzZlOGJkZTE5NGEyMzFjYTZhZWUyZjM0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhBdFgwdS9xZlZuSXBBRUgwTTJYNXc9PSIsInZhbHVlIjoiclBCRnp1MnFNVjB0SWhZc2dFUkVPejFEcUYzV2J0WEtqZ1ZYaWpLNFgzMHVoMGs0Y1hQZHh6WTM4TjRPcTJhSTdIRVkveGR0ekRkUFJNK3k4amhIMzlVdlNweGRmbXd1akNmUFJnWnJYWERPTkh2bG1USHB3RUpqODU5UTh6NGI1eWdwSUtZWUtVOGx1SEt6bWJ5RXArMWV1dmhhU1dHM2tNcjhwSHJBbmxHUFJuMWlmcy9yRGpsVVN5SFBEa1RzT3RNaHVOMGRYY0VoL1dwUVM0KzF4Wm5yOXZNLysxQXlGcEVXNEY3d3U2dm9tVUFwbDdjMDd4bkY2RU9BejlzdEJEdTFzTGFDZ2VtbVdZWmxOSjJYZElsWGgvcVlpdHhnY253ek9hUE9mYzZua01WNld1cS82eW1rZ1ZPcmh4V2RrZXlnU0E3WDErNnRTa0pMWUNqSWRzOFJhMGJVTk5wUUczbmRZZS9kejVyVURqSGJTVzl6OTNXeVFWVjQzeEthbDZCV0xxdncvaVgxOE9NemE5NTVOMVNzaVAxSWtzNmxvWVFmbDNmSHJNUDQ0U2hDYm5QRTRzeVVENGlROFBBK282SDVtSlBRa0U4cmNkRXU5c1lpNWlKYUdlSGpJL0xzcUZ3NlJJNTc4RTkzbktkMExWRjdpeml6VmVYd0dkSkEiLCJtYWMiOiJkOGEyMjkxNTYwM2FkZWU0Y2U2MjlkMTgwNGYxMjE2MzgzYzE2ZTUyNjc3N2UxYmFkMzYwMDc5YTQwODdiZDNjIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InoxZElRREJrV0srTkpJTlRrZkJ2c0E9PSIsInZhbHVlIjoiTFBrUWVDTU9PMUNGbDUzYzNCSVB3bHRjMnk0Szhtd1R5MkRSRjFHNTRzb000TUM0S09tN25sWXFyc2V2YzdQdG8zK3ZiQnNmbzhwL2ZFZVVIMmRJQ1V0YWp2TDhab1lPNTZVd0M4ZzdsWjdtYnFnSTg3REdjWlMvZzdBL0drczNYZGFhT2pBZVhZYkYycHlTaXB0cUZiRXFhM1J2UEJCbWxjQ2I0UVpFQXFpWXdOUTR3S3ZreU5TeHJaSHhlR2lCMXZiaVB4eUdZbVVKTE5HeEVZcEJsWWhHRWlpY3FaOVdQdHVmVnB0R3BFT1dCaTlrL3VLbXhjMUFsUStOTHE5c0N5cnhVVlduWWYrS0NhS3lVS0JRcjRxTUx1MDhNem5YZm1vMnV5L0UyemtpQmx4c3VRMnE4UnRqNlBsdTNlUFhIaHNQWXJ4VG45Vno0Q3RCM2Y5dzRuK2VhaTA4UmlxM2ZvQytMNm1tMytndmJqc0E1bjVNMXBONy9iSzJxbGVzblJqWVlwcmszUGdIV0ppZEFCM0prS1k5UVdqbzY3VWthTHhRRWJYVFNaM04xdis5aGczS1o3U2R5Z3ZvamliNlMwNUUwMSttZlVaMHU4T2NHbnpQVHFhc3UrVEx4VjNJZmVnQkpSMVBjOVJ3MG1FdWlPRXFycVpuczNEWmVPcWoiLCJtYWMiOiI1ZjU1OTc4ODNmMTJhZGQ1YTQyNDkzODkxNTczOTllZmJjZjdjYTYzMzZlOGJkZTE5NGEyMzFjYTZhZWUyZjM0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}