{"__meta": {"id": "X55e8de45dd44d1ab512a33be0b5d326e", "datetime": "2025-07-14 22:41:00", "utime": **********.95847, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[22:41:00] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.95208, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.535494, "end": **********.958485, "duration": 0.4229907989501953, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.535494, "relative_start": 0, "end": **********.882111, "relative_end": **********.882111, "duration": 0.3466169834136963, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.882122, "relative_start": 0.34662795066833496, "end": **********.958487, "relative_end": 2.1457672119140625e-06, "duration": 0.07636499404907227, "duration_str": "76.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49256160, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0034, "accumulated_duration_str": "3.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9196591, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.706}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.931491, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.706, "width_percent": 15.588}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.946275, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.294, "width_percent": 16.176}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.948209, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.471, "width_percent": 8.529}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-913661520 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913661520\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.95146, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-1280780699 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1280780699\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-62283175 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62283175\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-992979021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-992979021\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1188273767 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpueGlvMkZFS3NRRlNPZGJOQ281ZXc9PSIsInZhbHVlIjoiME4xWlhJM21ucXVOdTZ0WnJ0bHV6MTlaTVgwS2RBUDlZNFZoZFZXTnJSandOdUdJU3NPYTQvWlNRWFBCaHdNSXpNZ3hacGx2VW05MnJjQlRSVjBmeVliUjg3VVAreWNHZ2xDNUROZERqSkFXZWVVamZqckVQLzdkK2taYURuR3Q5S3drQnQrdzBGS0lYS0xFYkVXcFUyazhPYmN2S2dlOVllQWh6anFndElacFFCb0g0L1daalZDS1pFdmh2WUk1bGh4WnNjOW9vSmNhMlJ1NHBNVEtQanF0TmhHRjdwbzFGWXo5a21lNjF6ek4yd29OWWJxU1VlQW5Zcll2eVU4VkVoclhTWEphRndnSTFUZFZMUFhsTjhRUmVXaWpHbk1XakpMbmt0VWJhUFUvb1pDb2l1UmtXMVd3RlZFSkVRVjNHeUxselJBZEpqKzJpSXlObjhjU2QrenZhUjVDWmVUZUNOdmhHclU2d0lFQ1pFb3Z5RlVLeTFlT2lHSjhvMUhPektKOXRuTTVDQkQ5Um5ycFY0V04wYkpaSHVjUkM2cVc5RUF2aW1WTWNTazcwT2FTZWxTMTV5cE9TWFYrSjRpdDV4WWlXL2Vkakp4ZzE3QURyZHovUkNaOWhYWnkrT2t1bjQ2YmU3Q1JpSC9Xc2JQOFRDTGJBeGp3TSt4MEIycHkiLCJtYWMiOiJjNThhNDYyMmJlOTBjNGRhNmQ5ODZlYWYxNzg5ZGI2NWQ4Nzk3NjA3MzZlMDA3NWE3ODcyYTQzMDUxNzQ1NGMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJEemNjTkIvODJZR1RzYWg3d2wyRWc9PSIsInZhbHVlIjoiNGFOelQySVBKYms3eXdFem1vYjlOek80UDB0ZG9uT1FqRHRZeDNDNEFxMUl5c0dSbnlVdVg2OXBBZVhDUWVNTXVOY3ZoNmw5Q3l0czZCOXBsdnJNUk5xWE94QzV0WUZaVjB5elpzbWRMMmlnUnN4UWxvak5uQnpteXFFNW1HNENvblBnd3hPOXRkVDQvUExEeWNTcTRDL1dud2lmVE1SRkxOUWNCdFlsNUZnaUpXWllrdWVUVWtXQzZFQ2JKWXo2TVNUZlA3K1lwZWxISzhSMFRiNnNRYTVONzJPWS9TNTFIR29xQmpHZ3dJUHRFU1FKUmxwczFCbTRCQml4bnV6UFhlMmJGMGxCRWx3WGJmSVppUGlWZEFhR2poSEs1b29YY2toRFhrMEpTZ2JiYVl4eG5QT0hsbFR6enJ6bEV0RUxaSllwamRpaC9YQlJTZjhLVkpMckx2ejdQNzJTaHgzWGlLc21PVW5VdlFnU2hMZnVWaGhXYUsxaE5wOTErU2pkdkFuMy9RWkd0K0E0OW1IVDdZTENOUmtzYW5XRmdzT3hJU0hPN3lvWDN4MlhQdDYzUm5nazJwNTJxY0VHSFZxR3ZyL09ka2FNdlNlbmI1bGZubkVERkdGWXFHQUJPNi9yWnpsOHRBeXc5QTE0TjFRYkdCNW4wVDZKQXhaay9iK1MiLCJtYWMiOiJlMTYyZDcyMmNhYTFmMWRkYmFhNTNjNTkzNjZiMmE4ZmI1MmRkMmNhNjc1NDdhMmViMjgxM2M1Yjc2NWY3NmU1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1188273767\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1184107535 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184107535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:41:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxvS2JsdW5KdGMvTjdobjZmcFFzM3c9PSIsInZhbHVlIjoiZ3lRZlBWTmxCdWNSOEx4d2VyZVErR1NzSE5FeW5vRTRlSFUyYklhMU80RlM3aWdKc3p2WVBFT3FxNU9HQ3o0Z2dWVUozdVprU29GOTlVUGZ5bGd1V1o0MFBPbjhvRUdiamZVUU1xZUE0YjQwbHFKMDV3MUU5NjB4QWlad2trTS9KUzhlYkpBVFJKd3huUGRabkRvM2hIMlNzZnRmVXg3Q1ZsVzM4MFRjUlAxQmVJVkhscU1aMkV5dTBoMGpjaXRwclk5ZFhrUU92NVVYZS9HVi9MSkJSck9rMzFVM1lQTFpFK0dlb090Smg5VDhNRkNMR2pJcm16djhhKzNVRFgwZ2pQbTVHZDBtcnNiNStiV1Y1QVVsTW9xSnFEaHYvVTBUa051K2puR0dPM3E3MmsyOGgrY2xyclN5SWtQc3E4TDVjUzhJS0s2VjhkNXZTMy9iaUFENTQ4ZWVOQlBwRnU4cloweE9BYm00ZUttZTYyTjhIaHN6NDRxcFdjZmVUQUZwQmRCZGQ5akREclhrRHJTdUFwNTNUTlg0Zlg2aXFZZ3BCenA5TGZKZU05NHRWMTkzbnJkRjM1dFNqMlpJNHVjdDgvTXJNNkN5VUZYVHQ3U1A2a2wreUsxQTMzNW9GSEdySmxUd2F4ZUMzaDQ4ajlsQkR3aGcwOTFqNnh2eG9LTG0iLCJtYWMiOiJlODIxOTlhMzY2MDc3NTdkNTU4NjVhOGNkNmIwYjA2N2E0MDhkMmVlMjczY2I1ZDBjNTU4YzQ0MGQ5MDhlYWYwIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImM4Q1ZpdlJyME5weUhWR1FOdmNNTHc9PSIsInZhbHVlIjoiZ25BaGd6YzZlS2gwMHVOS1JQOU5oU2JhOW9wZFpJb1RReHRENzJ3Zm9xQXNndnZabERQR1VvN3B1aXdkTFFlWDFvUTFvMmM5V3lXR1YwNjZBa3g3R3I5cmhYeG5tMU5KVU5zWVBBNVFZNzJsM1p2U0d6VjBxS00yNmRha1BLUm9kZFpTV3lvb3l6bTBrNk5kMXc2dlN1Z0xmT3lrVE9WSmRnL01JYW9MR0tpeFJ0aWpxMmN3ZUpWaFJOWTkyZkhGSWxobVJmUUtNUEVBbVdGYitRK0VLekFMRlVWVDlTZmtKL0hybnJlTTc0TEhwYUF2d3kyMHpseXVKZDhrNmNxSW0yMUJWSXVVcC9FbmxwZm5pTUo4dXBsLzZTZ2JtRFFXT08zV2NCNVhKdVRUKzVlZ1owdDNQd0tCVEFoQ2RYVVdVL1A4TG90cDVjWWxGYkRkYTNNVHM5cEx1Z0V4ZzBLV3ErZFZUQUF0Um9LVTBnQnYwMjdReTQzYlJteWNLOXZDOGdwK0NZZmNZRVgvK3BmQlRNUUlpdkoxS2hzUW1TcmdldEU5aXYxQm1HUzgxRHRwbUU4aHpOakNpUVMwcVVHVk1YOEhqQ0Y5dWJaSXkyWGY1eW5TQzVFK3FESXk3dlArY3RvbDN2NHUxeEd1NFpTR0JaZm5sUmNnNkRlYW1TaEUiLCJtYWMiOiJjMzFjZDk0NmZmYWI0NjRhZjQxMGRkODZmNGIxYTk4OGVlNTFiYmNmYzVhMDNiNjRiZWQwZTY5YjFmNTg3M2VmIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxvS2JsdW5KdGMvTjdobjZmcFFzM3c9PSIsInZhbHVlIjoiZ3lRZlBWTmxCdWNSOEx4d2VyZVErR1NzSE5FeW5vRTRlSFUyYklhMU80RlM3aWdKc3p2WVBFT3FxNU9HQ3o0Z2dWVUozdVprU29GOTlVUGZ5bGd1V1o0MFBPbjhvRUdiamZVUU1xZUE0YjQwbHFKMDV3MUU5NjB4QWlad2trTS9KUzhlYkpBVFJKd3huUGRabkRvM2hIMlNzZnRmVXg3Q1ZsVzM4MFRjUlAxQmVJVkhscU1aMkV5dTBoMGpjaXRwclk5ZFhrUU92NVVYZS9HVi9MSkJSck9rMzFVM1lQTFpFK0dlb090Smg5VDhNRkNMR2pJcm16djhhKzNVRFgwZ2pQbTVHZDBtcnNiNStiV1Y1QVVsTW9xSnFEaHYvVTBUa051K2puR0dPM3E3MmsyOGgrY2xyclN5SWtQc3E4TDVjUzhJS0s2VjhkNXZTMy9iaUFENTQ4ZWVOQlBwRnU4cloweE9BYm00ZUttZTYyTjhIaHN6NDRxcFdjZmVUQUZwQmRCZGQ5akREclhrRHJTdUFwNTNUTlg0Zlg2aXFZZ3BCenA5TGZKZU05NHRWMTkzbnJkRjM1dFNqMlpJNHVjdDgvTXJNNkN5VUZYVHQ3U1A2a2wreUsxQTMzNW9GSEdySmxUd2F4ZUMzaDQ4ajlsQkR3aGcwOTFqNnh2eG9LTG0iLCJtYWMiOiJlODIxOTlhMzY2MDc3NTdkNTU4NjVhOGNkNmIwYjA2N2E0MDhkMmVlMjczY2I1ZDBjNTU4YzQ0MGQ5MDhlYWYwIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImM4Q1ZpdlJyME5weUhWR1FOdmNNTHc9PSIsInZhbHVlIjoiZ25BaGd6YzZlS2gwMHVOS1JQOU5oU2JhOW9wZFpJb1RReHRENzJ3Zm9xQXNndnZabERQR1VvN3B1aXdkTFFlWDFvUTFvMmM5V3lXR1YwNjZBa3g3R3I5cmhYeG5tMU5KVU5zWVBBNVFZNzJsM1p2U0d6VjBxS00yNmRha1BLUm9kZFpTV3lvb3l6bTBrNk5kMXc2dlN1Z0xmT3lrVE9WSmRnL01JYW9MR0tpeFJ0aWpxMmN3ZUpWaFJOWTkyZkhGSWxobVJmUUtNUEVBbVdGYitRK0VLekFMRlVWVDlTZmtKL0hybnJlTTc0TEhwYUF2d3kyMHpseXVKZDhrNmNxSW0yMUJWSXVVcC9FbmxwZm5pTUo4dXBsLzZTZ2JtRFFXT08zV2NCNVhKdVRUKzVlZ1owdDNQd0tCVEFoQ2RYVVdVL1A4TG90cDVjWWxGYkRkYTNNVHM5cEx1Z0V4ZzBLV3ErZFZUQUF0Um9LVTBnQnYwMjdReTQzYlJteWNLOXZDOGdwK0NZZmNZRVgvK3BmQlRNUUlpdkoxS2hzUW1TcmdldEU5aXYxQm1HUzgxRHRwbUU4aHpOakNpUVMwcVVHVk1YOEhqQ0Y5dWJaSXkyWGY1eW5TQzVFK3FESXk3dlArY3RvbDN2NHUxeEd1NFpTR0JaZm5sUmNnNkRlYW1TaEUiLCJtYWMiOiJjMzFjZDk0NmZmYWI0NjRhZjQxMGRkODZmNGIxYTk4OGVlNTFiYmNmYzVhMDNiNjRiZWQwZTY5YjFmNTg3M2VmIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-868474574 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-868474574\", {\"maxDepth\":0})</script>\n"}}