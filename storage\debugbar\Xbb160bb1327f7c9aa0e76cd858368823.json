{"__meta": {"id": "Xbb160bb1327f7c9aa0e76cd858368823", "datetime": "2025-07-14 22:41:24", "utime": **********.840246, "method": "POST", "uri": "/empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.366346, "end": **********.840266, "duration": 0.4739201068878174, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.366346, "relative_start": 0, "end": **********.748184, "relative_end": **********.748184, "duration": 0.3818380832672119, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.748193, "relative_start": 0.3818471431732178, "end": **********.840268, "relative_end": 1.9073486328125e-06, "duration": 0.09207487106323242, "duration_str": "92.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48450216, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@emptyCart", "namespace": null, "prefix": "", "where": [], "as": "empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1743\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1743-1757</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.02287, "accumulated_duration_str": "22.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7770522, "duration": 0.02152, "duration_str": "21.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.097}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.80963, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.097, "width_percent": 2.405}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.8235161, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.502, "width_percent": 2.186}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.825455, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.688, "width_percent": 1.312}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-544575921 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544575921\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.829003, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "error": "العربة فارغة!", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1190996289 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1190996289\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-323470878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-323470878\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1277989515 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277989515\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1560658313 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2816 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; laravel_session=eyJpdiI6InVVVEpCN25TWHpBQUYvNWdLeGdsdHc9PSIsInZhbHVlIjoiWHorVHpEUXVtMXg0R0xsM21TWStVRWdmNkp6aWlRL2FLcE9UWVNFTEtZbDJhdlRrN2RmeTRNaGZNQUk3THZEWGtHL0pEaXVPNGJ2T2RrMmtJU1ZEaFB4bmR2V29HR3lHMXhxenRmSkNWWThISGlUQThLTEZVYXRPNU8vRVhOb3BOZDRhVG5NS2M0TmlOVEg4YnVGWnhhekNpVXc0SldZQjVFV216T0kwbmZvQWpTZFUzV3VaQkg4NjAzYXRQakI2VllZMjhrUWJnYXFsTzFzZGdEUFBKdWdIVjEzMGd4cHk5cnkydWJsR0txeFAyQlU4YUxGeWhzRXo4U3dDTjVlQ1dqVm02WlUzYmdla2VKREc3cU5LQUtRd1lDRHJZSGFYZUZlNytqdnpUbndMdm9DSFVMQ2JKMzZ1ekRjMkVzc3R1Wi9ZMEQ3ZGlTekgvbzF1TjZEQ2E1M2xHZCt1cUplbk8wSHZrdUhrRElTMG9uUGhwQWtROHZwYUxFYXV3RGE4Y1pMVnh0QXpDWVdMZXVTbHlZemVMMFNBbG5zQndTNmM2TzZoS3N1MXZGU2RhM0hwb214bHNWaWJGeTYwTEx6NGgrQVB1VUEzdVFFeWlvU1NyMmJNbHdDdUVtVlY1ZE9TRkc0RXova3JvQTZhZmxuMnE4M0t3QXZnOHNUcFhRQ3MiLCJtYWMiOiI1MDU5MzYzZTMwZDc1MTYzZDZhZmExYTI0ZTg5M2I4YWI5ODZiYTUzZWE4MDRiMDY2ODUzMmQwN2Y0OTZiZGM5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkxvWnZsUXFYWUc2bmZrMHVveVdDbXc9PSIsInZhbHVlIjoiQWFndksxRGdqZDlhMG5QUXdUNjhaSWVJNENta3Y0dXVoOC9NQjlwTUZOSlBqYWx6SGt4SkR0RlNrdWNSUjNHZE4wT0tPeFA0SXZhbHJtWlN5TFBUNWZjNGhRVFNJZWJkd3dmNVorakg1bnY3clNqVzJHVkVLYWFGVENBM2hXZXZBcFVtWFRqY3AyYkVMbW9VRFhtQnY0d1g2S1pYTjNkM2V5TjNqa1V6aHZBTnRSNjhyWUc4alRIK1VJN2YzRW5xeEsyQVlSUlVDS2RCUUFvZ2xDbUt0VHE3ZXo3Z09lamdOM0tWWm5lS3Y4TjZlTFhFalFqazhPakYyTXZGdVlzdmpycUpUR0h1SDU2THlZbzQ1bnZkZERjbVE4Q2VBSll5SmJVWVVMQmFnTEVzUTRlUVJHWGFTZnIxVncxWFFTN1ZRdktacVczbEo3Y0dQNVBsdkVWcWQ4akhEL0d1alo2MGU0RHpKRDFQdzA1Qy84ZTc1S0xMVmVCdVB3TlBLTGhvYUZ3VlZ1YlFxM1g4c09mNittcTAvODJQYzg4MGxqOFYybk8wVlhXZFM0YzAwcVdjTUtMeTFSQWNUSVpTaGJGeVFCaWN5c2pHdkE1NDVBVnpUZ1NvK0NpdmdSYmJMNWJ2dERTVFB6c0R6MGlUdEo4MlEyQVVKL2g0NW4xVzhlRVIiLCJtYWMiOiIyY2RiZjVhMDkzZTBjYTI0ZTZiMTI1ZjNkMzg4YjEyYTc5ZGRmY2RlMGU4ZGE2NDJiYTc2ZWNlYzM2MmNhNDc2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNPcytJSkUxbEFmcnpkbFVvQitmMWc9PSIsInZhbHVlIjoiL0wrYzhwdzE3VnZBWG05RUdjM3JzMkVIR1pzWXEveFZqMkNESzRWYXNoSU15L3VLaTZycGM1ejYwZ0VFL3hBRDJIVWVvek1NeEN1WjdoR3ZackttN2doQnlKNFhMRzZWandTcXNSSmE5U2hDZWh1NWdsL0dLendnNFRCRHRwVndqRkV0RlVLUHZJRXB5UENBMnQwVkVRMTVxZW5JUmVIV0EwZFdrZ1cweTRjblQ4R3Bla2NsRVlGZ29FQklqdmp2MEtjWk8xbi9pTVZiSUpPUTErUnJRNGx1NTJzVWZtSjFXeVpCZzFMdG1BM1gvdUFFVjU0RGNUZDZ3dmh3Z0xWR3ZSbnhxNlNTQTRUaUFoOXBwUlIxRS9kendlMCtoWDVzV1JCRFROcEJadmFsWGQ2MnFxb2ZBZ0RVbi83Wi84N2ZtMEw4MDh4dmpLQlRZTzV3b0ZQWXI3K0NoNXZaR1NOMVJ5a015Zm5rU0pyZTZhajQxMEFNTXFXSXdOY3FCMlU2SytNQzNIa1RIcTl2ZUZxWVBCOXdwUlMzK0tBMFpHeGpjcXZocDZWQjN2bTQ0V09KemJ6REdITHhiOWE5cWlZSlZHU0RVV0FUTGJOL2hpTk8wT0RSWGwxK3EvZXNNc0p1QytzUTc4akJydHk1TXA2NCtrMFJrQm14Q3R5MHo1MXIiLCJtYWMiOiIzNGMzNzhhZDJmMmNhNWIwNmMwZjEzY2ExNjExZjg0ODg0ZjIwM2FkMDAzNmQ4YzU5NWUxYmJhZjM1N2ZhZDU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560658313\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2053295285 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">11VKLFOC9SwU4msTriW9Qe9qFQYBzUDiUZyyONvR</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2053295285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-474564475 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:41:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1BRkxZUUpFdU1SeUpjRWliTXdvclE9PSIsInZhbHVlIjoicHhMVW5hT1htY0ZtM1ZYQ3V6a3Qrc00xVW05T0psR3QvaDZ1anUyU3JSRXRRTUFUY0JCZ1BiUGhGcGkxbXhzYno3NmZlWTJXR09CbHNheE1JdkpDc1AvZTByQnB5MGZWVjBMOXhteXkxOE80YnpzNWhoVmRqUjFYWEtMNk1NdzNZZ0RRMThiK2M3eTB0SHEzOE5PZnZOaGlGNE9tOUhNWWhaUFd3aUNaWm10YVhNNXU5MjA0VklORkJSaGlQS25wM0lodzh6dDBPaVVXeUVsRkR1dnVvNW8yL0JEUjZzWEVERUp5VXNiSTdPRGgzaHlKVFVDbzEvMy9VdkN6VWhYdUQ0blBCUHlGOWFlZFhWU25zRGpqL3JSWjArS21ZL1krSmhwRjNGWGtKaXp0bGxyMTdoTHA3cWh4M2ZVOFZRUWRzaTd2Y2ttSDZtQzcrcE9KT2lCcUlGUXY3Ky9sREltVGpRbVlPTzBGcEpvY0hyYWw2N2JnU1BYcjRadUJlOHZuU1Bsbkk0UUpnLytsL1BBaU5zdHZ1S2RueFcxQjZEeWd4aW1zVVFTRkw0MkppQ2wrYiszV2FNZ2d1WDZxOTYyUEpHN0YrMmlLTlFiLzczUmdQWldmNEpyZUNqUWJtemUrcDB4cTNIOVlQaUozdUNlRElWWS8wZThZcld5WDMwbU4iLCJtYWMiOiIwODJhNTIzMDU1OWRmMTRiZmI3MjNhMTdjNWE1OGMwYThkNjEzM2FjZmIxNDA5ZGY5NTIzZTRjNWJjNzI1ZjNkIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBNNllKSnZRdFBZY0hWZmRhbHNJMUE9PSIsInZhbHVlIjoiZHYydW9SUDNHa1NkdDB4MDNUVG9kQXRJYlFUU2IxdEhQZThBNWZzT3dYNm9Fd0wrQ0dwZGlPMm5JaWlLR0t4REcxNkNwQjEyTURuRERyaUkzWTUzUEVvOXZMMGRDbjdrdTJORFdRVE1wTlk4QWJsSUF3eWdzTG9PSCtGcUhnRkVTeEZQNVE4WG1qUDFONU1QNWhZanpRTGVxQktjdXpVdENoNTBBYjRhWEtUcGFHMjMyL0Z6ZlJsRmQ1TUU4dXdBZFQ0Z1VDeXU0TlFYanRNTFpNcTh3UmZ2cVcrUy9MbjYwRE5mOGhRNTF2SStmOFl0clhTcjVYbm1EVC9hZnBYeG9jWDlrNVZFU0dSUGgvZFZrdHM2QXdoKzB0TERhS0ZkRWg0YVdXM1h3clh4YTBBM2dhR0hQNmVZYlBZZnZYdnZSUlRCQlNqaTVxbENRQjB4dHd0bU5qdW5uNG1iRHVNamNsTUcxL2FXZ01HeGh2Z3Z6a2dBRXA4SElldWRJbjl4eTRMTFJobUNoU0Q5QWozelhDSEFIb3BjUXlDZ2F4dThJQ3FCaTY0VU1mSmN6aEl6TjVhNFZsdkYyZm80bzljY2U1cXErOE1sYkNjdTNNNEg4TWRGckdDd2o3YUZqaUhVc0o3UUZzdzRFbDIwTkJhU1lkYWVxYzBDdmt6aXhDVm4iLCJtYWMiOiI3ZGZmNzljNzAwNjQ1MjIwYjE3NzRjODQ4MmVkYzlhMjI4YzU3ZjU1OTYwZjcwN2ViOWIyMjgwM2FlNzdhZmU5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1BRkxZUUpFdU1SeUpjRWliTXdvclE9PSIsInZhbHVlIjoicHhMVW5hT1htY0ZtM1ZYQ3V6a3Qrc00xVW05T0psR3QvaDZ1anUyU3JSRXRRTUFUY0JCZ1BiUGhGcGkxbXhzYno3NmZlWTJXR09CbHNheE1JdkpDc1AvZTByQnB5MGZWVjBMOXhteXkxOE80YnpzNWhoVmRqUjFYWEtMNk1NdzNZZ0RRMThiK2M3eTB0SHEzOE5PZnZOaGlGNE9tOUhNWWhaUFd3aUNaWm10YVhNNXU5MjA0VklORkJSaGlQS25wM0lodzh6dDBPaVVXeUVsRkR1dnVvNW8yL0JEUjZzWEVERUp5VXNiSTdPRGgzaHlKVFVDbzEvMy9VdkN6VWhYdUQ0blBCUHlGOWFlZFhWU25zRGpqL3JSWjArS21ZL1krSmhwRjNGWGtKaXp0bGxyMTdoTHA3cWh4M2ZVOFZRUWRzaTd2Y2ttSDZtQzcrcE9KT2lCcUlGUXY3Ky9sREltVGpRbVlPTzBGcEpvY0hyYWw2N2JnU1BYcjRadUJlOHZuU1Bsbkk0UUpnLytsL1BBaU5zdHZ1S2RueFcxQjZEeWd4aW1zVVFTRkw0MkppQ2wrYiszV2FNZ2d1WDZxOTYyUEpHN0YrMmlLTlFiLzczUmdQWldmNEpyZUNqUWJtemUrcDB4cTNIOVlQaUozdUNlRElWWS8wZThZcld5WDMwbU4iLCJtYWMiOiIwODJhNTIzMDU1OWRmMTRiZmI3MjNhMTdjNWE1OGMwYThkNjEzM2FjZmIxNDA5ZGY5NTIzZTRjNWJjNzI1ZjNkIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBNNllKSnZRdFBZY0hWZmRhbHNJMUE9PSIsInZhbHVlIjoiZHYydW9SUDNHa1NkdDB4MDNUVG9kQXRJYlFUU2IxdEhQZThBNWZzT3dYNm9Fd0wrQ0dwZGlPMm5JaWlLR0t4REcxNkNwQjEyTURuRERyaUkzWTUzUEVvOXZMMGRDbjdrdTJORFdRVE1wTlk4QWJsSUF3eWdzTG9PSCtGcUhnRkVTeEZQNVE4WG1qUDFONU1QNWhZanpRTGVxQktjdXpVdENoNTBBYjRhWEtUcGFHMjMyL0Z6ZlJsRmQ1TUU4dXdBZFQ0Z1VDeXU0TlFYanRNTFpNcTh3UmZ2cVcrUy9MbjYwRE5mOGhRNTF2SStmOFl0clhTcjVYbm1EVC9hZnBYeG9jWDlrNVZFU0dSUGgvZFZrdHM2QXdoKzB0TERhS0ZkRWg0YVdXM1h3clh4YTBBM2dhR0hQNmVZYlBZZnZYdnZSUlRCQlNqaTVxbENRQjB4dHd0bU5qdW5uNG1iRHVNamNsTUcxL2FXZ01HeGh2Z3Z6a2dBRXA4SElldWRJbjl4eTRMTFJobUNoU0Q5QWozelhDSEFIb3BjUXlDZ2F4dThJQ3FCaTY0VU1mSmN6aEl6TjVhNFZsdkYyZm80bzljY2U1cXErOE1sYkNjdTNNNEg4TWRGckdDd2o3YUZqaUhVc0o3UUZzdzRFbDIwTkJhU1lkYWVxYzBDdmt6aXhDVm4iLCJtYWMiOiI3ZGZmNzljNzAwNjQ1MjIwYjE3NzRjODQ4MmVkYzlhMjI4YzU3ZjU1OTYwZjcwN2ViOWIyMjgwM2FlNzdhZmU5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474564475\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-495441582 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"13 characters\">&#1575;&#1604;&#1593;&#1585;&#1576;&#1577; &#1601;&#1575;&#1585;&#1594;&#1577;!</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-495441582\", {\"maxDepth\":0})</script>\n"}}