{"__meta": {"id": "X9d0111b9a2a95d72be95def845acf6db", "datetime": "2025-07-14 22:40:27", "utime": **********.748542, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.250865, "end": **********.748556, "duration": 0.49769091606140137, "duration_str": "498ms", "measures": [{"label": "Booting", "start": **********.250865, "relative_start": 0, "end": **********.643551, "relative_end": **********.643551, "duration": 0.392686128616333, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.64356, "relative_start": 0.39269495010375977, "end": **********.748558, "relative_end": 2.1457672119140625e-06, "duration": 0.10499811172485352, "duration_str": "105ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48506480, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.028589999999999997, "accumulated_duration_str": "28.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.67669, "duration": 0.02668, "duration_str": "26.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.319}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.716034, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.319, "width_percent": 2.623}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.732325, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 95.943, "width_percent": 2.483}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7356641, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.426, "width_percent": 1.574}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047739812 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047739812\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.740352, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"8\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 128.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1217135116 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1217135116\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1090295894 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1090295894\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1321961824 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1321961824\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-585101552 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">42</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1TeUxMamxhVS9PQTNJRzFYMytrQ0E9PSIsInZhbHVlIjoiWlJXc2hucHlxcEN2YW5sdDVFRFQ0ZlR6Mi8rTzhVMmVLcmxDYzVib0dBa25YRTBVZWxnaTFXcDJ0SVlyVGNGYW9YaG5ENHpub05rdkhsSlR3bUhRNTZPV2lwVDhWSlhFRTQveVJpMGVxUGpubEQ2SUNOZFhhcmp2YkZsUEp2Y2I4TjBvZXF5cjJ1UFJwMkpTV2lmUENDRmpWL3JYTFZCU05JK2gxaHFpbExmaE1hSlpERVBKSjA5eDNHVnlUOUY2VnVTdmZEdmw2RGQyb2ZBRDVRNGREMlA1NHMvL0hiNGVNL2o4bllFRmlTUzlOZTl5ZGdLNks3aWswN0hSTHJMb0NXQTV1ajBrdG40bC9MdmVjVm5JNk11WGJwSVVHejc5cVdVay9OZTl6aTk3S0NDanRJN2JVc0s2T2g3UlFGYWhIWnU1VlcvQko3YjdFOHB1THFiY05ZYytjaHFlZkVCTWdaUW5PUVV5TjdzNkZCUWxRazZwNUptaTh3amVWem80YXNvbGFuRDQ1elhQRTZYcUZ5d3ExR3FsdlVPVGJWaGE0cnZXeEhrejBwR25kQnFtRDZiRkQxWWdnTGtYQWQ3aGlMRGtFdE1ZYXR5OTQvZ2RHbDE1Q2NJSUlkS2ZLN1h6alZPcFMxRkxJWVhkSm94bFRxRW15QXIvVnNZSmpaeWIiLCJtYWMiOiI0OGYxYzczMDRkMjdhNTRkNTZhZDNjN2Q1OGM4MWQyMjQyZDE1M2M5YzE1YWQ5YmM2YjFkNzk0Y2E0MWYzM2MwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldBL2lkam5VbURnbVc4U0xqekM1WFE9PSIsInZhbHVlIjoiVE84ZGVFNU1EOTY3MjNLc05adjRhbHB0eFc0Tk1lcWxkNk5xVER6TmFBN0Fjdjh3MG42bFNHM2J6L0tXSTZYRUdDaDREQnRpbXZtajl6aUp0dHdHeWRkYVJSbUJoUkgrVHFrUVhOem81RldqT2QwcC93cExuYnJXKzU0ZjNhQ2JDNDRZeEF0L3FxdWIxYWtxaW5FZnh2dVpZSWg2MnNtTHZKSUtXWDlZdkZTUWxSRnJPc1B3RXI2V3g1MU1Vekx0SU1EYndCQ1ZjOGIrRDQyZnM2YW1wTUw5bU8vNVBXU3dWRTdISVpNbjNSbmo2NjZZT3FOM2JrTE9FTjZaN2lOYlYrMmxJUDk5ZE5DdjU3WWpjTU5YVmdjTVBraERRVzZjR2Z3ZUt5emRWNkhhWUdValg2TmJHd3B0UWVWUDZXTjVhUk9nQ09MdTBFbHNac2twZlBvaHJldTlvbm10UCtOL21kNy9ybERMWW1ZbHB3L3VDYUhCc0xTTXJhOXJhVnNoMW83cndqcDVPWmh6Uk8wbllqeFhNNGFjUVQyeENYbHhkRHEvaHNSY1B5Z1FlU0xvQiszOGEySXhwcW9ENlp5bW11TWlvSGJ2akxyaG5KdFdRMXNlWXJBWkNLa2svWkpoZ3lHZmZEQUljRldvMEJhNFhOazdkVHRYUjJBSEtZUGkiLCJtYWMiOiI4MDUwM2ZhMzJjZjFkYzQwNWVlNTAwNmFiMDdiMDIyZGJmYTBmM2IxMDVlNzlhMjczMTI4NzMwOTZmYmFiNDM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585101552\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-335973656 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-335973656\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-38297942 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1DaUE4d2t5d3dodDljYTdTTlBiM2c9PSIsInZhbHVlIjoiejcxelU4Qit4S3poTHk4RjhGakdmWmRqa2dzS3NwOTJ2L08xaEQ3b3pQWm9qNU1iWUkrTjRGclRIb051NEdXTHZtMkNaS0R5RGhjZWJEeGFySC9HUVdRTGFmeDJucGJtRmFQRUgzcDAxVUl1TlFYOVl4dm0raGY4alFZaEk4MDFlOGh4V1EzSHhkNVdjZE1Xa3djcythWTg0cHhHemlHaGF1bHpHMUZENTN2TmhsR0JCSmFTYWZRNW5MdGJBUEpHWmxFUUxMYTZINksvMisxeWV6Y21pSkVkdjlEUC9uaEhucUVaUFZZYnNZWG1OdDlFQ1ViNDFuUlhYcWRmeDMxbktEc05Ubm13WUN4NTNwYWtMdGJabU9uTktiQm9sY1Qwc3BMamxZQnp6dWxWdUR4bkFHb2ppUjRDN0dvekNFcm5sVE9ydlo5b0hTUW9SOW5OYlRPTDYvT0VUOVcxMGJnN1IwVUlRTGRTWGtEQzQ3WGNKdzRYUUdGSVJvTUU5NExhejJTckFkRmxrM1ZJM1R3ZzlDTkltc3R3MEhGRFA5MWR5NERQblRpeSs4OG9KV21hVnQrbEVXYVFWREJaUTlUYXM4bzRKWUNSYlNMeU5FZytWcHRpTjFZUzU4eHZJbFBTN1BPaXlmb3RLVG9QYVFhUHNKMlB6SFVHOWdFVGI3RTQiLCJtYWMiOiIwMWRlNTcwZWU5YTMzMDU2OWJlNmM3OWY4YjM3MWEyMzY0YjQwMjUzOTY3YWIyZmFhMjkzYjFlZGZmOTU3Y2RlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxMNURENEVicmR6dVkzNU9Sd2JqUGc9PSIsInZhbHVlIjoiUkhLLzRCYy8wcFdSZmxhQ2RLQzMzL0hxME9lK1F5Tkl1eXZocUxRL29lNEt3VnRkcGx0Q0hocTlLbUxpOS9UY3FvbVFhUzc5TGJEaUVjMVFiSzRJeXg5ODFYRkszeWI4eFFDdUp4MTVBZFNWZGpBanB6VjhHNUprUWc4NE9xZkZOcFpDTVZ3VXhGY2pIVndhRy9sQ1lGYjltS2trNlV6MTFUeHVMU01hNWczOHhJR05raVp4MkxaSUg1Mkhuc0MrR3pzOVFIMllDWE1UYlE0ejJPOTJ6aXdCdWhLTkl5UCsvZnNUR1MwS1VlaG9xY2VsYmd4VWhKOWdRUUZsbCtkS2ljeEVyL09CN2c0d1dUNlZNellTTHhpNmdYUXBjQ2VtUFNpem9ibXRTcGlzbUhleXVsYkFCYm9RS25Pd3hTN3lwQmk3Slc3ZE82b0lVZ2tnOXl5a0N0N0VBTTdvOGk0R0lXeWUzMjA2TnE1aTRyWlcvT21aaG8wU3RVOUlNTFJOclNWWXdXYkRCTWlPVzRSVTlCTXJTeTFwVW5ENU0zcFV5TUthQjUxVktJWU9uWWJDWnEzQUVHdTZYdWEzTzZkakE0VHZGdmlLYW82UGcxZXQ2eFNxQlVBbUV5ZUVkcmRXdkJ2d0E1Zi9tTy9HWVVGdXdldU12U3J1emxmZEZFYUIiLCJtYWMiOiI4NzAwYzdjZTUyYWViMzY4NDkzNmNiZmE5OGU3MmI2OWViOTBkZjM3MGE3N2I4ODQ0NDM0YzRmM2MzNjE2MzVlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1DaUE4d2t5d3dodDljYTdTTlBiM2c9PSIsInZhbHVlIjoiejcxelU4Qit4S3poTHk4RjhGakdmWmRqa2dzS3NwOTJ2L08xaEQ3b3pQWm9qNU1iWUkrTjRGclRIb051NEdXTHZtMkNaS0R5RGhjZWJEeGFySC9HUVdRTGFmeDJucGJtRmFQRUgzcDAxVUl1TlFYOVl4dm0raGY4alFZaEk4MDFlOGh4V1EzSHhkNVdjZE1Xa3djcythWTg0cHhHemlHaGF1bHpHMUZENTN2TmhsR0JCSmFTYWZRNW5MdGJBUEpHWmxFUUxMYTZINksvMisxeWV6Y21pSkVkdjlEUC9uaEhucUVaUFZZYnNZWG1OdDlFQ1ViNDFuUlhYcWRmeDMxbktEc05Ubm13WUN4NTNwYWtMdGJabU9uTktiQm9sY1Qwc3BMamxZQnp6dWxWdUR4bkFHb2ppUjRDN0dvekNFcm5sVE9ydlo5b0hTUW9SOW5OYlRPTDYvT0VUOVcxMGJnN1IwVUlRTGRTWGtEQzQ3WGNKdzRYUUdGSVJvTUU5NExhejJTckFkRmxrM1ZJM1R3ZzlDTkltc3R3MEhGRFA5MWR5NERQblRpeSs4OG9KV21hVnQrbEVXYVFWREJaUTlUYXM4bzRKWUNSYlNMeU5FZytWcHRpTjFZUzU4eHZJbFBTN1BPaXlmb3RLVG9QYVFhUHNKMlB6SFVHOWdFVGI3RTQiLCJtYWMiOiIwMWRlNTcwZWU5YTMzMDU2OWJlNmM3OWY4YjM3MWEyMzY0YjQwMjUzOTY3YWIyZmFhMjkzYjFlZGZmOTU3Y2RlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxMNURENEVicmR6dVkzNU9Sd2JqUGc9PSIsInZhbHVlIjoiUkhLLzRCYy8wcFdSZmxhQ2RLQzMzL0hxME9lK1F5Tkl1eXZocUxRL29lNEt3VnRkcGx0Q0hocTlLbUxpOS9UY3FvbVFhUzc5TGJEaUVjMVFiSzRJeXg5ODFYRkszeWI4eFFDdUp4MTVBZFNWZGpBanB6VjhHNUprUWc4NE9xZkZOcFpDTVZ3VXhGY2pIVndhRy9sQ1lGYjltS2trNlV6MTFUeHVMU01hNWczOHhJR05raVp4MkxaSUg1Mkhuc0MrR3pzOVFIMllDWE1UYlE0ejJPOTJ6aXdCdWhLTkl5UCsvZnNUR1MwS1VlaG9xY2VsYmd4VWhKOWdRUUZsbCtkS2ljeEVyL09CN2c0d1dUNlZNellTTHhpNmdYUXBjQ2VtUFNpem9ibXRTcGlzbUhleXVsYkFCYm9RS25Pd3hTN3lwQmk3Slc3ZE82b0lVZ2tnOXl5a0N0N0VBTTdvOGk0R0lXeWUzMjA2TnE1aTRyWlcvT21aaG8wU3RVOUlNTFJOclNWWXdXYkRCTWlPVzRSVTlCTXJTeTFwVW5ENU0zcFV5TUthQjUxVktJWU9uWWJDWnEzQUVHdTZYdWEzTzZkakE0VHZGdmlLYW82UGcxZXQ2eFNxQlVBbUV5ZUVkcmRXdkJ2d0E1Zi9tTy9HWVVGdXdldU12U3J1emxmZEZFYUIiLCJtYWMiOiI4NzAwYzdjZTUyYWViMzY4NDkzNmNiZmE5OGU3MmI2OWViOTBkZjM3MGE3N2I4ODQ0NDM0YzRmM2MzNjE2MzVlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38297942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-65396938 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>8</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>128.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65396938\", {\"maxDepth\":0})</script>\n"}}