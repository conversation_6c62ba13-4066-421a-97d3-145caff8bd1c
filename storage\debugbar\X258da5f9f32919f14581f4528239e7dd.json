{"__meta": {"id": "X258da5f9f32919f14581f4528239e7dd", "datetime": "2025-07-14 22:31:36", "utime": **********.717659, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.194332, "end": **********.717675, "duration": 0.5233430862426758, "duration_str": "523ms", "measures": [{"label": "Booting", "start": **********.194332, "relative_start": 0, "end": **********.653184, "relative_end": **********.653184, "duration": 0.45885205268859863, "duration_str": "459ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653196, "relative_start": 0.4588642120361328, "end": **********.717677, "relative_end": 2.1457672119140625e-06, "duration": 0.06448101997375488, "duration_str": "64.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45991128, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00362, "accumulated_duration_str": "3.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6855779, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.812}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7015212, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.812, "width_percent": 17.127}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.708909, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.939, "width_percent": 19.061}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-670339767 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-670339767\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-513851569 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-513851569\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-847056460 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847056460\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1675203161 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532272261%7C5%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImRKT3FSRkNOekZQU3VIejlFVnJsOEE9PSIsInZhbHVlIjoiU09oTDltNFU4NlhsanhHZ0xjcmloUUJUOCtOd3pSa2xEVDFGWjlKcUE0c2lUNDI5MWtzTmM1OFpCejFycXBjaG9VamVqVFFISlJFUW9kc0hMSEhyRitNR251MStpdmIxZU5kSmFCTmR4UUNZb2Z3dktBdm12S0x0c29CWURKWU5XUUJ6MXFub2lqakMvMFhldmV2QlE4YjN1VnVkM1o3aVZEUGpQWTQvU2pTS0JqYVhGQWhpeFVZc3FZRk5scTBTejlOT1REbCtFa3NzRU1CZlF0QnBoSlBUejhCOTR6dWFjcHpQaDBJa2lUS2sxTWc3VGJjdGZycEJxL1U5eFliWWFhb1RJMlNrbnNsY0ovaFBkT2pDZ1MzaGdnLzd3SU82Y25wb3RRRERpa0YvTUYzTFY1THZRWE5JOXVEQUNhTnZGL3RJOTFGdkptZDRsR2RHbHRmbUc3QjRZaXNYcTdONW1lcWJ5aEhrakNWQll1SThzZjgxbFl2THgySnZVY1FJTURDNVFUUmJkTVkxbFJqMFJpU0UzQzRjWXdkNkQyeGtUamQxRXlaTXk5YUpDWTcrNHhjbk5MeFhONkdYVG5vQUlMNkJuSjBmdUFzaGovMlY2bi82VWh6R2tjVFFvbnE5blFyUmFHbGZROUdLSUhqNWg3Qmlodm1HeHBVWGxQNlQiLCJtYWMiOiJkN2NlZWVjZjU0ZDNkNmJhMTNiZDcyNjNkZmRjYjRiNjk5NmY0ZTJmNWQ2ZmFiZDg3NmZjYTE0ZjhlZTVkZTA0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZHR3FvTWJtWmd1cGRLYkpoS0NrSHc9PSIsInZhbHVlIjoiQ2xiR0ViZG9NUzdoNHMwU1Z1cHhORmZ5OHJwTjZNck52NzJVQ0VhbXlPQXMyaVJ3dXF0ekRtOTlMWm9VTEg3cktYdkJ5bW1ZUS9BREhrVWpMVEljaGMxckE1SDVWYmFMYkZDS25nK0FURzlnRm8yV2tIOWJPU2lDSUs1KzZ5Smt5WUUyMVhYVXRHaFc4MDY5VTEvc0hDd2xTNG9xY2llMmFRckNGcDFrWTYxeUYxdVR4MUFIZkx4Rm1oa1dDSmdSVzhramNiU1pKb0ZnOGhja0IrNjloYk15dE9UbUtrQmZlUHE3RStvemY1VGVzRFNmaFZIOFg4dllTVk5mUEFMMm8zREJpZTIxMVRIRTVNL24wUWdDQnBMRFAxVmd5M3Y4dHR4NTNybzUrSzZrUVBFUG5GckkyZTM5Q05mUUtoeVNvbGZMZ00rVjN4QzJ4alFSNnRmSXRaOFlrcldzTGFTNHNMNEU0U211NUhuZm1pZzliVDhLdnhPbC9jT2doU0p1RUt2MHd3VFp3T09CL0NrdlBXNkZpVkQzN0U5Zzc0clVQeG5xZ2xaWmdCdnY4UHBGcVloQ3p3NWpTcCt3UUl2L3dxZXBpY3pmRFl5VFpIVFkwVnBSSUZmNEQ5aXhRWStzcWZRbjdJaU1MN1dlRXBIZHpJNlU3ejhSbitpcFhSZ20iLCJtYWMiOiIyYWVmY2NiYzBkZDBhYzMwZWJmYWVlMWQzYTdhNWYyMmQyZmUwYTFhZDQ0MDBiMzc3ODUzMTZmOGQ3MTlhNzdhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675203161\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1656031588 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9tDxXjbbhjxiDJbPcLBO2bj3xslZ4VoGjLB0sApN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1656031588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2097052094 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:31:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikxqc290Z3dlRUhLTzN6NFhNTUZFa3c9PSIsInZhbHVlIjoiSGxnenF2V2tqbk9NNzhTMmpwR21saDNkbHdpNTg3akNqUDBadU9hK2JrZHMvb3dzem1JNjk4Vnd6Uk1tR3FqWXFJZ1FUTVpqTXY1Z21aMXVFK1h2WkV6TkhMR2JqZTNlQXpLZWJuRXhRaFY4cGtia3FqVmdGbmtTbXp5Mi9EYjFydEN6RnpWc3FoRkNkNng0Z1hNVEh5OTd2Y0s1bk1wRHdVR0FIZk1jWnVGei9sRzhmbm45MnJYeHBWMVF3aXpYMDBvUGwvVU5PTzBmalBrWXFIWXgvYXBqSzZFbkM0WnQ5RVg3NVdyOTVHdUhUYWNveEJ5M3c4NkcxcVR2VzRBZm95M0RRajBWOGNKekVsektSdlMyTzVyb1VCbjFacWZKdzIrM3BJamo2ZDZZc21xcTdwM09hV3hUbWtIbytXa0xzVlA2TzAxT0Y4bk9mbDk3cm9wbHN5eXFRRTNKTXpjWGFQZUp2QlR3TnZ3N2p5OTNpMXIzR0ZaTzFxSk1BbkcvK3hSdnVmRk9wOU1mcDFLeThGNENONGw5VXQyd3hnWVQ3akNnQ0h4c2xnOWtSM0p6d3orcE0wcXFKeFlucFd0WU8wS1FPUmV6UVFJWVBqWmZMWkNtUWtDVEkzOHI0dTdCc29hbW5vK0d0VStSNi9ra1Z2T1J5djkwRmhVUk1FemoiLCJtYWMiOiJmMjQxYjY5ZmY5NWE4N2FkMGEwMGI2MDVlOTkwODU0ZjU3MTM2NDk5YTJiZDZhYzg5N2Q5MzEyNmZiOWVhODA4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InUxWk1kWDVWYWJCbGxlZXpHMUZFS2c9PSIsInZhbHVlIjoiUzVSTENoSVJ3c2VhRnV1V0k0ZXF3Y2pMZ3FDcERsUkVzT2xUNVYyS1o2SjBmV1dleFQ0alZYaFMrb2MyZ3k4UUNBOCs2Z3VRNGh3d0dqNThOTGdmMjFWS3ZuM2ZHeWxCcUtFSGljVE5xVEx6R1pjMk4wTjhkTDE1ajRmTmUrdjN3RTNGbWo4a2czV2JBdEFLZ1phckk4MWo0RkZ3OUptallmZ1VYMlBzcUszSFQ5T0dqeWJhdFdPOWpFc2JzNE5Xa3phYmhrQURaMk9yZW8zK3RqbEw2QlVFelBCMzZLR20vZ3NMaXNIQW9xOEVIU1FvWXZWRzFWTlR5SnYwc3QwTk82VFBnMnRGbWdWKzFmMTg5NkJIdUYxUDJqbHpiVW1YU2pBc21RK3pEcTVKaWdVVWRSTm9id0V4VytvMEVTVnNOREo2YnRxV3MyRyt2dTNCMmthQzV5a2lKc09FejdLVnBRbFN6eEs0TnlrbVlZZDFhSnJ4d3JzZHhDUC9qZGlwSDZ1RVBFNzJnUFJZUnZ5S1R3ak9wNWViWGRNK2xKSVpkSGFDTGZ5aGlBK29NVHRFUUoxbGFiMTY2MTlpWFFscit5OGRJTFN1NDVUY2x5aXd4dzNIelEyQ05LOXpjajlaZ1pLSFQ1cTZKZWNjLzRWVmNRajB6VVlnSnhPdlYxa3AiLCJtYWMiOiJiYzc3MDIxNDRjM2Q0NjdkNGJkYjVlZmM2ZmVmMzE4MjdhYWJmOTJiNDI3YmQxODM5MWE2OTUzZDRjYTQ2NjhlIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:31:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikxqc290Z3dlRUhLTzN6NFhNTUZFa3c9PSIsInZhbHVlIjoiSGxnenF2V2tqbk9NNzhTMmpwR21saDNkbHdpNTg3akNqUDBadU9hK2JrZHMvb3dzem1JNjk4Vnd6Uk1tR3FqWXFJZ1FUTVpqTXY1Z21aMXVFK1h2WkV6TkhMR2JqZTNlQXpLZWJuRXhRaFY4cGtia3FqVmdGbmtTbXp5Mi9EYjFydEN6RnpWc3FoRkNkNng0Z1hNVEh5OTd2Y0s1bk1wRHdVR0FIZk1jWnVGei9sRzhmbm45MnJYeHBWMVF3aXpYMDBvUGwvVU5PTzBmalBrWXFIWXgvYXBqSzZFbkM0WnQ5RVg3NVdyOTVHdUhUYWNveEJ5M3c4NkcxcVR2VzRBZm95M0RRajBWOGNKekVsektSdlMyTzVyb1VCbjFacWZKdzIrM3BJamo2ZDZZc21xcTdwM09hV3hUbWtIbytXa0xzVlA2TzAxT0Y4bk9mbDk3cm9wbHN5eXFRRTNKTXpjWGFQZUp2QlR3TnZ3N2p5OTNpMXIzR0ZaTzFxSk1BbkcvK3hSdnVmRk9wOU1mcDFLeThGNENONGw5VXQyd3hnWVQ3akNnQ0h4c2xnOWtSM0p6d3orcE0wcXFKeFlucFd0WU8wS1FPUmV6UVFJWVBqWmZMWkNtUWtDVEkzOHI0dTdCc29hbW5vK0d0VStSNi9ra1Z2T1J5djkwRmhVUk1FemoiLCJtYWMiOiJmMjQxYjY5ZmY5NWE4N2FkMGEwMGI2MDVlOTkwODU0ZjU3MTM2NDk5YTJiZDZhYzg5N2Q5MzEyNmZiOWVhODA4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InUxWk1kWDVWYWJCbGxlZXpHMUZFS2c9PSIsInZhbHVlIjoiUzVSTENoSVJ3c2VhRnV1V0k0ZXF3Y2pMZ3FDcERsUkVzT2xUNVYyS1o2SjBmV1dleFQ0alZYaFMrb2MyZ3k4UUNBOCs2Z3VRNGh3d0dqNThOTGdmMjFWS3ZuM2ZHeWxCcUtFSGljVE5xVEx6R1pjMk4wTjhkTDE1ajRmTmUrdjN3RTNGbWo4a2czV2JBdEFLZ1phckk4MWo0RkZ3OUptallmZ1VYMlBzcUszSFQ5T0dqeWJhdFdPOWpFc2JzNE5Xa3phYmhrQURaMk9yZW8zK3RqbEw2QlVFelBCMzZLR20vZ3NMaXNIQW9xOEVIU1FvWXZWRzFWTlR5SnYwc3QwTk82VFBnMnRGbWdWKzFmMTg5NkJIdUYxUDJqbHpiVW1YU2pBc21RK3pEcTVKaWdVVWRSTm9id0V4VytvMEVTVnNOREo2YnRxV3MyRyt2dTNCMmthQzV5a2lKc09FejdLVnBRbFN6eEs0TnlrbVlZZDFhSnJ4d3JzZHhDUC9qZGlwSDZ1RVBFNzJnUFJZUnZ5S1R3ak9wNWViWGRNK2xKSVpkSGFDTGZ5aGlBK29NVHRFUUoxbGFiMTY2MTlpWFFscit5OGRJTFN1NDVUY2x5aXd4dzNIelEyQ05LOXpjajlaZ1pLSFQ1cTZKZWNjLzRWVmNRajB6VVlnSnhPdlYxa3AiLCJtYWMiOiJiYzc3MDIxNDRjM2Q0NjdkNGJkYjVlZmM2ZmVmMzE4MjdhYWJmOTJiNDI3YmQxODM5MWE2OTUzZDRjYTQ2NjhlIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:31:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2097052094\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1793734518 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1793734518\", {\"maxDepth\":0})</script>\n"}}