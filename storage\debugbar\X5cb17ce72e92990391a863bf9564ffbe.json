{"__meta": {"id": "X5cb17ce72e92990391a863bf9564ffbe", "datetime": "2025-07-14 22:40:41", "utime": **********.370397, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532840.929395, "end": **********.370412, "duration": 0.44101715087890625, "duration_str": "441ms", "measures": [{"label": "Booting", "start": 1752532840.929395, "relative_start": 0, "end": **********.302818, "relative_end": **********.302818, "duration": 0.37342309951782227, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302829, "relative_start": 0.37343406677246094, "end": **********.370414, "relative_end": 1.9073486328125e-06, "duration": 0.06758499145507812, "duration_str": "67.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48506640, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0033699999999999997, "accumulated_duration_str": "3.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.332433, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.644}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.343292, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.644, "width_percent": 15.727}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.357352, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.371, "width_percent": 15.43}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.359357, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 90.801, "width_percent": 9.199}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1314683018 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1314683018\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.363412, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"21\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 336.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1291881069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1291881069\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1057706173 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057706173\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1745904920 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im84TnU1UnhsUVZXamJQekFQc2pGbnc9PSIsInZhbHVlIjoiaEhmdUZRVUJZSWZoUnNEMmwzS3dzOXpLZzB6S3lob3RpQ2I2WlpCSVpkTElUblM1dGlLbENpZXE2K2srZ29BWElxRVNTUUE0d1ArVFYrUnluYnkvSUg5UnpEcDF3eDQwV3pGUFRFS0dTbWxGd083ZnhuZXptQ0RFbHRyaTNOWDlhRU8rUDJnVTkxbzhkQmd2RERXOHAvNkNFWDYzKzFnaTNDZEhzTE4xU3pGQnJuQ1FQVGhoZFc2NFdvOENuM1VHOEovQmJKeG5OZ2p5T0Vjd05Nd01TeTJDbjhZYnZ6UzZkL016ZlpreWt6clBRdnY5TTNkbks5TE5ZRVRnSU5ZZUVJVXJIWW5uRUhKSkZrQmxPOGhXRHdrZUxWT2NFUTlTR1RCVE1EKzJlMWJvRFF2Q3dvWEpSelRjaXlaQU54K0hxdXYrN0JXZFc1eE1BK0FzSUtXUjRGcDJMSCtxeStlQmdCNzhPUWFPNDRUTnRocjIwSlZRN0NqL09XcEpoWEJ0SW11dFd6YlZ6emljRG9MT3h3TFlMbk9TZjRTTEFQZDlCTWI1Y1BxVzBFK2FaYU9PT0FKS2ZUQ0Z2VmVINnJUOVRrWHZNK05kZElLOEo4UFUyNnY1MEVWNGZ6aFJ3OEYycXV4cHVLUWszVTRzSE5JaVFielNTWDJadmp1Wkk1UWciLCJtYWMiOiJhNjRlYTgyOTJjMThiNzNlZDI1M2ZiYzlkYTBhZjRmYjRmMWIzMzM5OWE2MGNlMzBjNDJiYzI3NTdkYzVhNmI0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVuTFdDTWNxcWh3cjZyVllxbktWNFE9PSIsInZhbHVlIjoiMUVZR3Q2Rzl2TXVXUW1aelkrV3pxcFNQSWVmbkNUeHNIY0VLNWZVeWd2MWM1ZGd5R1BOamp0WHZOTTlPZ0NxbkhJWXdldVFwZWpidEZIZzhGZElPTVo0QlhnZzFZMDRZSS9Ob2ZKOGpScjk5K1FIcUZ1K1pkMC9hcEw0YlRNQWVOZ3k1Tk9WWjJ0R0sySmhnTkNMaG44TXFHcUxDVmdyTHhURllGL3J5dG1RQVFQd1UrZXY0ckZCQVhMdmZCUGlvb3R0VklKcXRVL1dwcmRMU2o5NEFNbDNLN2tmVDhmT2R4V0VNTVZJeHB5YlVSZEhGM1RjelpmcGk0Q3FzZjNPbGNWazlBL3M0SGViUXY1WlQrWk15U29CeVFsN2Ryd2FGZXJSeDZsdWpBMDhwcWNSTFJSdU45S0QwVThlSzlYTWdUZDNWMzRPc1dHOHRpRjk3MEcrb1kxK0FIUVRJVjhKOWs5dHgwZkZaNzFubWxKUWdzSVp5dWUyOFRseXB1NW55SDFza2NnZG11OVo1cEtRcEtGMjE5RTc1TDZYQmcyRER6czNLb1NBTlBHU012eEpOT3c2Y1h5RmNieERoL21QckpmbEl0SW96QzFVTVM0aVR5Z0lvYmNOdENFOG1MVGVPczZnd01HNEFRQ2pobTJSMXRsU1d4bGtjMHBIWjE4ay8iLCJtYWMiOiJlNjVjNmJmNjJkNTA3NDYwYzA2YzAxNDdmMDNiOTg2YjFkYjc1ZWQ0YmEzMWFjZjg5MmY3YjViMTY4ODA1MjI0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1745904920\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1959505710 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1959505710\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-59221447 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlowRyt2Y2k0d1dHNk93c2NBM1FmSVE9PSIsInZhbHVlIjoiVVN3UE9UN0JqMFBjTjN1TUxPYy9aZW9DRGhmbUJQR0tnN1JuVkxlZ2lSVmU5enMxQmxzaFlMREF6RG5sbjdJUVYzRlF1SWFneitwNlFrSm80WWVRL0xlVVhYVTlRVjhZZEpRUk5YbkxkYkE4QXFtNjFGd1dMV3dCTVlSaVM3bTgrazlCc1JwWHd3bXA2NE9nQnBOek1YQlVwUUIxSjBpRFhybE9ITUZiM21uQ3JMVjhYQU5nZncvZWhteG9FMUwra3Rzb2hkaU1oc1hJcmhRNEIwUlFCSkpWZWFpSm4zbmE5Rk5DOEhVSmVrOHQvQ1BFU2tNTlYzM0VJVEFzeEtNM0tTRnBNcmdaYUtwU1ZGcE1OdXlkc1d0YUtuZjhxR3c0YUlRMGhtSVdLd2VZdXFSa2xOUXJyMEtUTlN2VTNvT2ZXbGVQK05YOFZaS0xQZlN3ZmxzbkxDaGxBTzBRSTcxNkZhVE5NaEJHbEUwM2xWMmRRVjVCeTFCSUZ3SHB5dWFwSm5LSC96N1kwMTN0WHJsTWtMdXJqZksreFJHWUZEOG8vWUR3YmlpWTMxeVlYVzRlNWF6SXFudkhTbTMyMWg5WTJmTk9NRDhHbldVLzRhNVdlWkEwM000RVIyeC9Bb3ZLSmEveStDMlk5T09Hb3hBeGNXekdRYUdKZE9wVWVzODEiLCJtYWMiOiJjZjBhYTRkYmY5MzMzYTcyNTI2ZjU5YmJjYmE2ZTNmZjI1MjE4NjY0YmM4YjhlZjkyMjA3ZGZjYWI0YzZlODY2IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImQvSGZMMUF3b3hCVytEaEtQckhzV1E9PSIsInZhbHVlIjoiMGhlWCszVXowNVR3SFJ3U0xWVFVHVGovZjNiVWVvb1QvcnJySU5CaUQzY29YbzR3WFBQVE9SM1NWU3dTYWswTnhwUmtKNlF3UGJScm1DTDEzSG5jK1FUdFcvZlU0MnhLcUVMRGlncDlrWjNZcHRzYnRNK0NxbFpHZlp3VE1uMEh5Q1kzL1d6dndkSWsyMU4vTDFrWEhmbXErRnZjZjR6K0g0UnduR2NZdGM4cy9FZ0JWOVpKK0FNdlVWdFpHSG1PWSt6RlpmbnpNYktmM001aHg4VlA3MXNFSFp0UkpJMnM2d3Zpcm1ha0loRFVsOHZXK3k4VHQwTGdacWQvdHBDcDVlRURGVXg2NmIrbGgwQXluUjVqOUU1SWJ1NHBtTzVyTzVoMGp1aGIxQkRvSGNpVm1EcWVYZFU5bU82VitpTXoreEoyQlVhcEZCdmRnR2JxUExpVDV1WmlPMlhrY1RkcFBsMm5HRld3UWxLZDUwQ2M2UFFWS1o5Qk9JVkN2cXU0SE0xZWoyaWlMMTAyd0xzWW9BTnRySWRuZWNqazE3NEdEM0FzRitPU0hxWC9SLzh6NHVFMGhDNGNRdUlxTWpPK0oxZUo2SFVxZk1BVnJrdkN4NDF6Ly9IMWFMUHdsQ0JsWFFEcGRvdElqdVlXMTc1SDhrMjk3ZFhnRG5XYmxMS3giLCJtYWMiOiI2ZmVmYjU0OTVkYmQyNGY0NmQxN2IzZjkwMDM0NTBlYjBkYTNjZTA2ZTJjODAzZjMyNDAyY2NkMzdmMTA1NzJiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlowRyt2Y2k0d1dHNk93c2NBM1FmSVE9PSIsInZhbHVlIjoiVVN3UE9UN0JqMFBjTjN1TUxPYy9aZW9DRGhmbUJQR0tnN1JuVkxlZ2lSVmU5enMxQmxzaFlMREF6RG5sbjdJUVYzRlF1SWFneitwNlFrSm80WWVRL0xlVVhYVTlRVjhZZEpRUk5YbkxkYkE4QXFtNjFGd1dMV3dCTVlSaVM3bTgrazlCc1JwWHd3bXA2NE9nQnBOek1YQlVwUUIxSjBpRFhybE9ITUZiM21uQ3JMVjhYQU5nZncvZWhteG9FMUwra3Rzb2hkaU1oc1hJcmhRNEIwUlFCSkpWZWFpSm4zbmE5Rk5DOEhVSmVrOHQvQ1BFU2tNTlYzM0VJVEFzeEtNM0tTRnBNcmdaYUtwU1ZGcE1OdXlkc1d0YUtuZjhxR3c0YUlRMGhtSVdLd2VZdXFSa2xOUXJyMEtUTlN2VTNvT2ZXbGVQK05YOFZaS0xQZlN3ZmxzbkxDaGxBTzBRSTcxNkZhVE5NaEJHbEUwM2xWMmRRVjVCeTFCSUZ3SHB5dWFwSm5LSC96N1kwMTN0WHJsTWtMdXJqZksreFJHWUZEOG8vWUR3YmlpWTMxeVlYVzRlNWF6SXFudkhTbTMyMWg5WTJmTk9NRDhHbldVLzRhNVdlWkEwM000RVIyeC9Bb3ZLSmEveStDMlk5T09Hb3hBeGNXekdRYUdKZE9wVWVzODEiLCJtYWMiOiJjZjBhYTRkYmY5MzMzYTcyNTI2ZjU5YmJjYmE2ZTNmZjI1MjE4NjY0YmM4YjhlZjkyMjA3ZGZjYWI0YzZlODY2IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImQvSGZMMUF3b3hCVytEaEtQckhzV1E9PSIsInZhbHVlIjoiMGhlWCszVXowNVR3SFJ3U0xWVFVHVGovZjNiVWVvb1QvcnJySU5CaUQzY29YbzR3WFBQVE9SM1NWU3dTYWswTnhwUmtKNlF3UGJScm1DTDEzSG5jK1FUdFcvZlU0MnhLcUVMRGlncDlrWjNZcHRzYnRNK0NxbFpHZlp3VE1uMEh5Q1kzL1d6dndkSWsyMU4vTDFrWEhmbXErRnZjZjR6K0g0UnduR2NZdGM4cy9FZ0JWOVpKK0FNdlVWdFpHSG1PWSt6RlpmbnpNYktmM001aHg4VlA3MXNFSFp0UkpJMnM2d3Zpcm1ha0loRFVsOHZXK3k4VHQwTGdacWQvdHBDcDVlRURGVXg2NmIrbGgwQXluUjVqOUU1SWJ1NHBtTzVyTzVoMGp1aGIxQkRvSGNpVm1EcWVYZFU5bU82VitpTXoreEoyQlVhcEZCdmRnR2JxUExpVDV1WmlPMlhrY1RkcFBsMm5HRld3UWxLZDUwQ2M2UFFWS1o5Qk9JVkN2cXU0SE0xZWoyaWlMMTAyd0xzWW9BTnRySWRuZWNqazE3NEdEM0FzRitPU0hxWC9SLzh6NHVFMGhDNGNRdUlxTWpPK0oxZUo2SFVxZk1BVnJrdkN4NDF6Ly9IMWFMUHdsQ0JsWFFEcGRvdElqdVlXMTc1SDhrMjk3ZFhnRG5XYmxMS3giLCJtYWMiOiI2ZmVmYjU0OTVkYmQyNGY0NmQxN2IzZjkwMDM0NTBlYjBkYTNjZTA2ZTJjODAzZjMyNDAyY2NkMzdmMTA1NzJiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59221447\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>336.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}