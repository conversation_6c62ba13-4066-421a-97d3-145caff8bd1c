{"__meta": {"id": "Xd7bd2325928d41dcc39147e87d424182", "datetime": "2025-07-14 22:41:00", "utime": **********.51202, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.033356, "end": **********.512036, "duration": 0.4786801338195801, "duration_str": "479ms", "measures": [{"label": "Booting", "start": **********.033356, "relative_start": 0, "end": **********.414172, "relative_end": **********.414172, "duration": 0.3808159828186035, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.414182, "relative_start": 0.3808259963989258, "end": **********.512037, "relative_end": 9.5367431640625e-07, "duration": 0.0978550910949707, "duration_str": "97.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48491968, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.024259999999999997, "accumulated_duration_str": "24.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.45527, "duration": 0.02034, "duration_str": "20.34ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.842}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.483667, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.842, "width_percent": 1.484}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.4972122, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 85.326, "width_percent": 1.814}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4989362, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.139, "width_percent": 1.195}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.503256, "duration": 0.00283, "duration_str": "2.83ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 88.335, "width_percent": 11.665}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-251111853 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-251111853\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.502183, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-2132754038 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2132754038\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1945145387 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1945145387\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1367682373 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1367682373\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-518232939 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; quickly24erp_session=eyJpdiI6InZxMnJxUEM3UDJLVTcyT3F2RCswWWc9PSIsInZhbHVlIjoicFJ4MnZJYi82WEpZMW1iVnFLSUp4SG5Eb3RLQjg5THJvMC9aRHlqSFdSTlgzZ0FEWndBSk9EbmtwQmhLcXp0VGJQVGJMdG1SU1BhbDF2ZTB6SS9JWmhqMC9CaldJYmtYSFB6ZE1UMW1UaUxkTFJjNGFsaHR4L1g3V3gzc1NhSUpUeDRhRi9KQ0tTSmY2WGhYdjV2dTdGYnhEa2ZvYWdIV1lmV2lpS0p2WGFFOUY3VHJNUk9iVGZCT2xPRFlhWktkZkczWTlQWnlsU3BQZWJhTGRHWGJERlFRMGtIRUkweWk0Q29acGJNZHN6UE9KT0JyQTFIcDBaSU13MUx2YWllSlhNU1lEbjhJclRZQ2lCVFg5YWdpM0lYTXpBaDNCVHlvN0JqVVNMbm5OQXZlMklMbDFpWmVMbUt0TjZPa3ZsWExxbmNMZHVQVEVSQ0UxdmZvNGxwd1ZXSTQzY0xrMHI1Ym1LTE5YWHYwcXN0VHdNSkQzeEFneHdVb1EwWVNzNDBkYk5ua0MxT1ExNXBRc29vMGpINWIvdytGWUl2Qm5WNnJMMXF5Q0dPc3NhUzAzZ1FWd05NNStkcmw3ZjVIdzROQmdCWHBqZXZVTmVXYTZFWDVkcTJLenRHZnY3TlRtZ3l3d3BYTko5SkJ2dnhuSlhncUtSSmRYQUoydkw1UDAyU04iLCJtYWMiOiIzYzFlOGZjMzA4MGZiZDkzYzY4MjRmZGVkOWQ0NTgxZWRmZjlmMDA2MzZkNTQ2NTYzZjI5MzJhODQxYjlkZjIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518232939\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-368119387 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368119387\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1319436032 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:41:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpueGlvMkZFS3NRRlNPZGJOQ281ZXc9PSIsInZhbHVlIjoiME4xWlhJM21ucXVOdTZ0WnJ0bHV6MTlaTVgwS2RBUDlZNFZoZFZXTnJSandOdUdJU3NPYTQvWlNRWFBCaHdNSXpNZ3hacGx2VW05MnJjQlRSVjBmeVliUjg3VVAreWNHZ2xDNUROZERqSkFXZWVVamZqckVQLzdkK2taYURuR3Q5S3drQnQrdzBGS0lYS0xFYkVXcFUyazhPYmN2S2dlOVllQWh6anFndElacFFCb0g0L1daalZDS1pFdmh2WUk1bGh4WnNjOW9vSmNhMlJ1NHBNVEtQanF0TmhHRjdwbzFGWXo5a21lNjF6ek4yd29OWWJxU1VlQW5Zcll2eVU4VkVoclhTWEphRndnSTFUZFZMUFhsTjhRUmVXaWpHbk1XakpMbmt0VWJhUFUvb1pDb2l1UmtXMVd3RlZFSkVRVjNHeUxselJBZEpqKzJpSXlObjhjU2QrenZhUjVDWmVUZUNOdmhHclU2d0lFQ1pFb3Z5RlVLeTFlT2lHSjhvMUhPektKOXRuTTVDQkQ5Um5ycFY0V04wYkpaSHVjUkM2cVc5RUF2aW1WTWNTazcwT2FTZWxTMTV5cE9TWFYrSjRpdDV4WWlXL2Vkakp4ZzE3QURyZHovUkNaOWhYWnkrT2t1bjQ2YmU3Q1JpSC9Xc2JQOFRDTGJBeGp3TSt4MEIycHkiLCJtYWMiOiJjNThhNDYyMmJlOTBjNGRhNmQ5ODZlYWYxNzg5ZGI2NWQ4Nzk3NjA3MzZlMDA3NWE3ODcyYTQzMDUxNzQ1NGMyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJEemNjTkIvODJZR1RzYWg3d2wyRWc9PSIsInZhbHVlIjoiNGFOelQySVBKYms3eXdFem1vYjlOek80UDB0ZG9uT1FqRHRZeDNDNEFxMUl5c0dSbnlVdVg2OXBBZVhDUWVNTXVOY3ZoNmw5Q3l0czZCOXBsdnJNUk5xWE94QzV0WUZaVjB5elpzbWRMMmlnUnN4UWxvak5uQnpteXFFNW1HNENvblBnd3hPOXRkVDQvUExEeWNTcTRDL1dud2lmVE1SRkxOUWNCdFlsNUZnaUpXWllrdWVUVWtXQzZFQ2JKWXo2TVNUZlA3K1lwZWxISzhSMFRiNnNRYTVONzJPWS9TNTFIR29xQmpHZ3dJUHRFU1FKUmxwczFCbTRCQml4bnV6UFhlMmJGMGxCRWx3WGJmSVppUGlWZEFhR2poSEs1b29YY2toRFhrMEpTZ2JiYVl4eG5QT0hsbFR6enJ6bEV0RUxaSllwamRpaC9YQlJTZjhLVkpMckx2ejdQNzJTaHgzWGlLc21PVW5VdlFnU2hMZnVWaGhXYUsxaE5wOTErU2pkdkFuMy9RWkd0K0E0OW1IVDdZTENOUmtzYW5XRmdzT3hJU0hPN3lvWDN4MlhQdDYzUm5nazJwNTJxY0VHSFZxR3ZyL09ka2FNdlNlbmI1bGZubkVERkdGWXFHQUJPNi9yWnpsOHRBeXc5QTE0TjFRYkdCNW4wVDZKQXhaay9iK1MiLCJtYWMiOiJlMTYyZDcyMmNhYTFmMWRkYmFhNTNjNTkzNjZiMmE4ZmI1MmRkMmNhNjc1NDdhMmViMjgxM2M1Yjc2NWY3NmU1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:41:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpueGlvMkZFS3NRRlNPZGJOQ281ZXc9PSIsInZhbHVlIjoiME4xWlhJM21ucXVOdTZ0WnJ0bHV6MTlaTVgwS2RBUDlZNFZoZFZXTnJSandOdUdJU3NPYTQvWlNRWFBCaHdNSXpNZ3hacGx2VW05MnJjQlRSVjBmeVliUjg3VVAreWNHZ2xDNUROZERqSkFXZWVVamZqckVQLzdkK2taYURuR3Q5S3drQnQrdzBGS0lYS0xFYkVXcFUyazhPYmN2S2dlOVllQWh6anFndElacFFCb0g0L1daalZDS1pFdmh2WUk1bGh4WnNjOW9vSmNhMlJ1NHBNVEtQanF0TmhHRjdwbzFGWXo5a21lNjF6ek4yd29OWWJxU1VlQW5Zcll2eVU4VkVoclhTWEphRndnSTFUZFZMUFhsTjhRUmVXaWpHbk1XakpMbmt0VWJhUFUvb1pDb2l1UmtXMVd3RlZFSkVRVjNHeUxselJBZEpqKzJpSXlObjhjU2QrenZhUjVDWmVUZUNOdmhHclU2d0lFQ1pFb3Z5RlVLeTFlT2lHSjhvMUhPektKOXRuTTVDQkQ5Um5ycFY0V04wYkpaSHVjUkM2cVc5RUF2aW1WTWNTazcwT2FTZWxTMTV5cE9TWFYrSjRpdDV4WWlXL2Vkakp4ZzE3QURyZHovUkNaOWhYWnkrT2t1bjQ2YmU3Q1JpSC9Xc2JQOFRDTGJBeGp3TSt4MEIycHkiLCJtYWMiOiJjNThhNDYyMmJlOTBjNGRhNmQ5ODZlYWYxNzg5ZGI2NWQ4Nzk3NjA3MzZlMDA3NWE3ODcyYTQzMDUxNzQ1NGMyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJEemNjTkIvODJZR1RzYWg3d2wyRWc9PSIsInZhbHVlIjoiNGFOelQySVBKYms3eXdFem1vYjlOek80UDB0ZG9uT1FqRHRZeDNDNEFxMUl5c0dSbnlVdVg2OXBBZVhDUWVNTXVOY3ZoNmw5Q3l0czZCOXBsdnJNUk5xWE94QzV0WUZaVjB5elpzbWRMMmlnUnN4UWxvak5uQnpteXFFNW1HNENvblBnd3hPOXRkVDQvUExEeWNTcTRDL1dud2lmVE1SRkxOUWNCdFlsNUZnaUpXWllrdWVUVWtXQzZFQ2JKWXo2TVNUZlA3K1lwZWxISzhSMFRiNnNRYTVONzJPWS9TNTFIR29xQmpHZ3dJUHRFU1FKUmxwczFCbTRCQml4bnV6UFhlMmJGMGxCRWx3WGJmSVppUGlWZEFhR2poSEs1b29YY2toRFhrMEpTZ2JiYVl4eG5QT0hsbFR6enJ6bEV0RUxaSllwamRpaC9YQlJTZjhLVkpMckx2ejdQNzJTaHgzWGlLc21PVW5VdlFnU2hMZnVWaGhXYUsxaE5wOTErU2pkdkFuMy9RWkd0K0E0OW1IVDdZTENOUmtzYW5XRmdzT3hJU0hPN3lvWDN4MlhQdDYzUm5nazJwNTJxY0VHSFZxR3ZyL09ka2FNdlNlbmI1bGZubkVERkdGWXFHQUJPNi9yWnpsOHRBeXc5QTE0TjFRYkdCNW4wVDZKQXhaay9iK1MiLCJtYWMiOiJlMTYyZDcyMmNhYTFmMWRkYmFhNTNjNTkzNjZiMmE4ZmI1MmRkMmNhNjc1NDdhMmViMjgxM2M1Yjc2NWY3NmU1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:41:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319436032\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1170720955 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170720955\", {\"maxDepth\":0})</script>\n"}}