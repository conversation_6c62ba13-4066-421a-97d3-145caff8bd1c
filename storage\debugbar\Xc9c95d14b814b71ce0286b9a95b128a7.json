{"__meta": {"id": "Xc9c95d14b814b71ce0286b9a95b128a7", "datetime": "2025-07-14 22:39:00", "utime": **********.778698, "method": "GET", "uri": "/search-products?search=&cat_id=0&war_id=8&session_key=pos&type=sku", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[22:39:00] LOG.info: POS Search Request {\n    \"search\": null,\n    \"type\": \"sku\",\n    \"cat_id\": \"0\",\n    \"warehouse_id\": \"8\",\n    \"session_key\": \"pos\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.772493, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.335983, "end": **********.778712, "duration": 0.44272899627685547, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.335983, "relative_start": 0, "end": **********.703391, "relative_end": **********.703391, "duration": 0.36740803718566895, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.703401, "relative_start": 0.3674180507659912, "end": **********.778713, "relative_end": 9.5367431640625e-07, "duration": 0.07531189918518066, "duration_str": "75.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49256160, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET search-products", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@searchProducts", "namespace": null, "prefix": "", "where": [], "as": "search.products", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1224\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1224-1271</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0044, "accumulated_duration_str": "4.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.740286, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.045}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7517161, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.045, "width_percent": 15.455}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.766243, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.5, "width_percent": 15.909}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.768325, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 93.409, "width_percent": 6.591}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1043550605 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043550605\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.771917, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/search-products", "status_code": "<pre class=sf-dump id=sf-dump-156842371 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-156842371\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1346620015 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cat_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>war_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1346620015\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-682497497 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-682497497\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1435309865 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Inh0S3dNdllROXRvMDFjK2pqaUJncXc9PSIsInZhbHVlIjoiYkxNQ2JHMGUxN1RxQTBxb1htYmhibDlPQ3F6WWh1L1JXTGxSRFhwRlNIbGtmSG5JcHRKRzBPNFBld0hvNVhQMU1BMGNJNGVmcE1saVpDbmk4ZWp3czAyNTd2aEREbk9pam1nK3BsY0xvWCtjS1hOUlF6QndaWnZYYnRrQ3M2U0x0T2VMczZBY0Q1QmZpTFNnOWpXVG02K0hod2hmNEVWQUptZDNtYzBQbGlwS3Y2Um9Va3RlYUVPYWx6WEVHdlRkV0tIUUlQaHByZGdjbTVUL1FsaEIyOGNXVUhRT01YMTJGcEFKSkFyQWVmb0o3MmxLSitYRi9lb1FDZ3VYZjNTeHJMOGVCeG51ekpDUTJIUURia2hGbDlNNjVCRGFieGdML0RoSjFMYzdBdG9IWm1oLy81dm9nd3lEeUtBQjFhaGFEZ2hIcXJYN2FmbUVNd3JhZWp0YlFQcnFOVkcwK1FWRTlvTmJwTnluWjUxdEcrQzRyT0dtbzVrR2p1VmtDU3cwa1MzeDdIcUMrTjRCdG41ajR3cWtaeTJ4em9PcU1jM2QzeEQ0eXJ2Q1F4UXk3aWNHVkFwYTVWWkpQMWt2OC85Vi9Wb0lQa1Q1SXpYMXR0VU1JNndqcnlLU2F2VTRSL3dpM1h4K3Z3REJGRUcySXZ2TktwMjBFZHhrckM4MHNGUWoiLCJtYWMiOiJkNDRmNDUwMzVlNjU2OGFjOGY5NmJmYzhhYmEyOWRiMjFmZWZhYmE2NmNmZjM0MzBlNDdiMDk3MmY3N2E0MWFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikc3VS9mL092VUhRc29ZZmVyQlFzMFE9PSIsInZhbHVlIjoibDJvbHNETmZkMG5ZSHNXd3pFdnlPZ3R4Sy9RWmJJTzZIMDN1bVA4WG8wTkQ4czZSYnJudm12VlJrVWZyVWpPMmE1TkNsVGtRendHRWlWMFhaaE9aUGt3dG5lY3hSdTRNaEE4cGM1R2Q4NHc2TW1VNkNVUldWakloVzQvTC9mRjFNR3BpVHhYNjM5cmEwbzhyTU45SUtDUXJMOVhSQ1gzd05KY1FMTi9FVU4zQ2JBTWF5cXdVdnl5cGhLLzRmWGs5eG45SzcyM1dteHVKQmNnUkhyWDErN1lvWkRhMGxzSUtJdDFiRFBSUHRVckV2NG9EOS9iZWlCdzFJdUU4TGNxaGM4WkhSMUVSYVJ5bEh6OVZXK0R0S1Z6NHZIVlVRL1BZSHFuUFQ5UnQ4UFdwTUliaWZ4MTN5Q0dPb1ZKN25mSDNBWVJ3Zm9ZTGQ2NzlXS1oxT0l2UEFBYUtSM0svRkdFOWJiTVAwOVlBMVhwUU9IMC9ocTk1eXFFSjFEaUdPRUFISUkyTkdVcEFyUnNjbGFyQW4rV1ZoWlIxMWZHeG9Qa0oyUStMMytjT3dsMCt2Um1OR0NvcnhTK1A0UnJpSjk2SkJyQmJUVlNpQ2xYM1R5NGQxZGZBUGJCTFBFMEhXTDZPQlU2YkxxQkRvWkFYMytVVENsUVVXNm15WU1ib3k5QmMiLCJtYWMiOiJjMWFiMzFlMDg0ODFlODczZjVjMjlkMGEwNTU1NjdmNmE5MmY4YjkwNDM0MDkzZDIxZTA4MmU5ODg2YjU1NTNiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435309865\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-956667878 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956667878\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1053872885 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:39:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtLWVBxQzhjTjBnRnF2RXpTYWxYUkE9PSIsInZhbHVlIjoiaUs2SzUyYjIzY2FvQ1RuL2dVWkZhd2pEUTIyYWY2ektaTGNWZE9yMzZIVVo2VTJCQ2xDTFk1M01ZVmxMenFKTWRveW9JTGp2RmxORzhXM25IVEtuVUhEbEk0OTJXeU1qVWFMZHcrMTdqbzBkUDhnLzRWajN1LzZHREo2OHZrSHVEcUI2d2krQ0RyR1hhbWZPZ3FkSDBpeGZ0RTlpT3RqOEcvWVl3cTI2L0NVL3Awc2hnZzgxaGVJZUE5elZkdHFuYm1sUk84cjhRT2lHU3E1dDloZWZxdHk4dDZZcDNKTG1sK0YyMnFjOGpzZStlRHZVTWdyRy9SV01KdUVBeSswY2pYWlVBa2VBbytKcmp6d3JqUUJXZWlzMFY3WmhQbHRXZUx1VUVVaWNPNGRWN1Z2MWNIODlrR0UwYnpTTGR3ekRNSGhYY0lqWE1aTCtQLzU5YmEyY2dlZk1GSTlvaG9iWlJQTm5GazNhWUxJSWVvYUg1RVIvTDg5REthNElOSDA1YTRJQkdnRHkzdy9XWW5la2hSdllzam5ucThXOGJxRk1sODNvQVBVbFVHRGRmRGVxV0pqK0pDMjR0WXhLZEFTVEN3TXhCWEtoY2tNS1F2bEtQcW5TblJ2RllKWkpPTmNhVGZEVFNraVVBcnlSTWpoa25kK3ZHZkdDS1gwM003V1YiLCJtYWMiOiJjYWZmMjkxZDZjYjU1OTUyM2VjODhiZWMxNDVkNzc0OWRhNTIyYThlYWIxZWZjNmFiZGI4MjFjZTliNzdmOTljIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:39:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpxajliTzJKSHBKanozcUh1YTE4RlE9PSIsInZhbHVlIjoic1JhRjZNSUV1YmlwNlFoeFY0MjBmcTZxeUhSSVdoSW1Xd3Y1NGwzZks4czA1N09wZ2xvNzdJTzZRUVVhQnhEangvRkZOSU02QXVqa3UzQ0R2NHBzSzNzelpocW1pN1RJSUNYUWF4ODgvOUhUZTUrekR2SW5RL3dOZzVOUjJZUjFxY1FCYXVSUGV2Z0tLQXhGcndKcTA0Qzl0byswQVdpSDZSWHB0SXAzRC9MdnVYcHRHQTJnYkVKbjA3bTlMR0JtbXc2bzVaWXcvR3ErSW5jMnpjeFRqRlJQeVp6OEJrWW13V3RVZkU0eEF2SXBzWGUrMXl6VDdWdklnc3dNSG01bStXT1BneXkrMHlUcU5sOERhWnpmbGprT3BWUFUyYmlxWUk5c25nK1kwSW8rTU4xQmlTNjZUb0E5d3lraW0xdlhlQ0VJenZpekhnQVhPOVI1bFZWNDdmeGUrdFBjeTdTSU9YUnpMZ3FqWDV6dk5KTVRoNnlFdFF3MTV1eUlwTE1jc29EaDFLWEVuRDhXVFIzMUFOdXdWNE5NSmtyNmtpT1NIaGpCVXhQYTY1VDdLYmI2MjJmRFc1MUowa3dTYi9PUzcrTTFKQWQvRW9qMGRjbEQ3dXNlUHdhbnlOcnhMZTNmTTNSNkZrQldrKzdYc1M2SUdybDdrOGMrNWtDTDZYT0YiLCJtYWMiOiIzNTE1Yjc5NjVhMmNmNjRmODk5OGEwMDVmZDc4NWY2ZWQ4MzRlYWE3MTdlNjI5ZWJjYjY3YzYyMzBiN2U1ODQyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:39:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtLWVBxQzhjTjBnRnF2RXpTYWxYUkE9PSIsInZhbHVlIjoiaUs2SzUyYjIzY2FvQ1RuL2dVWkZhd2pEUTIyYWY2ektaTGNWZE9yMzZIVVo2VTJCQ2xDTFk1M01ZVmxMenFKTWRveW9JTGp2RmxORzhXM25IVEtuVUhEbEk0OTJXeU1qVWFMZHcrMTdqbzBkUDhnLzRWajN1LzZHREo2OHZrSHVEcUI2d2krQ0RyR1hhbWZPZ3FkSDBpeGZ0RTlpT3RqOEcvWVl3cTI2L0NVL3Awc2hnZzgxaGVJZUE5elZkdHFuYm1sUk84cjhRT2lHU3E1dDloZWZxdHk4dDZZcDNKTG1sK0YyMnFjOGpzZStlRHZVTWdyRy9SV01KdUVBeSswY2pYWlVBa2VBbytKcmp6d3JqUUJXZWlzMFY3WmhQbHRXZUx1VUVVaWNPNGRWN1Z2MWNIODlrR0UwYnpTTGR3ekRNSGhYY0lqWE1aTCtQLzU5YmEyY2dlZk1GSTlvaG9iWlJQTm5GazNhWUxJSWVvYUg1RVIvTDg5REthNElOSDA1YTRJQkdnRHkzdy9XWW5la2hSdllzam5ucThXOGJxRk1sODNvQVBVbFVHRGRmRGVxV0pqK0pDMjR0WXhLZEFTVEN3TXhCWEtoY2tNS1F2bEtQcW5TblJ2RllKWkpPTmNhVGZEVFNraVVBcnlSTWpoa25kK3ZHZkdDS1gwM003V1YiLCJtYWMiOiJjYWZmMjkxZDZjYjU1OTUyM2VjODhiZWMxNDVkNzc0OWRhNTIyYThlYWIxZWZjNmFiZGI4MjFjZTliNzdmOTljIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:39:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpxajliTzJKSHBKanozcUh1YTE4RlE9PSIsInZhbHVlIjoic1JhRjZNSUV1YmlwNlFoeFY0MjBmcTZxeUhSSVdoSW1Xd3Y1NGwzZks4czA1N09wZ2xvNzdJTzZRUVVhQnhEangvRkZOSU02QXVqa3UzQ0R2NHBzSzNzelpocW1pN1RJSUNYUWF4ODgvOUhUZTUrekR2SW5RL3dOZzVOUjJZUjFxY1FCYXVSUGV2Z0tLQXhGcndKcTA0Qzl0byswQVdpSDZSWHB0SXAzRC9MdnVYcHRHQTJnYkVKbjA3bTlMR0JtbXc2bzVaWXcvR3ErSW5jMnpjeFRqRlJQeVp6OEJrWW13V3RVZkU0eEF2SXBzWGUrMXl6VDdWdklnc3dNSG01bStXT1BneXkrMHlUcU5sOERhWnpmbGprT3BWUFUyYmlxWUk5c25nK1kwSW8rTU4xQmlTNjZUb0E5d3lraW0xdlhlQ0VJenZpekhnQVhPOVI1bFZWNDdmeGUrdFBjeTdTSU9YUnpMZ3FqWDV6dk5KTVRoNnlFdFF3MTV1eUlwTE1jc29EaDFLWEVuRDhXVFIzMUFOdXdWNE5NSmtyNmtpT1NIaGpCVXhQYTY1VDdLYmI2MjJmRFc1MUowa3dTYi9PUzcrTTFKQWQvRW9qMGRjbEQ3dXNlUHdhbnlOcnhMZTNmTTNSNkZrQldrKzdYc1M2SUdybDdrOGMrNWtDTDZYT0YiLCJtYWMiOiIzNTE1Yjc5NjVhMmNmNjRmODk5OGEwMDVmZDc4NWY2ZWQ4MzRlYWE3MTdlNjI5ZWJjYjY3YzYyMzBiN2U1ODQyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:39:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053872885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1381302561 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1381302561\", {\"maxDepth\":0})</script>\n"}}