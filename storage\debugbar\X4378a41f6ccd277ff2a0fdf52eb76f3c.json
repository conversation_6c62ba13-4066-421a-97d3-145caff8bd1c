{"__meta": {"id": "X4378a41f6ccd277ff2a0fdf52eb76f3c", "datetime": "2025-07-14 22:40:39", "utime": **********.739398, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.275145, "end": **********.739416, "duration": 0.46427083015441895, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.275145, "relative_start": 0, "end": **********.652409, "relative_end": **********.652409, "duration": 0.37726402282714844, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.652419, "relative_start": 0.3772740364074707, "end": **********.739418, "relative_end": 2.1457672119140625e-06, "duration": 0.08699893951416016, "duration_str": "87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48521352, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0043, "accumulated_duration_str": "4.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.685965, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 49.07}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.697359, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 49.07, "width_percent": 15.349}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.720614, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 64.419, "width_percent": 21.86}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.723742, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.279, "width_percent": 13.721}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-293483268 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-293483268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729356, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"18\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 288.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1589532500 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1589532500\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1003836529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1003836529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1103222578 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1103222578\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1697459915 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJuQUwwcFA2U2xKRkxIOHErMlo0MVE9PSIsInZhbHVlIjoidEovQUhDMmdJRktWeUtiZnpVa0Z3bFN2Ujk2cVJyMzExQlVUZjY4aU5VdW9Kb3BsZ2Y2eTIyM0VNazZOQnlnNWN4eXV6dXh0dWVEMm1HY2VYS3lma3BmU2g2by9IbllkTmxDOFhsTXJyV2FIRnlMSEIySUk3MTcvY2ovd2lxVU1mOVFIaGZVSzFZVk0veDk4aWxzZVlIbU13RXJGM2FIQ2pQbEtpTFc0Tk5YVUMwNGYwd1NWa1d6a1ZKekpLVll6dzI0U0ZodUY1T2JVbHlUUkUrdS9kOHFsTWM1Z3BjY2gwWHp0Z053cGtkMWxtS0RsZEs3U2w5aG81bEFDcXRwK2hMSTR5U3pyS1JlQU1qVDJUNm91SkhmSGh5MEVhenFaSURjNUVtaTljakpYekhGa3dFblRKSkhoVXVEdkFXVy96T2VBdDZoQ3ZCU2ljdjRZK1VTWVFyakpzSDFWN3hacmt1TDE5OVJkZ0pWd1hwM1NiOUc0clcwY0ZGYUx2UUNHTGcyQklaeGYrZDcrNHdEN2E1S21vY05FVm9GQ2xmR2trZlRjb1BkdmI2QXVINWEwK3RDS1pzNFcrZ1RNaHY5elVpRmI5QlNWWklUbCtzQ2ZFYXF0dmtNQzhNMG1rbTR2bE00cFdUdTBYWjRxdE1FcHN5eGUzRHVnSjh4V0VlR2kiLCJtYWMiOiI4MjZkYjU0ZmU4ODA5M2NmMjljZDQ1MzYwZDMwZGQzOTIzYWNkMGRmNWFmODQ0NTc5NTVhZWNmMmM3MzZkZDZlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Iml4L0J2TW9GTE83ejNHanNlSEZSYXc9PSIsInZhbHVlIjoidDNkM0pPTW1qb2dYVXdwMmJOS3VnWlBRdWFKN3k0cDhTS0htaFI4S3BQd0taVTZ3bWZHSkZ2czQ5VS94cm5BMlROaHJSRGNCMzJac2VaZEZ4UURaRWVnS3FMMzBPL0g0b25KRUUremtPL3B0Q2hMYnBGUldHakkzbXA1OGE2dEhqTlMyYmM1SThFWC9tc1dzeEdPTHpoL0pDUURXeHg2b2g5ekM4ek5ZTnVWc3FOSnBFc1RwVmJDc0dXSzNvUmlCUk90aHd2TlJHUnlSRW9aSWRtM2ZOeWVNcThIYm9WRXQ4V1Z0Vi8rVjFOWUNoVzlQN01DV0c0TkZJYkowVy96SzhWTmlhMWZReFhSU1BocFlZTjdIb3hFZGVYaWx6cjJSUUkvSDFZeHE3b0wxNXJ1bElwUlI0ZHZ1U2NFYlVUSmxkWmJvMHhIYlFXYk8vKzRzbTlIRDJaOS81WlprNVBGbVRZYTNiS2thcm5BOWtPalZTZW5CN0dOYzNaUUZkRUdWZWhvTTI0MnY2ZUxIZzZnUldOMThheENlcTRhc0RONmtVTUdiaHNneWk2UUhHUHdLb255YlRuRjdBMnlXY25UaFRyVTFJZWNyb0JRVTBxTGtoaE9IOUR0T1RWNmFqS3pScE45SytISlB4aDJVekx6d0lCKzRNTS9SZVNNT3NwRlMiLCJtYWMiOiI4OWY1ODQ1N2NiMDcwODJjYzcxNjYyMzA4MDA5N2U3ZGQ0YTRlZDMwNDU4OGYyNjFjZGIzMzJmZTFlNTc4ZDhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697459915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1718122257 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1718122257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-27389223 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklIT1pYVDN0Skd2QU5oM1hBd3dScHc9PSIsInZhbHVlIjoiRThTNkxJWjB0ek5MbStoT0piSjVkR25WYzJyOFJJMTFmM2h5Zi94WHc0dm80WVZGWENtZ20wVElTdkpjYjBnUUg5NGFlZG5pYzl5aHhHMFNEVWNwTXJBWjM4bW9sWXRmR0NJaWFoZ2N5YS96Um5LWnNIb05ub1ovRUltZjJvTFZUZlA2dWh0VkdxSll5V05UOFpMeDV4c05yaWNPOHYyRGRIMzQ0SDFZK2FCZTYvekF0bGsrcFJhZzJ3NXhKR1JxRjBpVGVhRkVMZllzRnI2VGE2WG9TWXVsNDV1aUE2dzJGU3FGbEpOd0t1R0MxQy9iR1NSOExnOFRJWnhPQ0FiaGVWQThpZGZkU1NJRWpIb1JhTnAyeHNEZzB3WHFsdGVITU91VWRDdWdiTXo2dUdxSk1ScGE0bitOV0k3R2w5b0xCZlhSNjlGalFaMFhVWnhKai93djczdTJHS3d2UG5GWTZKb1FYUkh6b1U5OFJMQWFLdVdDVEpPMjhXVnQyakVVZ21pOC9nRzhZUThmN0s2a0RZVThKbFFKYTZlZ2ZHVm9qUmliK3RHOVhVa2c2dGlWVlU0Rnc3K3ZweThFNHE4OEVwNmkzcnFndDcxMkpxR2ZHTXB3S0VuWENUUEVzNUx3YVNhK0JkU3JWWHJSUDJYM3ZlMEgxWHZkUExvdEZINFciLCJtYWMiOiJhZjQ1Mjg3MTcxNTY3YTlkYTFkMTFiMjAzNTc5ZDRhOTg4MjQ5NDU0Y2RjNmE1NjA2NWI5NTQxNzczOTM3MTc4IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlhU1hpZ2o1VTRkZGpRTnpGcGJnakE9PSIsInZhbHVlIjoield4Ykp0RlBQb3pUM2o3Z05JRHlnOG10QmpQWlZ3ZUxkR2Z4R2hWTEVQSVpjQnRZbTduRjg0TTJpWWxPS25vdkR5QUFuL1c5N0FNbTkvNXN0bmhLblpRR0U0bjBrSSswTmpqeGJIaW85dkNrU0Y4NUpCN0dJZE5KTjNMZXZQUEZlWjhibmpVYkovMjZFL2RuR3lxQUh5R1BNKzY0RTB2MUdSYmJWbmE3MkkwVS9qWkVnS3BxNUZLMUNoOVIrZ0R4QWRySHZCYkUwcUdDbFpYYlpJN0ZONWQ5S2R1QTRXR2MxMWkrbFo1TzBmdHZTSzl5UGRURndaS2hiZ05UdCtwTjhVY2R3cWxzdXZHSGxGdkJtck84T21BU01MWFRwaE9LbmlxM2t5WWpHU0NQNkRNYjY5a2JjNkNXUE80Q1d4WFBGcUI3VkxCZFM1NFRFZjQrU0FpV0lrbnBCNGVpQ0ViajNwUTVWNVZ4TjZFM0FlSlZlaG0wa0ltTmsvR0tRM1RGalI2VEo0OEtDVGZkTTBYZERJWGpBYWRFVzV2dDExaW5xY2pMM2xrTyt1TmVqVmdveUpDYlFBekJoVTN4YVVXd2VuVU9lR2g4aEE0UEVGRHNBdkRlWmdrdXJ1TktzaWpzRWozQURjLzFwZUFIbjJXdm9JTmNXejl1NUlheG9jL0wiLCJtYWMiOiJlNjE1YjczMDliNDVjNGQ2ZjcwODA2ODAyZjQ1MThiNWJkMGE5M2RjNjMwOWY3ZGM2ODI5OTlhNmM4OWNlOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklIT1pYVDN0Skd2QU5oM1hBd3dScHc9PSIsInZhbHVlIjoiRThTNkxJWjB0ek5MbStoT0piSjVkR25WYzJyOFJJMTFmM2h5Zi94WHc0dm80WVZGWENtZ20wVElTdkpjYjBnUUg5NGFlZG5pYzl5aHhHMFNEVWNwTXJBWjM4bW9sWXRmR0NJaWFoZ2N5YS96Um5LWnNIb05ub1ovRUltZjJvTFZUZlA2dWh0VkdxSll5V05UOFpMeDV4c05yaWNPOHYyRGRIMzQ0SDFZK2FCZTYvekF0bGsrcFJhZzJ3NXhKR1JxRjBpVGVhRkVMZllzRnI2VGE2WG9TWXVsNDV1aUE2dzJGU3FGbEpOd0t1R0MxQy9iR1NSOExnOFRJWnhPQ0FiaGVWQThpZGZkU1NJRWpIb1JhTnAyeHNEZzB3WHFsdGVITU91VWRDdWdiTXo2dUdxSk1ScGE0bitOV0k3R2w5b0xCZlhSNjlGalFaMFhVWnhKai93djczdTJHS3d2UG5GWTZKb1FYUkh6b1U5OFJMQWFLdVdDVEpPMjhXVnQyakVVZ21pOC9nRzhZUThmN0s2a0RZVThKbFFKYTZlZ2ZHVm9qUmliK3RHOVhVa2c2dGlWVlU0Rnc3K3ZweThFNHE4OEVwNmkzcnFndDcxMkpxR2ZHTXB3S0VuWENUUEVzNUx3YVNhK0JkU3JWWHJSUDJYM3ZlMEgxWHZkUExvdEZINFciLCJtYWMiOiJhZjQ1Mjg3MTcxNTY3YTlkYTFkMTFiMjAzNTc5ZDRhOTg4MjQ5NDU0Y2RjNmE1NjA2NWI5NTQxNzczOTM3MTc4IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlhU1hpZ2o1VTRkZGpRTnpGcGJnakE9PSIsInZhbHVlIjoield4Ykp0RlBQb3pUM2o3Z05JRHlnOG10QmpQWlZ3ZUxkR2Z4R2hWTEVQSVpjQnRZbTduRjg0TTJpWWxPS25vdkR5QUFuL1c5N0FNbTkvNXN0bmhLblpRR0U0bjBrSSswTmpqeGJIaW85dkNrU0Y4NUpCN0dJZE5KTjNMZXZQUEZlWjhibmpVYkovMjZFL2RuR3lxQUh5R1BNKzY0RTB2MUdSYmJWbmE3MkkwVS9qWkVnS3BxNUZLMUNoOVIrZ0R4QWRySHZCYkUwcUdDbFpYYlpJN0ZONWQ5S2R1QTRXR2MxMWkrbFo1TzBmdHZTSzl5UGRURndaS2hiZ05UdCtwTjhVY2R3cWxzdXZHSGxGdkJtck84T21BU01MWFRwaE9LbmlxM2t5WWpHU0NQNkRNYjY5a2JjNkNXUE80Q1d4WFBGcUI3VkxCZFM1NFRFZjQrU0FpV0lrbnBCNGVpQ0ViajNwUTVWNVZ4TjZFM0FlSlZlaG0wa0ltTmsvR0tRM1RGalI2VEo0OEtDVGZkTTBYZERJWGpBYWRFVzV2dDExaW5xY2pMM2xrTyt1TmVqVmdveUpDYlFBekJoVTN4YVVXd2VuVU9lR2g4aEE0UEVGRHNBdkRlWmdrdXJ1TktzaWpzRWozQURjLzFwZUFIbjJXdm9JTmNXejl1NUlheG9jL0wiLCJtYWMiOiJlNjE1YjczMDliNDVjNGQ2ZjcwODA2ODAyZjQ1MThiNWJkMGE5M2RjNjMwOWY3ZGM2ODI5OTlhNmM4OWNlOGE0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27389223\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1831363568 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">18</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>288.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1831363568\", {\"maxDepth\":0})</script>\n"}}