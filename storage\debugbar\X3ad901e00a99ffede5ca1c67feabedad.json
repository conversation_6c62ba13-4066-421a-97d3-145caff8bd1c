{"__meta": {"id": "X3ad901e00a99ffede5ca1c67feabedad", "datetime": "2025-07-14 22:40:35", "utime": **********.521298, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.002561, "end": **********.521315, "duration": 0.5187540054321289, "duration_str": "519ms", "measures": [{"label": "Booting", "start": **********.002561, "relative_start": 0, "end": **********.418928, "relative_end": **********.418928, "duration": 0.4163668155670166, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.418938, "relative_start": 0.41637682914733887, "end": **********.521318, "relative_end": 2.86102294921875e-06, "duration": 0.10238003730773926, "duration_str": "102ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48520616, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01742, "accumulated_duration_str": "17.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4605951, "duration": 0.015220000000000001, "duration_str": "15.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.371}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4859471, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.371, "width_percent": 3.617}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.503508, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 90.987, "width_percent": 5.683}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.507407, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.67, "width_percent": 3.33}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1631411852 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631411852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.512701, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"11\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 176.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1728293891 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1728293891\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1706218573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1706218573\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2014349533 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014349533\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1683492684 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjhwOFVLTzN2NVlRZTZSZ2cvbzZadGc9PSIsInZhbHVlIjoibytjN04zanZGTkFoSHhZc3g3TG4xNmdtUkFBVjU3Y1hvSWJOd3Fya0gwckdaMFc3cnJncWVRNEtkMS84L0F1bFVkTzFjdThwbkJKanZWT1JrMlkxWmlxR0ZLOTNocWJWRmswN3NzNXd0ZS9ZVjN0SG53NVE3UFQ4VlE4SklwRTh4dVRhVmFoKzRLM3EybFhXeDNzRGpEUWFOalB0N3AwdTZITzU0ZWZ2L0h1YmlEOVRncnBQOVh5STZNUEtrWVA4cE11c0VQNy9FUTFrT3B6RjRkd3gxeGdKY1VSd21Ld3BTU3pqTUJxZWc3OFlxSHVkVkRIc0dPZFcrL3czOXNDUWIzUVVIV2JwbzFKSGZNT0JxYVBQMlMyb0ltMVFJUHpjM3hYRXV1ZVd5MC9VdWJ6a0pKdWpYUy9YWWRwY21QZGlTY2xUamZQVmVBeVpYNkd2VndpaVR5ZmVoeXlUSEt1cUdtdEttU00vOFFGUmNZaFVSZHdBajkycU9TZE9BNjBwK3U5QkpMWjFyMTlPaCt2ck9NVkRFdGgwUjE0dElWanFaSU5haHV5elBibi8vYWJxNU9iaHk1dXc3Um9sT2J1bHFoZ1kyaFdxNVpYQXlFWTVhRUxqMXdJd3FTNnFZQnJFbkNSWUdFL0xQbGRCeUx2T2RFb21jMlJta0xsREg5MkUiLCJtYWMiOiIzY2NkZGRjZGE3NjY4OTExMmZhYzk4MjVhZmQ2N2IyN2ZhNjUzNmZjMThkNzQwNWUwNTM1YjM3ZmNmZjIyNzRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikdzam0zOGs3amxzK1R3SkZFOHV4N1E9PSIsInZhbHVlIjoibnJ3OVVDOFJhVlgxU2tkN05JTFcwTXZxejdZYXBoMk00RzVlbnNTKzR6akpFeDNxcTYydzB2aFhvWldRM3ptclpjdW1Vbk5zVVVFakZhcEFiQWdieCtJMUZ6aFFZdUNxN3NjUGJsMHlEQjUzZGUva0owVWJoL2xBdGtjcFJ2eGRkMTBGQ3luTmFVVDBaVzBteDdyL3hWeWlzcVNzTkxUYnI1TkJWREpxRlo2MTF1Y1BsQ08wT0JGc05KK1Avdm1mUjFQU3FJSUp3cWJCaXJGWU93VUJZSVppelAxc2haZk9ZZDdxSXp3aUlwaWcvRnQ1WXdabzhsM08rbHgzaXFJd2h5T3BXVXlQU3dLekVBaVBPbjFPQUR1cmFkRWx2WncwNTh4cklkWGVibmFlTTBva0JUWTBBS0xIN1BaUnJRYkZRbUV1SWp3d2s0TU93TG5zc0huNFY4eVJZUjVkTGhWOXl0d0xhVGovZTRJcUIyWDQ3Q1I3WE8wSytaeUd3eFprTTF2UU8vem5KVklUa2dXZWlqeVZEUFEyVzBibFpRK1l1UElRdVU3QVloOUtWWk1PM0htMjJGSW90eENtSU9BQW5tcjI2OHFYVG1qMzlnYU53R2JBUVZvbDVTcjcyekI2OGFuZ3FtWElSZkQ3Q3gxSm1oMnA2RnZyM3hqTk04Z1kiLCJtYWMiOiI5ZDViNGFkNTUyNGRhMTY1NjhiNzhkYWVmMjhkYzlkZTg5MjhjMGQzNjE5Y2U5MDMwNjEzZTAzMTRiYzRlNmE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683492684\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1307190347 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1307190347\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1097489946 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InREeTc2blBNcXB6eTF0VmF6VVc2Unc9PSIsInZhbHVlIjoicngzNUxTQXFhcTBNZzRBWVlUa05sdS9yTENnN3d1NjN3RnZ5T3NTRzJaMG9HTEtRRjJVWFR2QWh5SnllUjRmdTUxUEJvOTlyTkIyaXk2MXZHQk55Qy9PdzZYRk1hekJob2hSa3BnblgwTDVlT094LzdYM2dNT0pXNHBYNmxXQVZpcE5DelFaZVdkUU52N2ZOSWg2ZU8vUWxjYkNXL1BXTWFzNU5VVWZXaW1hN1h1SGdWZkFwaEZiM2FmQllyYlJ0TVRnc2lucVRhZ0ZkRWNjL2Z6VjI2THczMjJWOXBHS09rWE9pZGZnS1hOYVVvRGlaQWlFR1N2bGpXcWFZTmFxVjRyT3hoUDhFUkVrNHBaeEVScVY2bG1uWGpSUGlCVjNvSnRLa2RXY2tTa3dnak1XL3ZObS83bmsxY1I4N3dOUnNyb085Rk94R3QzMWVSMWZHZGlQb1RQYmtDdEhvRWNYcUJOaEZ6RWdqYUxYRE5oYU04Y1RIa2QycEZ5NU1zbW1Hemp5dnFFWENsMGZ4LzhjZUZ0enB2RWVqSzRqMWJjc2lDV2FYQlZwVUVPRFlqVmpna3BaUUQyL0NhcVpLYlBGSmdXYzJKVXJpQ09WamtuZFkxTEducWJCNkZpakNrOXVOSkdBaEhiWWU1em1PbzZnMTBhV3FPa0V3b2NhbTdweEIiLCJtYWMiOiI1ZDYzMWQ3MjNiOTJiODIxYTRmOWJmYjJjNzAzMzMyZDY3YmFlYzUzYzZlYWUxMjE2ODA2Mjc5NTNiN2Y1ZmM0IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imx2RDd0RE02am1rZ1dMMjBldmpmOFE9PSIsInZhbHVlIjoicWcrckRWRkZoTVQ2S2VjdFdFZWNkdTBzenhJWWt4dVJ6Vk1OU0E5UC93R2dzdTBrR3l0dWY1cUFvRmVlUHB3N0JRdzBDd29BWjg1RGxVbGVna3VBNjlOQ1dJeFRUN25KL3JSZUNNMjcwMXhHVWZBVHdZazlRcGJVNGlrWVROK2ZhTGszTklna2xzdnN5RzZMSVRVN2hEK0pBelp0RzB0QTd0OXNEdGk2YUtCSzlxSncvbTRpbTVXcXNDdGNBS1QzM0hiTHg1VTNWbUxZaGtkM1FRTFBBMVN5MDBYVVlVQ3dwWDc1SzN5N2Q3RWpaVlc5aEhKdHYyT3pORkdXZ0RIZHB2aXlNTlA2bGZBK1RvTEZab05ZejgrZ1hRQ3FNeHUyVmUxUmFHOGN0a0toSCtmRmdvUmZyKzlPMWRoUHBDdVAydFB1SURjNWUzYTNUV0xWWTV0bng0UGthRC9YVkZteDlUN254TzRzVCtqR0RDQ0VDcHN1d2VMWGdyb3h2U3R6dlhCMHduaEpLRmdzenhqRUhmQkZvTHdYRWVxWitoWUtqNklSNmYzUWxPTkwyYkNzUTcyZHNTNXM1emx1ZVBEcHo0aFcweVRzRnZvS1VsMlZhdmE1TER3TWpiM2Mzd3VMekNpVEh0YWFVQmNuUCtXYTFiaXdRdDlGbC9jczRxVzUiLCJtYWMiOiI4NjI4OGZlZDNlNWM3OWViZGMwMDc1MjQzNDgwODQwZTY1OGJlOTZiNWEyNTdlYWNhNzk3NDliZmM1Yjg2ZjVkIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InREeTc2blBNcXB6eTF0VmF6VVc2Unc9PSIsInZhbHVlIjoicngzNUxTQXFhcTBNZzRBWVlUa05sdS9yTENnN3d1NjN3RnZ5T3NTRzJaMG9HTEtRRjJVWFR2QWh5SnllUjRmdTUxUEJvOTlyTkIyaXk2MXZHQk55Qy9PdzZYRk1hekJob2hSa3BnblgwTDVlT094LzdYM2dNT0pXNHBYNmxXQVZpcE5DelFaZVdkUU52N2ZOSWg2ZU8vUWxjYkNXL1BXTWFzNU5VVWZXaW1hN1h1SGdWZkFwaEZiM2FmQllyYlJ0TVRnc2lucVRhZ0ZkRWNjL2Z6VjI2THczMjJWOXBHS09rWE9pZGZnS1hOYVVvRGlaQWlFR1N2bGpXcWFZTmFxVjRyT3hoUDhFUkVrNHBaeEVScVY2bG1uWGpSUGlCVjNvSnRLa2RXY2tTa3dnak1XL3ZObS83bmsxY1I4N3dOUnNyb085Rk94R3QzMWVSMWZHZGlQb1RQYmtDdEhvRWNYcUJOaEZ6RWdqYUxYRE5oYU04Y1RIa2QycEZ5NU1zbW1Hemp5dnFFWENsMGZ4LzhjZUZ0enB2RWVqSzRqMWJjc2lDV2FYQlZwVUVPRFlqVmpna3BaUUQyL0NhcVpLYlBGSmdXYzJKVXJpQ09WamtuZFkxTEducWJCNkZpakNrOXVOSkdBaEhiWWU1em1PbzZnMTBhV3FPa0V3b2NhbTdweEIiLCJtYWMiOiI1ZDYzMWQ3MjNiOTJiODIxYTRmOWJmYjJjNzAzMzMyZDY3YmFlYzUzYzZlYWUxMjE2ODA2Mjc5NTNiN2Y1ZmM0IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imx2RDd0RE02am1rZ1dMMjBldmpmOFE9PSIsInZhbHVlIjoicWcrckRWRkZoTVQ2S2VjdFdFZWNkdTBzenhJWWt4dVJ6Vk1OU0E5UC93R2dzdTBrR3l0dWY1cUFvRmVlUHB3N0JRdzBDd29BWjg1RGxVbGVna3VBNjlOQ1dJeFRUN25KL3JSZUNNMjcwMXhHVWZBVHdZazlRcGJVNGlrWVROK2ZhTGszTklna2xzdnN5RzZMSVRVN2hEK0pBelp0RzB0QTd0OXNEdGk2YUtCSzlxSncvbTRpbTVXcXNDdGNBS1QzM0hiTHg1VTNWbUxZaGtkM1FRTFBBMVN5MDBYVVlVQ3dwWDc1SzN5N2Q3RWpaVlc5aEhKdHYyT3pORkdXZ0RIZHB2aXlNTlA2bGZBK1RvTEZab05ZejgrZ1hRQ3FNeHUyVmUxUmFHOGN0a0toSCtmRmdvUmZyKzlPMWRoUHBDdVAydFB1SURjNWUzYTNUV0xWWTV0bng0UGthRC9YVkZteDlUN254TzRzVCtqR0RDQ0VDcHN1d2VMWGdyb3h2U3R6dlhCMHduaEpLRmdzenhqRUhmQkZvTHdYRWVxWitoWUtqNklSNmYzUWxPTkwyYkNzUTcyZHNTNXM1emx1ZVBEcHo0aFcweVRzRnZvS1VsMlZhdmE1TER3TWpiM2Mzd3VMekNpVEh0YWFVQmNuUCtXYTFiaXdRdDlGbC9jczRxVzUiLCJtYWMiOiI4NjI4OGZlZDNlNWM3OWViZGMwMDc1MjQzNDgwODQwZTY1OGJlOTZiNWEyNTdlYWNhNzk3NDliZmM1Yjg2ZjVkIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097489946\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">11</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>176.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}