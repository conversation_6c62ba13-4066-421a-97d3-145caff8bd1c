{"__meta": {"id": "Xb04734c3761ec1a3b9a09c5fa6e95ec0", "datetime": "2025-07-14 22:30:57", "utime": **********.054129, "method": "GET", "uri": "/inventory-management", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.533142, "end": **********.054145, "duration": 0.521003007888794, "duration_str": "521ms", "measures": [{"label": "Booting", "start": **********.533142, "relative_start": 0, "end": **********.934042, "relative_end": **********.934042, "duration": 0.40089988708496094, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.93405, "relative_start": 0.4009079933166504, "end": **********.054146, "relative_end": 9.5367431640625e-07, "duration": 0.12009596824645996, "duration_str": "120ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 49166280, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET inventory-management", "middleware": "web, auth, XSS", "controller": "App\\Http\\Controllers\\InventoryManagementController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "inventory.management", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FInventoryManagementController.php&line=20\" onclick=\"\">app/Http/Controllers/InventoryManagementController.php:20-34</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02951, "accumulated_duration_str": "29.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.974094, "duration": 0.02607, "duration_str": "26.07ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.343}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.010288, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.343, "width_percent": 2.338}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.02019, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 90.681, "width_percent": 4.27}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/InventoryManagementController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\InventoryManagementController.php", "line": 23}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.027173, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.951, "width_percent": 2.474}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.038167, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 97.425, "width_percent": 2.575}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage inventory, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-319376302 data-indent-pad=\"  \"><span class=sf-dump-note>manage inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319376302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.041765, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => null,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1510993901 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510993901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.042646, "xdebug_link": null}]}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/inventory-management\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/inventory-management", "status_code": "<pre class=sf-dump id=sf-dump-577730105 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-577730105\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-286563635 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-286563635\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-701685857 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-701685857\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2059375864 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532246434%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlA3bXEwYnVvTEQxbHd1cHdXRm5QSmc9PSIsInZhbHVlIjoiVDdLeXdPY2VTNDdNMFFicXJxcTV4MkUvbzZ2dm1SUXdWWEpTdURIUUVHNHFzbkkveWhLRjE3NHNsVHR4K3JEVndkems4SGgzblVPVmxEdGk5NjErK2lqUE5zRmhiVW9IU3QxRCtBT3l6eGtnMXMxeHBTeGVHN3lZd0g3dGxMNGVjbm0xdFdOL2Y2UERuWG00aHp3dk9Ob3MrZXJRbGhDUm5uN0pDY2pYSytVSlY2QWxEb2VoL0NRM2xuUXVTclcvSWpyMzNUdFlVMlYxREVIeWpMYkI0a2lYVE03alhIRE9vNVhaOW00YzgzcUJWdFJ1cWNQZTRHZlBiN2lCWnJadGE0c1g0dnVmR0U5VWc1VnUrZ3NwdGxxQmFpdURnNDhraFdVRHRZWmJVd1lBY1lXbFhWZHVXRDE2OWl5WnJHK3dlcmZVN0h6VGxDK2M2eHJUdVhYdGVJQnIwM1ZTbXl2OHJpV2UyaW5LdGFjVUhhakxSbi9DM1FUSE12U25walFrNDYzYVZpTWZmViswOUxEU0RnRjljUFJnbit4VlV3eXlWdTVvLytBcXdNQ3JIY2p4Vm9YV3hwZ3dibzFVeUp3bzh3b0Z5UkVyQ0NkK1VTVmVkTE5MYzFQVmxMZFJSL2srRTVIbkNjUEpMUFJTQThUTW40dkp5eDU5TlJYU1FFbWMiLCJtYWMiOiJjZTFjYjU5YzEyMjdhZWZkYjQ4ZmY4NDZhODk0YjNiNGU5NDZhNzQzZDQ0MDJjNGFiY2FlMzIwZDI3ODFkM2ZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImYyd3djOVlWQzlrK3dtakVYTXB0OUE9PSIsInZhbHVlIjoibnBvNmI4SlhWNWxucGJWdGtzcVVUb0FpTVdVTXZCUnNaL1Y2SlRLT0piUWxaWmdwUFZDTkQvTW14UG1EeWRmQXNpYWt3OE9vcCt6VHo3bWZGUkxCTGl1TjdQbFhWWEtsWEJwbGJJTHUvY2k4cmQ0ZTFTMzFlZFF1d2cwL213L0U0bFRkeSsyUlQrQ3JwYzlUWDAxNFJpRXZ6S21ITjhSdU92S0c4eXlnUDZ6K3dQMStCN3dLLzhFYllybWdZVkdxaHBkVjNUbWs5cUxnVE1UNzVRODNDQWlaaWcybUhOOHRMYzA1R3BSWXZSWHY4eVlPaExMcnREczc0Nzd2aXJSRmNWdzZTdk5lOXRxT1lqOHorYjhneW5kSUNNMm1wWE1ldVI0QUt4K1pNMElLZWE5cHpoUkdXTnZTYUM2eHhqUkg0QlZ0Nm5LdzFCS1BVdmJSMTlUeU40dzBOVUYrUWt0UkVZamxoZ1JEbzRyS2hQZU1DdExtMWNSN0tjOExwRUg4OUhpQzl4cEs0WFoya3IveWVZTXk3cFNISUlDQnBUMlJWMngycWJOTHJDbDlpQ1QvN0pTNSt0bGRlNnk4VWx4K3ZXSk9qSk9UQjNVSXRDOFBOZ1N1bFhsQXZJaC95UnkzR09LRDlGTlJYaXZhQWlSRGFQY1h2UTZDRUZVWDR0ZDQiLCJtYWMiOiI4NmM2NDViZjhkNDcxNTA1OTA5NjAxZDEyZGYxNDQ2MTMwZGEyNjEyMmJkNzEwMTllNWIyY2NiNjNkOWZkY2YxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059375864\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1332751620 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1332751620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1990109021 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:30:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImN6eGdVZ2dRejhVeGFjZFVsUGJsc2c9PSIsInZhbHVlIjoia0xGS1JwUjkyYlZodWtTSmcvU1pZMW00bnRrZ0RwS1hPTEd0N3BCclg4MGtwWEZBdDlZWnNBSSs2bjlqU2tvQTJQUGNMNWFQUFFTYmVvQ1FEbDR4bHNZbXdrWUR3U3Q1SUNkaTZvWUhUM3BaejZkQnA2bEg0ZElINjdqU0NUUkc3N1Y3cWpPTzJVUTE2Rzc1ZW04UzNtbWszN05lMU00MGN6TlZDbzllM2ZSVjByVk44SkR2RlpiOXFNMllWai9vdXdtejJrbkkxVG14dmxPL01pVnIzczRmazFFOFQyYS9jVUlqTzBteWxGY2NFZTZrWDNwTGJleGlCc1pxMC80elp0WnVKSDNMMi96MlY4TGJSYVlydU1GUGJFajNSYytCK0YwRGgvTFczYzhILzExMTVRdnBPQldhZnY3YTJRSk9nbXpycCs3ZXpJSXdyamNhTTdTbTNwWTE1VGpjaURXNWxab0hURi9KY1VxejVHSUZRdTl2akJ2SlRiWXhhc1liRTlyb3M1Mi93WTVxNzAraHhWMFZyTTVSRXAxSkxIV1lSM1o0aThzMFhxNFNUZVBQYmJieG9sa05SdWdBTEFlb0xPTXVkNkNmWnM5S1E1WTllNkpwbVRNL2tpV2tPNEVFT3JsdHZ3MjYrWGsyeWdENzJJOXd4ZTdnVTJqeCsyR2siLCJtYWMiOiJmYWRmZDgyOWNhM2VkOGZlOTEzOGQyMDhkNzNiMGJiODYxZjZhYmIwMWQ4MWIwNzFmNDY2YzhiMTY5MmViZGIzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhBNHZSVXcvSHRKeVZMTGN2VURBclE9PSIsInZhbHVlIjoiMU84UjdjeFI3enpqVk5BenJvUUEzaTlQVmw0NGc3a3JFdGpzOCs2b3hjUjYzLytpamJsS0YzcTNNS0c0Q2c4cE5HUldyUnRvQ2ZmWmo2SExyeTMyYVMwcEExWVdlK0JGUXd2WU1aWkZIcnVUdkZIMU1laldQQUl1UytKM1VqZWFEMlMxemJKUUVUME9ESjVzY0hIQ01sRG83WXBsdWlZYzBIaWpTeS92TmtidTcxRDlFcjJ1ZnAwYm5QdVJkREJWbXNLSjB3RWp6MVNaMEk5UGJ1WC8rQVZIeDdvOXphSVU2U1hENE5PZ1hMNm1rQWZsNnN4ZjdObEk1d0ZiTC8wRllrbjBvTDllN2JZRWFBbmp2WStFeXA3VVYrNTdWRVM3UlluTmlqQitheFp6YVY5d1pKa1dzSk9TOWQ4R00zUno4d29YWHg0b0RLOWtRLzV2aXA2eXNlQy9ad09EdTFFRjZUM3M2M1VPTWRmMERDKzBLUWlqNUgzWlBQZXVkN0VicWMvSHJTUVdVRlIzVkF2ODFPOUVlN0FYeEs0bHFNMGNIcEJpSzRsK2VKWmlxbGVsL2FCQ2R3MGt5dGw3dlBtK3FkSEJtQ1hDTktMQUladit2M2tpaDdPZ0xWVzNBVHVJRlFNT2VTNkJxMnN3YWtDYU9sSU5GUTZpc2xXVSt3NDQiLCJtYWMiOiJhY2IzYjM4YWY0MGE3NjU0OGJiY2NhMjZlZDhmOGZkZTEyY2UyYjI1NDg0NTkxNmJiYTNjNWI0NGIzMTczYzg3IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImN6eGdVZ2dRejhVeGFjZFVsUGJsc2c9PSIsInZhbHVlIjoia0xGS1JwUjkyYlZodWtTSmcvU1pZMW00bnRrZ0RwS1hPTEd0N3BCclg4MGtwWEZBdDlZWnNBSSs2bjlqU2tvQTJQUGNMNWFQUFFTYmVvQ1FEbDR4bHNZbXdrWUR3U3Q1SUNkaTZvWUhUM3BaejZkQnA2bEg0ZElINjdqU0NUUkc3N1Y3cWpPTzJVUTE2Rzc1ZW04UzNtbWszN05lMU00MGN6TlZDbzllM2ZSVjByVk44SkR2RlpiOXFNMllWai9vdXdtejJrbkkxVG14dmxPL01pVnIzczRmazFFOFQyYS9jVUlqTzBteWxGY2NFZTZrWDNwTGJleGlCc1pxMC80elp0WnVKSDNMMi96MlY4TGJSYVlydU1GUGJFajNSYytCK0YwRGgvTFczYzhILzExMTVRdnBPQldhZnY3YTJRSk9nbXpycCs3ZXpJSXdyamNhTTdTbTNwWTE1VGpjaURXNWxab0hURi9KY1VxejVHSUZRdTl2akJ2SlRiWXhhc1liRTlyb3M1Mi93WTVxNzAraHhWMFZyTTVSRXAxSkxIV1lSM1o0aThzMFhxNFNUZVBQYmJieG9sa05SdWdBTEFlb0xPTXVkNkNmWnM5S1E1WTllNkpwbVRNL2tpV2tPNEVFT3JsdHZ3MjYrWGsyeWdENzJJOXd4ZTdnVTJqeCsyR2siLCJtYWMiOiJmYWRmZDgyOWNhM2VkOGZlOTEzOGQyMDhkNzNiMGJiODYxZjZhYmIwMWQ4MWIwNzFmNDY2YzhiMTY5MmViZGIzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhBNHZSVXcvSHRKeVZMTGN2VURBclE9PSIsInZhbHVlIjoiMU84UjdjeFI3enpqVk5BenJvUUEzaTlQVmw0NGc3a3JFdGpzOCs2b3hjUjYzLytpamJsS0YzcTNNS0c0Q2c4cE5HUldyUnRvQ2ZmWmo2SExyeTMyYVMwcEExWVdlK0JGUXd2WU1aWkZIcnVUdkZIMU1laldQQUl1UytKM1VqZWFEMlMxemJKUUVUME9ESjVzY0hIQ01sRG83WXBsdWlZYzBIaWpTeS92TmtidTcxRDlFcjJ1ZnAwYm5QdVJkREJWbXNLSjB3RWp6MVNaMEk5UGJ1WC8rQVZIeDdvOXphSVU2U1hENE5PZ1hMNm1rQWZsNnN4ZjdObEk1d0ZiTC8wRllrbjBvTDllN2JZRWFBbmp2WStFeXA3VVYrNTdWRVM3UlluTmlqQitheFp6YVY5d1pKa1dzSk9TOWQ4R00zUno4d29YWHg0b0RLOWtRLzV2aXA2eXNlQy9ad09EdTFFRjZUM3M2M1VPTWRmMERDKzBLUWlqNUgzWlBQZXVkN0VicWMvSHJTUVdVRlIzVkF2ODFPOUVlN0FYeEs0bHFNMGNIcEJpSzRsK2VKWmlxbGVsL2FCQ2R3MGt5dGw3dlBtK3FkSEJtQ1hDTktMQUladit2M2tpaDdPZ0xWVzNBVHVJRlFNT2VTNkJxMnN3YWtDYU9sSU5GUTZpc2xXVSt3NDQiLCJtYWMiOiJhY2IzYjM4YWY0MGE3NjU0OGJiY2NhMjZlZDhmOGZkZTEyY2UyYjI1NDg0NTkxNmJiYTNjNWI0NGIzMTczYzg3IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1990109021\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1642576956 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/inventory-management</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642576956\", {\"maxDepth\":0})</script>\n"}}