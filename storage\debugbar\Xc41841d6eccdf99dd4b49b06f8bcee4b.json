{"__meta": {"id": "Xc41841d6eccdf99dd4b49b06f8bcee4b", "datetime": "2025-07-14 22:39:00", "utime": **********.325787, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532739.823631, "end": **********.3258, "duration": 0.5021688938140869, "duration_str": "502ms", "measures": [{"label": "Booting", "start": 1752532739.823631, "relative_start": 0, "end": **********.199882, "relative_end": **********.199882, "duration": 0.3762509822845459, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.199891, "relative_start": 0.37626004219055176, "end": **********.325802, "relative_end": 2.1457672119140625e-06, "duration": 0.12591099739074707, "duration_str": "126ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48491968, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-223</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.030920000000000003, "accumulated_duration_str": "30.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.232377, "duration": 0.02615, "duration_str": "26.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.573}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2718139, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.573, "width_percent": 2.458}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.299988, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 87.031, "width_percent": 1.52}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.302238, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.551, "width_percent": 2.62}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.307089, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 91.171, "width_percent": 8.829}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-250744836 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250744836\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.306167, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-920193530 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-920193530\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2086409374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2086409374\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1034536618 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1034536618\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-756976966 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFYWllKVnhsWG5IK05qYVYvaVordHc9PSIsInZhbHVlIjoiREhpSU9qdnJ2NHNPUGpsMHhJTkIyeFRwclNiV1h1ZWhxT24vMGx1SEptaWxLS29QY3gvMEx6dmFsc0VRVC9GYU80WjhxWmF4Tk82eVZxZXpJcVpXbTFnVDhJSU5CZUxrOGtJU2dWMHlxM2RVRFoyTTZXbk1UVUxSSGozcjAvNHcxYzFJeENycDNPcm0rc2VpdUJsdWdKZkhKd0RBNXdsUURvNGVDdjRYOFh3dEErbURwT0RrNzJ1alhqMU4rMkdsbEdXbFdXS3dEMHhIY2dkQ2ljZVkrU3ZOd1NDZVZWTkRHYlBYQnhQcUNwb2VVSFcyZjhkeWNJNm9rSmJYTlBlT245MWloQmcvZlVraTJSMERFT3ZLVEJrYmdmQ2owcFl5UEJVMjlSMXlsVHRyK09ZWXR6WjhGN081aVdCWmFWOUU4KzRIMDkvZjAwV0g3Mnc0QjljN3d1VkdDVm0rcGo2OWdlT1FVc01FRng0T0tEMjByNDVoOGp4UkJ6ODhXWmNLVHpQUndNcG1DbXdoSmVPRk9Rb0JjS2c2SkJxSGxqMVd6U21FZGo4Z3dQUHYxQkFnZHEvOWl1SWVkVDFOcVkyakZIYnU4Vk5rVlJlcThYN2xOM3MvVmVObHRZS1lWSGEvZkZrS09vL3pESkNqenVIV3FNK2p2OHh0SnJKcTB2L2YiLCJtYWMiOiIxOWUyNTk5OWQwMzE3OTViMjdlZGVkOGU3YzJiODQ3Y2JjZjdjYWJhMTY0MWM3MDVlMTM5NDI1NjI3Mzk2OTU0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InlQaXg2WThzZ1JaRlFUTlNmQS94R1E9PSIsInZhbHVlIjoiVllZaE9hTUY3RmR0ZS9qb1h2ZnlsakdsUVBnaU1OdkEwN2lURVNnSmRiMENDbUtQODZoNVZZdExMUkpjR3BPa0QrUjJ4Mnh4V1JtQ3c0Z25XYnhLcXpIRklNOFY1b29Qc3Vobk9LamV2dlg1amRwSUV6MjZXbVlnbHUwZnFlUDdRSzIyWTM0cUhsdmJPS25BeHZWN2VSZHNySXlETW5USUh2dmpyTEc0NDJCNDdGeEsvTVczUUpuOXNYZ0dWNzllMEUvMkt6TGp1NlZDU2JXaVkrN0NEN0p6RktWZG1Ja1pheEp1eTd2dnEzWS9CRXhSekVwR2hyTmQ3Mys4Ty9aNVJPT2E4Mm1EVVhGUjVaeC83bTZmc2lqSEJEcWllNUo5di8yR1lkNHNrNWZsekNyRURkaEVoR1dhMTIzVFF0K1pvTGw2c2c5VlNWS3JTUFBLb3NLcUFEOVIzV0I3Mk41SExOVFJWczNGd0hES3VFeW05VDQ1bHZZVjFadWI4dTZRZlF1enoyc3lWQTFrb0cxZkpUUGYwRjF4dm02RmUzaStwNVFkbVRVTTdOd25tMDVPekU3cnA1ZlNxMUZLN1VUNEkxVEpJcXk3cDBzVFdKdnQveGl6a2NlR1pxTVBPTkJpekRnejJ0c3ZzT1hXNXhJNzNEd3BGMDEzb0cxSzJMQ3ciLCJtYWMiOiI4NjU1NzZlZDk1NWZmODdkY2I3ZDVmODA5NGE2ZmQwZjhkZjkxOWQyNWUyNzYzNDg0OWMzM2E5YTc4NmFkNzYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-756976966\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-327984840 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-327984840\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2138181000 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:39:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inh0S3dNdllROXRvMDFjK2pqaUJncXc9PSIsInZhbHVlIjoiYkxNQ2JHMGUxN1RxQTBxb1htYmhibDlPQ3F6WWh1L1JXTGxSRFhwRlNIbGtmSG5JcHRKRzBPNFBld0hvNVhQMU1BMGNJNGVmcE1saVpDbmk4ZWp3czAyNTd2aEREbk9pam1nK3BsY0xvWCtjS1hOUlF6QndaWnZYYnRrQ3M2U0x0T2VMczZBY0Q1QmZpTFNnOWpXVG02K0hod2hmNEVWQUptZDNtYzBQbGlwS3Y2Um9Va3RlYUVPYWx6WEVHdlRkV0tIUUlQaHByZGdjbTVUL1FsaEIyOGNXVUhRT01YMTJGcEFKSkFyQWVmb0o3MmxLSitYRi9lb1FDZ3VYZjNTeHJMOGVCeG51ekpDUTJIUURia2hGbDlNNjVCRGFieGdML0RoSjFMYzdBdG9IWm1oLy81dm9nd3lEeUtBQjFhaGFEZ2hIcXJYN2FmbUVNd3JhZWp0YlFQcnFOVkcwK1FWRTlvTmJwTnluWjUxdEcrQzRyT0dtbzVrR2p1VmtDU3cwa1MzeDdIcUMrTjRCdG41ajR3cWtaeTJ4em9PcU1jM2QzeEQ0eXJ2Q1F4UXk3aWNHVkFwYTVWWkpQMWt2OC85Vi9Wb0lQa1Q1SXpYMXR0VU1JNndqcnlLU2F2VTRSL3dpM1h4K3Z3REJGRUcySXZ2TktwMjBFZHhrckM4MHNGUWoiLCJtYWMiOiJkNDRmNDUwMzVlNjU2OGFjOGY5NmJmYzhhYmEyOWRiMjFmZWZhYmE2NmNmZjM0MzBlNDdiMDk3MmY3N2E0MWFhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:39:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikc3VS9mL092VUhRc29ZZmVyQlFzMFE9PSIsInZhbHVlIjoibDJvbHNETmZkMG5ZSHNXd3pFdnlPZ3R4Sy9RWmJJTzZIMDN1bVA4WG8wTkQ4czZSYnJudm12VlJrVWZyVWpPMmE1TkNsVGtRendHRWlWMFhaaE9aUGt3dG5lY3hSdTRNaEE4cGM1R2Q4NHc2TW1VNkNVUldWakloVzQvTC9mRjFNR3BpVHhYNjM5cmEwbzhyTU45SUtDUXJMOVhSQ1gzd05KY1FMTi9FVU4zQ2JBTWF5cXdVdnl5cGhLLzRmWGs5eG45SzcyM1dteHVKQmNnUkhyWDErN1lvWkRhMGxzSUtJdDFiRFBSUHRVckV2NG9EOS9iZWlCdzFJdUU4TGNxaGM4WkhSMUVSYVJ5bEh6OVZXK0R0S1Z6NHZIVlVRL1BZSHFuUFQ5UnQ4UFdwTUliaWZ4MTN5Q0dPb1ZKN25mSDNBWVJ3Zm9ZTGQ2NzlXS1oxT0l2UEFBYUtSM0svRkdFOWJiTVAwOVlBMVhwUU9IMC9ocTk1eXFFSjFEaUdPRUFISUkyTkdVcEFyUnNjbGFyQW4rV1ZoWlIxMWZHeG9Qa0oyUStMMytjT3dsMCt2Um1OR0NvcnhTK1A0UnJpSjk2SkJyQmJUVlNpQ2xYM1R5NGQxZGZBUGJCTFBFMEhXTDZPQlU2YkxxQkRvWkFYMytVVENsUVVXNm15WU1ib3k5QmMiLCJtYWMiOiJjMWFiMzFlMDg0ODFlODczZjVjMjlkMGEwNTU1NjdmNmE5MmY4YjkwNDM0MDkzZDIxZTA4MmU5ODg2YjU1NTNiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:39:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inh0S3dNdllROXRvMDFjK2pqaUJncXc9PSIsInZhbHVlIjoiYkxNQ2JHMGUxN1RxQTBxb1htYmhibDlPQ3F6WWh1L1JXTGxSRFhwRlNIbGtmSG5JcHRKRzBPNFBld0hvNVhQMU1BMGNJNGVmcE1saVpDbmk4ZWp3czAyNTd2aEREbk9pam1nK3BsY0xvWCtjS1hOUlF6QndaWnZYYnRrQ3M2U0x0T2VMczZBY0Q1QmZpTFNnOWpXVG02K0hod2hmNEVWQUptZDNtYzBQbGlwS3Y2Um9Va3RlYUVPYWx6WEVHdlRkV0tIUUlQaHByZGdjbTVUL1FsaEIyOGNXVUhRT01YMTJGcEFKSkFyQWVmb0o3MmxLSitYRi9lb1FDZ3VYZjNTeHJMOGVCeG51ekpDUTJIUURia2hGbDlNNjVCRGFieGdML0RoSjFMYzdBdG9IWm1oLy81dm9nd3lEeUtBQjFhaGFEZ2hIcXJYN2FmbUVNd3JhZWp0YlFQcnFOVkcwK1FWRTlvTmJwTnluWjUxdEcrQzRyT0dtbzVrR2p1VmtDU3cwa1MzeDdIcUMrTjRCdG41ajR3cWtaeTJ4em9PcU1jM2QzeEQ0eXJ2Q1F4UXk3aWNHVkFwYTVWWkpQMWt2OC85Vi9Wb0lQa1Q1SXpYMXR0VU1JNndqcnlLU2F2VTRSL3dpM1h4K3Z3REJGRUcySXZ2TktwMjBFZHhrckM4MHNGUWoiLCJtYWMiOiJkNDRmNDUwMzVlNjU2OGFjOGY5NmJmYzhhYmEyOWRiMjFmZWZhYmE2NmNmZjM0MzBlNDdiMDk3MmY3N2E0MWFhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:39:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikc3VS9mL092VUhRc29ZZmVyQlFzMFE9PSIsInZhbHVlIjoibDJvbHNETmZkMG5ZSHNXd3pFdnlPZ3R4Sy9RWmJJTzZIMDN1bVA4WG8wTkQ4czZSYnJudm12VlJrVWZyVWpPMmE1TkNsVGtRendHRWlWMFhaaE9aUGt3dG5lY3hSdTRNaEE4cGM1R2Q4NHc2TW1VNkNVUldWakloVzQvTC9mRjFNR3BpVHhYNjM5cmEwbzhyTU45SUtDUXJMOVhSQ1gzd05KY1FMTi9FVU4zQ2JBTWF5cXdVdnl5cGhLLzRmWGs5eG45SzcyM1dteHVKQmNnUkhyWDErN1lvWkRhMGxzSUtJdDFiRFBSUHRVckV2NG9EOS9iZWlCdzFJdUU4TGNxaGM4WkhSMUVSYVJ5bEh6OVZXK0R0S1Z6NHZIVlVRL1BZSHFuUFQ5UnQ4UFdwTUliaWZ4MTN5Q0dPb1ZKN25mSDNBWVJ3Zm9ZTGQ2NzlXS1oxT0l2UEFBYUtSM0svRkdFOWJiTVAwOVlBMVhwUU9IMC9ocTk1eXFFSjFEaUdPRUFISUkyTkdVcEFyUnNjbGFyQW4rV1ZoWlIxMWZHeG9Qa0oyUStMMytjT3dsMCt2Um1OR0NvcnhTK1A0UnJpSjk2SkJyQmJUVlNpQ2xYM1R5NGQxZGZBUGJCTFBFMEhXTDZPQlU2YkxxQkRvWkFYMytVVENsUVVXNm15WU1ib3k5QmMiLCJtYWMiOiJjMWFiMzFlMDg0ODFlODczZjVjMjlkMGEwNTU1NjdmNmE5MmY4YjkwNDM0MDkzZDIxZTA4MmU5ODg2YjU1NTNiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:39:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138181000\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1010413892 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010413892\", {\"maxDepth\":0})</script>\n"}}