{"__meta": {"id": "Xb3dbd5437c4176568193ae3afc5170a0", "datetime": "2025-07-14 22:30:59", "utime": **********.29884, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532258.841749, "end": **********.298854, "duration": 0.4571051597595215, "duration_str": "457ms", "measures": [{"label": "Booting", "start": 1752532258.841749, "relative_start": 0, "end": **********.219882, "relative_end": **********.219882, "duration": 0.37813305854797363, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.219891, "relative_start": 0.3781421184539795, "end": **********.298856, "relative_end": 1.9073486328125e-06, "duration": 0.0789649486541748, "duration_str": "78.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46017224, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01734, "accumulated_duration_str": "17.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.249127, "duration": 0.01569, "duration_str": "15.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.484}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.273303, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.484, "width_percent": 2.826}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2833118, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.31, "width_percent": 4.441}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.28935, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.751, "width_percent": 2.249}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1387017510 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1387017510\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-197359015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-197359015\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-6609956 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-6609956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-16735807 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532246434%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkR0SitDQ0xKcVhaT1JSeEJ1YmxhSVE9PSIsInZhbHVlIjoiNWx6d1lQOHVLTWNoamhSWFRZYVV1cGJ4dWJHdU5icXJzLys1RGE1Zkl1ZUVEQURZanh1bXZENjlmdzJIdmN3TlFoZmFaMTlTTVhBYzlJWVlTRVJXemVTVTJSM3hFZnB3ZEJZUzBNU05sK21mRXgrNEtRU2ZLSzhzT0FzQ0RtZlRGWEtHeFNVeFNCaTJSZFBhMmxJL2xsWjFkUDR4WW1QZXlUNkovYW1YbWFTRDFQQTg2RzBwa1VJQVVoQkV4ejZudVovZkVsMGhsb3RDaTVrOTBkbW1nbFN2dUY3WmJNeEtGeFRrUWlUSWMyejU3TlhDTnQzS1VsL2JUV2N3M1Q2b0FFNHIwM1hxWWptbFVRS0JGcTVJeVdXT1ROZ05Uck9IYWMrUHp3d2JieEN0Q0dlL1l3S2ZOM25UNDhaWW9qbUdoYWlyakVlcURpdjFPNGo5Nms5VnhPZ0o1eDk1Z1Z4UE8vQy9BdUJiMHc1Z3JaZnRTVTRyV240UnpPc3NDbU1WWnBsdEtwWXBZQU4wZW41eDVWLzBacWw3ay94NTM5TzFnUHIyYk80RlBMNFNDRFJsczhYU2piTThjcDZlblN4RUhETm84U2VWZkhOZkllZVpjZ1JiaDd2MEJldnFDbGxNV280NGxjb2tpVk0yT3ljM0t3SVZYK2k3aHV1dEdubkQiLCJtYWMiOiJhZjEyZmQwZTY3MzVhMWYzZGM3YTI0YmM1MzhkNGEyOGU1NzhlMzZiMDAxNTE2ZTg4ZjNlM2JjYjdlYWM4YjdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVVWFNwb3FEZlFDN2RkazZnZERpWmc9PSIsInZhbHVlIjoiTVk5VHlJNjRvd3hQcDdBYndjMzg2enFUNjlSeWFUOFluT21udEJUUTVJZDk4SnRJcDBmbTRZRmlaQkhpSVZiYW56R29ud2RKb0hVckhybWV4a0plNDlIOHFwQUtmWGY2ZFRtcXNWVUJLMGJLQU0yaVZlMHVWQWU2RU9OcC9rMTFNUUUwSjlkemhKM2lLZGluZ2NUcDYrc2pCejgzOUQ3bzQxS3NORVFtY0NqVkFYVHZmbnZnRTRPMVZZOWhBNnIrd3lVVGs3WkpsbzVEaEViLzZ6OTZlY1o4TVpndG1ZaTVvT0FwVlhKQmNOVWh5Y0VEcVI3L1JnbEt1c1hkZjhucmphUUVvaWV3U3N5Y0RRTzhyRzRpR2ZMMUMvSWlJYnB4Q3dzUGNya29LTVJ5dFNQTHRwc0pUM3hId0JrOG5EYVBwMGtjKzNTZDRJc3RDZFRmSFBQNEU3emZRcDd4MDVkL0RwQk9xbGxkMlZjcWY4enA4cUJHNVB1Wkd4cWpLZXROZ0pZYkxSZlkyeUFnbXRXd05jRW1CemFPaStzdzVlajhPa3N4QUpWNDlwQnVySTlwSVNXTlJNUURwd2RVTWVKYk5zV0k0YjF6U1hRYzN4bjRmM01NS014QjF2WndPYXNUc1JRZTZZck9xb0sxNVdRdHEySWhTZjdTVGhYOXBvREIiLCJtYWMiOiI1ODA0YmJhYTZkMTE0MzEzMjIzNjI2Mzc4Mjg4NjFkMjQ4MGRiNDhmMjJhMmFlNDU3YmFiMDA5MGM0MDM5MDFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-16735807\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1212641010 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212641010\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-182042069 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVZM2p6aFpGam5HMGwvandSRVJQUXc9PSIsInZhbHVlIjoiSlNmNDBxbnRQYjRiRHNoUDVVK0NNeVdRTGR1bGI2ZVdoVDJQSzJrTUYydW1pQlJFb0szK0hYUUxyRmltYUppcmE4OWN1enA2OUU1aHZUOFR1QXhuQTNHZE8zSGZ0c0ZmVksvZ3QzUzc0d2thNVJGSUdnTVBxSGNmMld1M0VCSUFKUmczUnJiQk4xUkJDOXJMbmhVRUhuVW1UY0NveUZBaDNORWpGVVhiU1RCakFUME9tdVBjb3hVYlQ0YklIaEhOMTNvRTVoK2RkOHR5Tk9ndVc5aTMrMWhiVnkyRVdENDNTY2M3RjVqdmpnL1EwZmZoaHlpMXNCM0VZQzA5OEJjUkVGTHllUUxvNUVMa1paTUNnMFJnRngzRzQ3c2FWR0VTd3UzUGttT1BjeXE0ZWg2c0tFTzU5YkdDQ09Md240blR4aDc2MG44Nm5sN2VGUDVGZXBZZzQ4dzBRUnUySmcvV2QvMmptY0lwUDVYMmd5OENnQUMwNUdFZysxNHVzSEdrd3llWEdjeUVJaFphSlhsd1AyTDFha0tTVTcvY0gzTS9mcXRDaW1Ub25Fb2JaL3hiTGZSeHpKNTZhajZTYVdtcHJ0NWhqbEVwU2pRYkFGSHM2UHB0NGZvTm5jbXRrTXNUdXQySWJZRk16bm0wdnNUdll3SE01SVRSRzMyOForbXIiLCJtYWMiOiIyMzE3YzhlNzJiMmM5YjIyZDJjODhiYzkwNzJmMDA4ODA2MzhiN2EyMGE2NjAyMGQ0OTA4ZWU3MjJlMGFkNmU1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpNZTdNcHJSeFZSWHBqOUtJWW5QUFE9PSIsInZhbHVlIjoiUEFiYkpSMDdRV3RJbjFkZHpjbjY2Szhmamd2ZFR3dEI1aElhd1EzRVJpaTExK25hbCtLZ2tqOW9Eb3hLMWs5RzFDY0RwMjhVdkd6OGxpRCs0a3NLN20wQjFXbC81MmhRVzU0bGVLelBTYVlVZVhBZmRvRUlIcURRSHZ3c3d0WGlQbWhLVEYxWDkwd1hKaXRZbkZ1NEJrZk1qYmhQQ3NrZUs5eUtaVHdpWjl0K2tvWHVTY0F1ZkUrMDVuUHpYL2laNnhNanZ1cFhwc0ZYbVZTRzR1UndJRXBLNEtiMkxVSVI1eFhSVVNKQThheE5sL3BkVXBUTDFSN3hWR2NCbEs3V1VxRlBjbzBvRDh4UU5XS2NBY3FPREdqRzBodFMzVjVEUzhOb3NsNFNyMEIvNU1wYnNCOHF3eFc4TzdNZTErUjFySlJRcUVhamVwS3BjM3kyNTMvZGNHZURCa3REWW1nWHBSaG5DNndyREVUK0V2ZFRWSlUzTTNSaldhVW9sNmNZSVpaNDhwck1UNlVEc09EVmZaR3VvQTlNckp6R0ZhTXg1emR5UEhVenJLa2IzaEhxOXE4dlo2cUV2REI0ZEQyamJXdGtoTmNRYUQ0ckYzVnlkU0VuZW5EQmV0bW45cVNwaUx1MGl2ai9rRUg5aTlQUmhKS3pza095Qk8zdjZPQysiLCJtYWMiOiI4YjkwMjc3NTljMTVjOGEyZjU0ZmI5MmI3ZTIzMDI5OGRiZjM4NzU1MTVjNGNlNWQ3Nzc1YTgwNzMwYzNiMjMzIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVZM2p6aFpGam5HMGwvandSRVJQUXc9PSIsInZhbHVlIjoiSlNmNDBxbnRQYjRiRHNoUDVVK0NNeVdRTGR1bGI2ZVdoVDJQSzJrTUYydW1pQlJFb0szK0hYUUxyRmltYUppcmE4OWN1enA2OUU1aHZUOFR1QXhuQTNHZE8zSGZ0c0ZmVksvZ3QzUzc0d2thNVJGSUdnTVBxSGNmMld1M0VCSUFKUmczUnJiQk4xUkJDOXJMbmhVRUhuVW1UY0NveUZBaDNORWpGVVhiU1RCakFUME9tdVBjb3hVYlQ0YklIaEhOMTNvRTVoK2RkOHR5Tk9ndVc5aTMrMWhiVnkyRVdENDNTY2M3RjVqdmpnL1EwZmZoaHlpMXNCM0VZQzA5OEJjUkVGTHllUUxvNUVMa1paTUNnMFJnRngzRzQ3c2FWR0VTd3UzUGttT1BjeXE0ZWg2c0tFTzU5YkdDQ09Md240blR4aDc2MG44Nm5sN2VGUDVGZXBZZzQ4dzBRUnUySmcvV2QvMmptY0lwUDVYMmd5OENnQUMwNUdFZysxNHVzSEdrd3llWEdjeUVJaFphSlhsd1AyTDFha0tTVTcvY0gzTS9mcXRDaW1Ub25Fb2JaL3hiTGZSeHpKNTZhajZTYVdtcHJ0NWhqbEVwU2pRYkFGSHM2UHB0NGZvTm5jbXRrTXNUdXQySWJZRk16bm0wdnNUdll3SE01SVRSRzMyOForbXIiLCJtYWMiOiIyMzE3YzhlNzJiMmM5YjIyZDJjODhiYzkwNzJmMDA4ODA2MzhiN2EyMGE2NjAyMGQ0OTA4ZWU3MjJlMGFkNmU1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpNZTdNcHJSeFZSWHBqOUtJWW5QUFE9PSIsInZhbHVlIjoiUEFiYkpSMDdRV3RJbjFkZHpjbjY2Szhmamd2ZFR3dEI1aElhd1EzRVJpaTExK25hbCtLZ2tqOW9Eb3hLMWs5RzFDY0RwMjhVdkd6OGxpRCs0a3NLN20wQjFXbC81MmhRVzU0bGVLelBTYVlVZVhBZmRvRUlIcURRSHZ3c3d0WGlQbWhLVEYxWDkwd1hKaXRZbkZ1NEJrZk1qYmhQQ3NrZUs5eUtaVHdpWjl0K2tvWHVTY0F1ZkUrMDVuUHpYL2laNnhNanZ1cFhwc0ZYbVZTRzR1UndJRXBLNEtiMkxVSVI1eFhSVVNKQThheE5sL3BkVXBUTDFSN3hWR2NCbEs3V1VxRlBjbzBvRDh4UU5XS2NBY3FPREdqRzBodFMzVjVEUzhOb3NsNFNyMEIvNU1wYnNCOHF3eFc4TzdNZTErUjFySlJRcUVhamVwS3BjM3kyNTMvZGNHZURCa3REWW1nWHBSaG5DNndyREVUK0V2ZFRWSlUzTTNSaldhVW9sNmNZSVpaNDhwck1UNlVEc09EVmZaR3VvQTlNckp6R0ZhTXg1emR5UEhVenJLa2IzaEhxOXE4dlo2cUV2REI0ZEQyamJXdGtoTmNRYUQ0ckYzVnlkU0VuZW5EQmV0bW45cVNwaUx1MGl2ai9rRUg5aTlQUmhKS3pza095Qk8zdjZPQysiLCJtYWMiOiI4YjkwMjc3NTljMTVjOGEyZjU0ZmI5MmI3ZTIzMDI5OGRiZjM4NzU1MTVjNGNlNWQ3Nzc1YTgwNzMwYzNiMjMzIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-182042069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-125005221 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-125005221\", {\"maxDepth\":0})</script>\n"}}