{"__meta": {"id": "X69914ea03525e066f7df423612e03ab3", "datetime": "2025-07-14 22:40:36", "utime": **********.474509, "method": "PATCH", "uri": "/update-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532835.979425, "end": **********.474524, "duration": 0.4950990676879883, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1752532835.979425, "relative_start": 0, "end": **********.385831, "relative_end": **********.385831, "duration": 0.4064061641693115, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.385843, "relative_start": 0.4064180850982666, "end": **********.474526, "relative_end": 1.9073486328125e-06, "duration": 0.08868288993835449, "duration_str": "88.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48505904, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PATCH update-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@updateCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1678\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1678-1741</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.014920000000000001, "accumulated_duration_str": "14.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.420823, "duration": 0.01332, "duration_str": "13.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 89.276}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.444108, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 89.276, "width_percent": 3.686}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.458735, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 92.962, "width_percent": 3.887}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.4610732, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.85, "width_percent": 3.15}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1603212921 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1603212921\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.465399, "xdebug_link": null}]}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => \"12\"\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 192.0\n    \"originalquantity\" => 209\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/update-cart", "status_code": "<pre class=sf-dump id=sf-dump-1609637763 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1609637763\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-34361251 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-34361251\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1605644152 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">43</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik4zT1FFRUh2NDU0bk1GR0Z0dnhVYmc9PSIsInZhbHVlIjoiMGQyaVRiN2NXVmRWVmM1TzgxcXpNeTVyKzFwLzJ4bjJOMkd5YlVWUUJFZlh4bDFWQWRMS1RuaEN4SWVSN2NRRHU5dTh3MENMUjZRa1NIR21IWUVWc1hIVTIwYWM3R0hXaWVack1VL1Z1TTQ2TVhUVTJVUjB6ZmNNMk1NWXFsSTlHZW9tS0VsSytGTFhLYmRkMTBGWCtoMmkvdG1CQXhOTmVuNzlNdUpnQlQveDRhdXIyUWN4YUpUSDJuMU4yZGRHSEloQ3l6KzNMbEMvT21ZbUlwOGgvY3dFRUJTRmZQTi9qWk1rM1ZrWmNSWm1hZ0Z6L2NScSttMUowOE1wOXRESmRERzhwd2pBZUR4TEp4eTRydDdjaFVvekYvbW84NjJPTDFVMlFXNVZaRFFzd0NFSFd4MFpOdGY3N1hpNzNNQkhvYmtHd0pjL3RrUHFnUWluVWN0Zmt6WkRjOWxWT1p5dVVPZ0FyZEh2WmZMOG1lSHFPSTdnKzhVVFd1U0pBWFFCOEJobXhNR2Jpcm13NEp2a1Vwak5vNlBEWkdzNE1IVGZPc25FNzkxR09DQjdpU3VhRFFtYnN6MllCOFFJYW5BQTZka0xyL0pCa2NsUk9UbE9oaHZHQkRqdDh5SUxOUzVSYVplcFo5ZHFSMzFsMXhPSFZER09Lb3ZLaWk4S0sxSEciLCJtYWMiOiI3Nzk0YTA2OTM0MDQ2MzEwNzRiMzFkMjA1YzkxMWYxOTUzNDQzZmQzMTY4NWU1OWVlMTQyZjcwZDAxMjczMmUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJ5SDYrS0ZlREpFTk12YWZJbW5uaFE9PSIsInZhbHVlIjoiOGtOTmJET3hJY0VFdFFCb0hJZ2JpQ3Qwa0x0OEJJekdrVlBOUnRaUGlXWElpWkJTRURhbEpQMDhpdXQ0YkhkMEg0dlhXNjVPaHM3M2pJcTZkYWVOZUkyYVRqVGdjV1JMd1YvUXo4cDJKTUVFckxkakhEbHFnYnRyMGdFZkpTcGZQMUJUcWRtbUFrbHVjSHhKdHpzOXUwSFNnZFpoU1A1bXR6d2wvNXdmNGh5T3piOU5uNXhETGRzNkFoUk53NUZrNTBnYTNMeHpsSnpLYWNHRFdvc0FDYW9YQmFYT0RFWmh3Y0djOE9wcm1RZ0pyVmRhenFXUlEwYWhWSkZZNUZZcGk4T1k1V1crd1dXQXJveTN0cGh0RktvRjUxK3F5UFhyREp1VUxOais3V1VORzY0Z09rSzJUV2Z0SFN1VEt5ZzlnRjMxdkFOVWNsQmhQcWtTKzhzV0pUL3ZUYWNEY0lVVFVVVlp3SHVrbXpwT3MyamJVSG1DTW0zbnc5UUQxblNKSTZ4eHp3TEhiZXl2OXAwWmlFWW1UWUIza0FNSjBSSTZzTm94UXA4K3N2Z0IrNEh5ZkdyMDJFMU1KbGVMVmJCQ1FXd2pXSmU4YlM0SzkxbEZpWE1tcVZmTmFOUDBiZ1lVbGVjQTNTTkRTZXA4cm03TmsyRDluK3NFNEgwUlVPQlgiLCJtYWMiOiJiNDE1NmVmY2VmMzU3YTQwZGM2ODdkMTA5N2IzMjBlOTgwN2UwNzIyOGQ0ZTBhNWJjY2Q5MWQ0NmQxMjlhMmE0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605644152\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1715304635 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715304635\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1175512471 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:40:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkhMdUtKQWpKeVI5UFZ4MW5WSEovakE9PSIsInZhbHVlIjoiZFZGLy82ZThaYmQ1bGJuZFh6L0tlN0lCZ2x1a3FYQWJwSVB0MlEwaWk3NU8yWFdUUExGTjlySmozakFDc1A1UnJ6U1FHM2lqcERkZUdxMHhqMGxDaTFDdHE5YlFIeUZmZWRydmR1bU1oZWp1ZTJSOVZYUXFNbUNLL3RWQkV4Qm9GbndMd2p0bnBzbFUxbTNrQWtNemhJZ0trUit5NVYyRDBTVlF5WmdmVDE5VlVwaXB4UVAzTDFuS3NZbGN4Z3V3WXcrVkZzTnRMdXJFVHJGZWVRTHhSc0FYdkdid2VyZ3ZiT2xiRlFFMCtaQWhlelVHR3AxeGJKa1k3WlJhMTl3cGdOMy9ZTk5HVmp6bjAvT1o3Q21kNVdNUGNsdlZZYlVUZHlJZ1pRclg1MjV4aGovdWtPbjVUNWVTb2Vya0lqQ244d2RXQzA5dUlpTDFqR2tkZDFJQlg2ZEhEYTNRdjd3QnJJbWw3anM0a2VWNDVFR0p6MW1vZy9pWmxFTURWOG80WHZYb04rc2J1M2JKWVROZmt3YVhBWmNwWHh6TmFXdDAvNVJJSm1KeUVuT1hNK3VBZFFaWWx1WjdqWk83UFRveGZwT3JGcU9tZE1SZEdCVCtGdERIazZPZDJZSjdmMVJ3MHdhWVZUMzh2L0pFMG1ETk0wN1gyY0hrb1BuMmRIVDEiLCJtYWMiOiI2MTE1M2FkMWFjODdkMjc5YmUyYzkyNWNhZmQyMTgwNDVmMDdhY2ZiMDYwNjdkMjdiZDJiMmExMmUzYTgyYTUxIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZvVldLMytnR2dYWEkxckNoTUtFMmc9PSIsInZhbHVlIjoiemdiTkxUK3hoY29LVkM2M1dRS25adnpOMnZUaStUY2V2MzYrelQ4MWdsWDEvQjBld0VRTHIxWDFDa2IvNldZMVlNMkFjRmxySE9VaU8wMFZlRElGTE9DRlE0T2xyRFF5eUs3SDUzQkxnaWlydll5RUlRbmZRWmZ6ZmRZdnlJeXh6ZzdjSzc4bUdQSDBveUNxOWFHU0xnQXRyZ1pDQ2ZVWm5STEs1cGtHbkNDcnBiZzlQc0k4RlZqMFJpcmN6dUw5bUw0UEw5dXlRTW1GMkQ2VDdsTDA2ZXR0MkJHODdobWVrL3Y2bE8xR1RmVTRBRXRIcjVzVFBpQ0V5eURwbEkyYnhXTjNIT2hMRHRpRFBCN090TythWnZrMjQ2dTBCamo1YkxQb1BOTGhtRnBLRWt1VzJPWmRPZ1JpaU5XcGE3OC95MGcvcCtsTWIvZWlZUHlPdHI0K25xa0w4RWJTMTVYQjlpOGZxUHNUTGZxN2p6bWplWG9JTm5tMEo4dFRQVUxyU1VwSi9MQ1FlUUVVY0RQWGFsQWFHT0pCRU9vaFZUd1VvNG5lZEZmbk03TEw5Njd1eFl0S1l0UlRCRlYwQVhyZmxET0lGRXpZQS9VdVZqbmxNZU81dUhWNjZVUk9SZWNGSHVKblJZcytKcXBZVy83WVY3aTJNL0pRSUc4emdIbDQiLCJtYWMiOiJmOWY2ZDcxZmVjYjE2NWZkYmQ4NDE4MDYzZDBkZTkyNGMyZTRjODE3MmM1YmZiZTkxZDYyNGI0MTIwZmZmZDE1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:40:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkhMdUtKQWpKeVI5UFZ4MW5WSEovakE9PSIsInZhbHVlIjoiZFZGLy82ZThaYmQ1bGJuZFh6L0tlN0lCZ2x1a3FYQWJwSVB0MlEwaWk3NU8yWFdUUExGTjlySmozakFDc1A1UnJ6U1FHM2lqcERkZUdxMHhqMGxDaTFDdHE5YlFIeUZmZWRydmR1bU1oZWp1ZTJSOVZYUXFNbUNLL3RWQkV4Qm9GbndMd2p0bnBzbFUxbTNrQWtNemhJZ0trUit5NVYyRDBTVlF5WmdmVDE5VlVwaXB4UVAzTDFuS3NZbGN4Z3V3WXcrVkZzTnRMdXJFVHJGZWVRTHhSc0FYdkdid2VyZ3ZiT2xiRlFFMCtaQWhlelVHR3AxeGJKa1k3WlJhMTl3cGdOMy9ZTk5HVmp6bjAvT1o3Q21kNVdNUGNsdlZZYlVUZHlJZ1pRclg1MjV4aGovdWtPbjVUNWVTb2Vya0lqQ244d2RXQzA5dUlpTDFqR2tkZDFJQlg2ZEhEYTNRdjd3QnJJbWw3anM0a2VWNDVFR0p6MW1vZy9pWmxFTURWOG80WHZYb04rc2J1M2JKWVROZmt3YVhBWmNwWHh6TmFXdDAvNVJJSm1KeUVuT1hNK3VBZFFaWWx1WjdqWk83UFRveGZwT3JGcU9tZE1SZEdCVCtGdERIazZPZDJZSjdmMVJ3MHdhWVZUMzh2L0pFMG1ETk0wN1gyY0hrb1BuMmRIVDEiLCJtYWMiOiI2MTE1M2FkMWFjODdkMjc5YmUyYzkyNWNhZmQyMTgwNDVmMDdhY2ZiMDYwNjdkMjdiZDJiMmExMmUzYTgyYTUxIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZvVldLMytnR2dYWEkxckNoTUtFMmc9PSIsInZhbHVlIjoiemdiTkxUK3hoY29LVkM2M1dRS25adnpOMnZUaStUY2V2MzYrelQ4MWdsWDEvQjBld0VRTHIxWDFDa2IvNldZMVlNMkFjRmxySE9VaU8wMFZlRElGTE9DRlE0T2xyRFF5eUs3SDUzQkxnaWlydll5RUlRbmZRWmZ6ZmRZdnlJeXh6ZzdjSzc4bUdQSDBveUNxOWFHU0xnQXRyZ1pDQ2ZVWm5STEs1cGtHbkNDcnBiZzlQc0k4RlZqMFJpcmN6dUw5bUw0UEw5dXlRTW1GMkQ2VDdsTDA2ZXR0MkJHODdobWVrL3Y2bE8xR1RmVTRBRXRIcjVzVFBpQ0V5eURwbEkyYnhXTjNIT2hMRHRpRFBCN090TythWnZrMjQ2dTBCamo1YkxQb1BOTGhtRnBLRWt1VzJPWmRPZ1JpaU5XcGE3OC95MGcvcCtsTWIvZWlZUHlPdHI0K25xa0w4RWJTMTVYQjlpOGZxUHNUTGZxN2p6bWplWG9JTm5tMEo4dFRQVUxyU1VwSi9MQ1FlUUVVY0RQWGFsQWFHT0pCRU9vaFZUd1VvNG5lZEZmbk03TEw5Njd1eFl0S1l0UlRCRlYwQVhyZmxET0lGRXpZQS9VdVZqbmxNZU81dUhWNjZVUk9SZWNGSHVKblJZcytKcXBZVy83WVY3aTJNL0pRSUc4emdIbDQiLCJtYWMiOiJmOWY2ZDcxZmVjYjE2NWZkYmQ4NDE4MDYzZDBkZTkyNGMyZTRjODE3MmM1YmZiZTkxZDYyNGI0MTIwZmZmZDE1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:40:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1175512471\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1652696576 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>192.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>209</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1652696576\", {\"maxDepth\":0})</script>\n"}}