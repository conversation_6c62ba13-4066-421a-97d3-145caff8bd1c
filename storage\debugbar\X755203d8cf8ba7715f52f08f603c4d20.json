{"__meta": {"id": "X755203d8cf8ba7715f52f08f603c4d20", "datetime": "2025-07-14 22:38:51", "utime": **********.87388, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.478044, "end": **********.873893, "duration": 0.39584898948669434, "duration_str": "396ms", "measures": [{"label": "Booting", "start": **********.478044, "relative_start": 0, "end": **********.830205, "relative_end": **********.830205, "duration": 0.3521609306335449, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.830216, "relative_start": 0.3521718978881836, "end": **********.873895, "relative_end": 1.9073486328125e-06, "duration": 0.043678998947143555, "duration_str": "43.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 44850512, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00231, "accumulated_duration_str": "2.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.861799, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 81.818}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.8668149, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "pos": "array:1 [\n  6 => array:9 [\n    \"name\" => \"بيتي كروكر خليط كيك ابيض 500 جرام\"\n    \"quantity\" => 2\n    \"price\" => \"16.00\"\n    \"id\" => \"6\"\n    \"tax\" => 0\n    \"subtotal\" => 32.0\n    \"originalquantity\" => 211\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1025080010 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1025080010\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">ar,en-US;q=0.9,en;q=0.8,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1944 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=vff8k8%7C2%7Cfxl%7C0%7C2019; _clsk=sxq5xm%7C1752532102369%7C1%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJoMlIrM05RYUo1dGhuai83bkJ5eVE9PSIsInZhbHVlIjoicytDeDlPcUxDMzNiak9xUUVHdWtBc1FiNmlTV0F3QitNWDUxckt3dUVmSVpHWFNPL3kvYXYyejdXeEUwRytzeVJLOWVTc01sWVJlNUFORC9SZi9rZVJxSVJFRUxsaDBpNG9GaDVqbE9tM1JqZnFzK3FhQVdQWXB3RWRKWTFHdTlZRnN1cElnNnJQYXp2bzFaWHJ0MEVCT3ZoVXhhc0E0WDBRVjQrbW9EbXRRSTRNM1k3MjBOODBTMU9pd3hKNEFOdms2QksvaGtUWDJVUEt6RGpIbGU1SmFWMGxEN0ROUEVadjExbHdKYWxOTEp2dVV4UzJYWFQvZ05aZ2NobUhxNkpXUEJBZlpZRlhaODc1VzdQZmRtaUI3ZStvWlFqcXh3dk5jcjZEaTRPM0dFVjhueWk2Z2x5S25aeWhRMm03WVh3andsQnZGNTZHOCtDT2IrNjlGUUQ3bG1VRlN3Q0did0RJNW5nZmJyKzRDVWZJb0dDcUJjaWtiTk5CTWlIWS80QWxva0JwblR3TE43WlZldkpGRnFGVEhJVFpjK3lxSkN3RndLQ1k4K1B2MDY4N1JOYXhHQmg5bmpjckViSEEwNmw4UURKVDllWGMwS2N3S3F0UHpOdEJmVW1neUJVZS92V0dMSVR1eW84d3NLdlNnMDNiOVNyeWY5OUIxNXlhdW0iLCJtYWMiOiIwZWU2MGQyODM1YzljMTBiNGY4YmZmNWZhOGY0MzBhOGYxODJjMWMyMmQ3NDc3Mjc0Y2JkZjU5MDU3NDI5YWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImFBRnM2dndyd2dzKzVBZk8rZlQvTkE9PSIsInZhbHVlIjoiTjE3MHhKL0NJR2xxVlpFbTY1VXcvQWVTWEdUelJ6Y0J3bWhhZ1FaTklBMjNNMmV6NXZFU1VnUkVBWlVtM0lDVnI4Ny9hNjlIdmdxN3I1WERuK0pJVnQ3eWVRYWFqVk5TTjJsbTQ0VDRXNVhXd01ZWDRPcFAwQXNiV216U2xSVmdWVUVQdFo0a1F3dTluZ1ZDSjJnWm1TMjROTHZiNStZNGV4Z0lHYkVvSXU5bW1QNjBtQkE0OVVGd3lIa3lBSmFQcWg2R1ZGZ1MzSWRzMlIrVWk5Zjg1aHkxUDFkVnpwQ0Ezd2dIQWpvU2dWa2xXVEZWdkZpand5WFBVVFN5cHFxUmdQejdiK0ROZkF0d05IektlYndHZmxLVE82L0VrbmxnNWtGTlg1UUpDdHdxa0pNLzRQUDlWQ29TSGpnVEFDZCtGYVNmQ3ZYSHNWMjBQZDZDOFVOVE5YMWtKNmdKdGlFdENYTkJSMnZBbVlKTVg0dm1DQnBHQ3F6ei9mNmtYSEx5N2QzbUxJdFk3bjYzZ3Q2Wm9haWYyekUzdytWeUNBL0h6Um52Y2JQSXR3ZGtrM2hoREZsL1Zmdm03alk5ZTRIdE5hbGIxMFZyREJqR1JPWTA4TTFkamRveXFCSXNlaDhDNlJad0dMQTUwNTlWSXpFZWNNemwvNHFERTNSUDZ4enIiLCJtYWMiOiI3MWVhZjkwZjliNTkwZTU4ZTdkYTZlYTVkMWU2NjgwYzMwOWM5ZmY0MzA1Y2NjYjZjYThlNzYyYTA3MmQ5MzU0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1713330716 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">rEr0yMmk3wsDmIORzoN9ooTD3k5LdpyWeBUpYwkm</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713330716\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1375438887 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:38:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikg4QVZrMXRWQTBWL0NFR3JBRHozWGc9PSIsInZhbHVlIjoiSENSNWpQUjA0Qkw4VVpwZ1BhTTNVQjVlSGhFVzFtZlFGeC8xSk1DQlFXVFo4dDVTR3RqSlkza3o0bTJUaHYzUEFDMW8rOUZFRGw5eU9PU3doMDMyZVpRK3c5bXViZmFyOGUxbzk2MlFiVWVROFIwOXN4WG82Rk5lUnltc0t2T3U2N0dkTUZJQlhFblNqeEozQzB6cjMvQ0FrNW5GaW9POWVjd1l1ekpnV1hlRXpiNkJNVllxUkYwaDVIci8wbnRDTlVHSGdUMFhINjIzODRuZE1rOU9UVE5ZMjNrSmRDK1A5QzQzbE1oVjVFUHo1NVJCVVk0Y0h0ejAxN0xyTmkyeGV2Y1hDekZ4UXZtamdwMUViMGkzVG4vc1h2VWRhNG5rNHRJeWFyWnltUGpSVlR5UXFJSVh4N2xyQ3g2OFhISE1DQXVFUE5JOGQ4bjVpRTZ4M1BYV3BGbnZsai9XYzVCR252ZENaeXVaZ25QZ2VRcTVjdENYa2tqNnZYZmREVFRpNnJLcDVRRHQ1WDFqbytVbWYybW5YbGlsUXduYTRCNVNKUW0yWGlxbm4yYzFCVVU4WjlWZHYxd0tYWnhrc2w2L2hsUHRmbGxSano4TEYweEVaaHBkZVdHTnpKWmx4ODgrSzNDZldpM3NJNVYvZnFPUVJId1V1QmNEb2xTTFFlSzEiLCJtYWMiOiIxMTMwNDhjN2YxMTVmZWViY2U4ODc0MDFjMTA4NjgwODhkMjRkMzM1NTk4NDBhNzJhZjNlMTU4ZDk3ODgyOTc1IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRjZUE5bTlvTjB5dUJRRnBQOUZaUnc9PSIsInZhbHVlIjoiVHJZd2pBY0QrSEh0b0VkVWF1ZWpxN3dNSXZrUUFtSEV6aU9YWGZRSUpBNXVZeVJlb0N2NWRhUjArWUdjanVwUnltakJ5cWxzVTdsQVNzWFNtVjhhaEIvSFc1eGp3SGtIZ1dPRkFyU2JqL25uRy95ajJvcFlDL0hpWGpnNHpMczVNdEwyOFhad1R1Zlpjc29ZMUsram1oUy9GRXU2NnIyYmx5Sk8xVE92V2JJeWlDTEhLWG92aGRkQldmQ0pJMGVtOTFZZElXMXBZT3dnZUp2V1Q3cHMxTjlTaUUrUXl0dkhiUlZUU0pvM2R1aWlUODF5MDJ5TzAweXhGUHFsK05RSnpTUTV5Z1E3eDdDY3RSU04renFyZEhmZlZCOGtqc3VpTkpPdTlycUZlQjN4Y3hvRU4rOW0vam5CN2FUZ3RNZDh4WkNCbUo3UGd6TnAxWmZhR3l3UXdqVnViaUlGdXY2MHRQbFlLS1ZhYU9GUlhxdmFndGQ0M1hxc0NsVWd4Q001U0pLTnFiSUE0ZGJMWDFUTUxuZXRReW5uS1krNmZ3c2xVRHBGMEJIVzZZZGdFd2xFNUM4eU4zTUw4Zml5KzJnOHplbDVyNWxTeWZPMnVteUR3RVlVSlk0VDNDaGdaMSs0amF2WUw0OUlMTDNiTi9DanlzQ3JJNEc2dmlaMTlEZEwiLCJtYWMiOiIyYTU2NGJiNzY4ODM2ZjNkMTgzZmFjZWJlYTQyNGM5YTg2Y2NiOTdkMDM2ZWE1N2Y0YzQxNzMwZGY4ODc5NzRiIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:38:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikg4QVZrMXRWQTBWL0NFR3JBRHozWGc9PSIsInZhbHVlIjoiSENSNWpQUjA0Qkw4VVpwZ1BhTTNVQjVlSGhFVzFtZlFGeC8xSk1DQlFXVFo4dDVTR3RqSlkza3o0bTJUaHYzUEFDMW8rOUZFRGw5eU9PU3doMDMyZVpRK3c5bXViZmFyOGUxbzk2MlFiVWVROFIwOXN4WG82Rk5lUnltc0t2T3U2N0dkTUZJQlhFblNqeEozQzB6cjMvQ0FrNW5GaW9POWVjd1l1ekpnV1hlRXpiNkJNVllxUkYwaDVIci8wbnRDTlVHSGdUMFhINjIzODRuZE1rOU9UVE5ZMjNrSmRDK1A5QzQzbE1oVjVFUHo1NVJCVVk0Y0h0ejAxN0xyTmkyeGV2Y1hDekZ4UXZtamdwMUViMGkzVG4vc1h2VWRhNG5rNHRJeWFyWnltUGpSVlR5UXFJSVh4N2xyQ3g2OFhISE1DQXVFUE5JOGQ4bjVpRTZ4M1BYV3BGbnZsai9XYzVCR252ZENaeXVaZ25QZ2VRcTVjdENYa2tqNnZYZmREVFRpNnJLcDVRRHQ1WDFqbytVbWYybW5YbGlsUXduYTRCNVNKUW0yWGlxbm4yYzFCVVU4WjlWZHYxd0tYWnhrc2w2L2hsUHRmbGxSano4TEYweEVaaHBkZVdHTnpKWmx4ODgrSzNDZldpM3NJNVYvZnFPUVJId1V1QmNEb2xTTFFlSzEiLCJtYWMiOiIxMTMwNDhjN2YxMTVmZWViY2U4ODc0MDFjMTA4NjgwODhkMjRkMzM1NTk4NDBhNzJhZjNlMTU4ZDk3ODgyOTc1IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRjZUE5bTlvTjB5dUJRRnBQOUZaUnc9PSIsInZhbHVlIjoiVHJZd2pBY0QrSEh0b0VkVWF1ZWpxN3dNSXZrUUFtSEV6aU9YWGZRSUpBNXVZeVJlb0N2NWRhUjArWUdjanVwUnltakJ5cWxzVTdsQVNzWFNtVjhhaEIvSFc1eGp3SGtIZ1dPRkFyU2JqL25uRy95ajJvcFlDL0hpWGpnNHpMczVNdEwyOFhad1R1Zlpjc29ZMUsram1oUy9GRXU2NnIyYmx5Sk8xVE92V2JJeWlDTEhLWG92aGRkQldmQ0pJMGVtOTFZZElXMXBZT3dnZUp2V1Q3cHMxTjlTaUUrUXl0dkhiUlZUU0pvM2R1aWlUODF5MDJ5TzAweXhGUHFsK05RSnpTUTV5Z1E3eDdDY3RSU04renFyZEhmZlZCOGtqc3VpTkpPdTlycUZlQjN4Y3hvRU4rOW0vam5CN2FUZ3RNZDh4WkNCbUo3UGd6TnAxWmZhR3l3UXdqVnViaUlGdXY2MHRQbFlLS1ZhYU9GUlhxdmFndGQ0M1hxc0NsVWd4Q001U0pLTnFiSUE0ZGJMWDFUTUxuZXRReW5uS1krNmZ3c2xVRHBGMEJIVzZZZGdFd2xFNUM4eU4zTUw4Zml5KzJnOHplbDVyNWxTeWZPMnVteUR3RVlVSlk0VDNDaGdaMSs0amF2WUw0OUlMTDNiTi9DanlzQ3JJNEc2dmlaMTlEZEwiLCJtYWMiOiIyYTU2NGJiNzY4ODM2ZjNkMTgzZmFjZWJlYTQyNGM5YTg2Y2NiOTdkMDM2ZWE1N2Y0YzQxNzMwZGY4ODc5NzRiIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:38:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375438887\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">87x6CdBPqkmQI4JBiwVlR6M6T7LL0yWNSNa2B2h9</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>6</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">&#1576;&#1610;&#1578;&#1610; &#1603;&#1585;&#1608;&#1603;&#1585; &#1582;&#1604;&#1610;&#1591; &#1603;&#1610;&#1603; &#1575;&#1576;&#1610;&#1590; 500 &#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">16.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>32.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>211</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}