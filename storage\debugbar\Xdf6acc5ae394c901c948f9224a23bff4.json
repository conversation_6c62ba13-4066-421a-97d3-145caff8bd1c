{"__meta": {"id": "Xdf6acc5ae394c901c948f9224a23bff4", "datetime": "2025-07-14 22:30:59", "utime": **********.298944, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752532258.841749, "end": **********.298956, "duration": 0.45720696449279785, "duration_str": "457ms", "measures": [{"label": "Booting", "start": 1752532258.841749, "relative_start": 0, "end": **********.220569, "relative_end": **********.220569, "duration": 0.37881994247436523, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220577, "relative_start": 0.3788280487060547, "end": **********.298958, "relative_end": 2.1457672119140625e-06, "duration": 0.07838106155395508, "duration_str": "78.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46035008, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.01632, "accumulated_duration_str": "16.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.250122, "duration": 0.01472, "duration_str": "14.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.196}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.273547, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.196, "width_percent": 3.615}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.283623, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 93.811, "width_percent": 4.289}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\تشليح ملفات\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.289616, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.1, "width_percent": 1.9}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2F%D8%AA%D8%B4%D9%84%D9%8A%D8%AD%20%D9%85%D9%84%D9%81%D8%A7%D8%AA%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-767978833 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-767978833\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2097404830 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2097404830\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1945086763 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945086763\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-83636776 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2002 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clck=10dm9x5%7C2%7Cfxl%7C0%7C2019; __stripe_mid=e192329e-0f74-45b0-93fe-5d637aa33ac46001c0; _clsk=izrips%7C1752532246434%7C2%7C1%7Cl.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkR0SitDQ0xKcVhaT1JSeEJ1YmxhSVE9PSIsInZhbHVlIjoiNWx6d1lQOHVLTWNoamhSWFRZYVV1cGJ4dWJHdU5icXJzLys1RGE1Zkl1ZUVEQURZanh1bXZENjlmdzJIdmN3TlFoZmFaMTlTTVhBYzlJWVlTRVJXemVTVTJSM3hFZnB3ZEJZUzBNU05sK21mRXgrNEtRU2ZLSzhzT0FzQ0RtZlRGWEtHeFNVeFNCaTJSZFBhMmxJL2xsWjFkUDR4WW1QZXlUNkovYW1YbWFTRDFQQTg2RzBwa1VJQVVoQkV4ejZudVovZkVsMGhsb3RDaTVrOTBkbW1nbFN2dUY3WmJNeEtGeFRrUWlUSWMyejU3TlhDTnQzS1VsL2JUV2N3M1Q2b0FFNHIwM1hxWWptbFVRS0JGcTVJeVdXT1ROZ05Uck9IYWMrUHp3d2JieEN0Q0dlL1l3S2ZOM25UNDhaWW9qbUdoYWlyakVlcURpdjFPNGo5Nms5VnhPZ0o1eDk1Z1Z4UE8vQy9BdUJiMHc1Z3JaZnRTVTRyV240UnpPc3NDbU1WWnBsdEtwWXBZQU4wZW41eDVWLzBacWw3ay94NTM5TzFnUHIyYk80RlBMNFNDRFJsczhYU2piTThjcDZlblN4RUhETm84U2VWZkhOZkllZVpjZ1JiaDd2MEJldnFDbGxNV280NGxjb2tpVk0yT3ljM0t3SVZYK2k3aHV1dEdubkQiLCJtYWMiOiJhZjEyZmQwZTY3MzVhMWYzZGM3YTI0YmM1MzhkNGEyOGU1NzhlMzZiMDAxNTE2ZTg4ZjNlM2JjYjdlYWM4YjdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVVWFNwb3FEZlFDN2RkazZnZERpWmc9PSIsInZhbHVlIjoiTVk5VHlJNjRvd3hQcDdBYndjMzg2enFUNjlSeWFUOFluT21udEJUUTVJZDk4SnRJcDBmbTRZRmlaQkhpSVZiYW56R29ud2RKb0hVckhybWV4a0plNDlIOHFwQUtmWGY2ZFRtcXNWVUJLMGJLQU0yaVZlMHVWQWU2RU9OcC9rMTFNUUUwSjlkemhKM2lLZGluZ2NUcDYrc2pCejgzOUQ3bzQxS3NORVFtY0NqVkFYVHZmbnZnRTRPMVZZOWhBNnIrd3lVVGs3WkpsbzVEaEViLzZ6OTZlY1o4TVpndG1ZaTVvT0FwVlhKQmNOVWh5Y0VEcVI3L1JnbEt1c1hkZjhucmphUUVvaWV3U3N5Y0RRTzhyRzRpR2ZMMUMvSWlJYnB4Q3dzUGNya29LTVJ5dFNQTHRwc0pUM3hId0JrOG5EYVBwMGtjKzNTZDRJc3RDZFRmSFBQNEU3emZRcDd4MDVkL0RwQk9xbGxkMlZjcWY4enA4cUJHNVB1Wkd4cWpLZXROZ0pZYkxSZlkyeUFnbXRXd05jRW1CemFPaStzdzVlajhPa3N4QUpWNDlwQnVySTlwSVNXTlJNUURwd2RVTWVKYk5zV0k0YjF6U1hRYzN4bjRmM01NS014QjF2WndPYXNUc1JRZTZZck9xb0sxNVdRdHEySWhTZjdTVGhYOXBvREIiLCJtYWMiOiI1ODA0YmJhYTZkMTE0MzEzMjIzNjI2Mzc4Mjg4NjFkMjQ4MGRiNDhmMjJhMmFlNDU3YmFiMDA5MGM0MDM5MDFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-83636776\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1490748511 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ehqd95YhC8MZV6aowmswJEMCcQxwEsLfl46OwOVd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490748511\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1004885905 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 22:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJpOGYxQk5xZ1V1OG1JdHNZY20zZ2c9PSIsInZhbHVlIjoieDgvQTBUSEFwLzhDdUJXTnpLL09pMmU4QThTZ0x1a0VIU1lXYTM1d3dsZWxScDdXTlNmZVRhZUhROVR6YjVhZmJFMnNyWUIycnEzU0hWbktmVzBvdXY3NG5KOUJtZXp1N3NKeEtOS3c1TWhCdUUxamIya1BHUjQ5eHF1K084YzZqZGJaUFRWbWdOMnduOWpxSGNRaHR0RVNsU0FUUkJPZjdvUlNVY1VhVUY0djQ1V0ZEWVBibFVYVkZJRGhNaHNZQTBkVEJQaTNLdTArSStiMENkNmpxaE5naDFQZmJETmFxMCtyNlF4QlJYdnY4anc5amNGdUU3TjZ2dEdESjNCL3paK3VPbVEvZ3pzL2ZmVWVCQ0dNUi9PUVJOdEVBQmdua2FNS1NwMU9yUlA2RkxqbVAzZE1mdlZYWWVRMlpJMER0UGYxUTlZc2hlTVBPK0ZNWE1LMjZEaXhzeEZGSlQwaVU1TXVBcXZMdHFGTHhKdjI5Yzc4Rk05UnhEcnIwQXNKdmU5RnNXWjlmclRudGp0Qjk5Zk02SWNwRG0wR2VXamY5MXFsWkNzWkNsdXNObXBkalc1TDJBTHNQU2FqaUkwRSs5bG1CbjBGSmdQK1pPZFVZOUcyT1NlOVpIYjAxa3l6ODVzaXBOME9JeWQ5dHlxV1dJQkNMUG91N2c1b2d1THAiLCJtYWMiOiIwYjJmNWY0Yzc1MjAzZGZlZmEyNDM5N2U1Mjc1NWYwMDgzYWU1ODJlZWZmMjI1OWVjMWViY2Q5ZGExMGQ4MTlhIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBwRW5HQndVSm01d3ZDTDg0MitlelE9PSIsInZhbHVlIjoid1RMVWFSM21hd3VVdFBkeHQ4RFJKVEJMbVZGNW5yOCtaZzQ5RDJ6cFlkcytDbWVjaVNhd2k3cjB5TUlQd1BZSktNVjA5WXRMQlJ4MXdzL1BFdGZDOU9rbmk3TFd4MnljUjRqQ3NkamMvY2ZYVnVzTUlmMGxCNzBUOFRUY1M5WFdHWm9XaVEySnN1YzFtYThYa0REMmtHS1Q2ZWk3MjJrOTNqNFhxMS8yaDBRV0prL1BFRlgyUTdCSVE5SGY0TTN6YmZSRmxMVzRoK1FzK25iTC9yTHBEK1FQNVhNdnRma0Q0MzBUR2I1OGExYVc4c2ZxYVVMbk9Lc0ZMRndLU2VWenYxanRBVlZPaWNSajR4STNudi9UK0FGcjRhTGFBUDFsQkpMQVNmVGptbUJpYWdKbVFoVzVuVm53cGlOc1lHQ2VSYUZ4NFNrN0hlbnkrVUR1UlUwYXNOenVadFljaDhaSTlmaUxsWmNTdjkySy93dXRlTUc0MTc2Z2p4OGlhV1RMbkxZZkxscU92VkFxR2FQdE0zOGN5UVhkUDErT2huUVFSVW5aVVRlRkJEMkM5dXNsUFpFR0wyNE9yQ3l3NVdub1pDUEdBQVU1bGNzQ3JNT1ZSUzhJaEc2Qm5uOEpFNFFzZ1ZQK2tTbGpGeDEwcHBGSHVETVhuODgwbjhkcXRlckUiLCJtYWMiOiJiNTQzZjE5ODBkNGRjNDIzOWUzYmFkYWMwYzBmZTNlNzZiM2MyMzEzYTYwZjYzMjYxYTNiZTczMzU0YjMzMjM2IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 00:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJpOGYxQk5xZ1V1OG1JdHNZY20zZ2c9PSIsInZhbHVlIjoieDgvQTBUSEFwLzhDdUJXTnpLL09pMmU4QThTZ0x1a0VIU1lXYTM1d3dsZWxScDdXTlNmZVRhZUhROVR6YjVhZmJFMnNyWUIycnEzU0hWbktmVzBvdXY3NG5KOUJtZXp1N3NKeEtOS3c1TWhCdUUxamIya1BHUjQ5eHF1K084YzZqZGJaUFRWbWdOMnduOWpxSGNRaHR0RVNsU0FUUkJPZjdvUlNVY1VhVUY0djQ1V0ZEWVBibFVYVkZJRGhNaHNZQTBkVEJQaTNLdTArSStiMENkNmpxaE5naDFQZmJETmFxMCtyNlF4QlJYdnY4anc5amNGdUU3TjZ2dEdESjNCL3paK3VPbVEvZ3pzL2ZmVWVCQ0dNUi9PUVJOdEVBQmdua2FNS1NwMU9yUlA2RkxqbVAzZE1mdlZYWWVRMlpJMER0UGYxUTlZc2hlTVBPK0ZNWE1LMjZEaXhzeEZGSlQwaVU1TXVBcXZMdHFGTHhKdjI5Yzc4Rk05UnhEcnIwQXNKdmU5RnNXWjlmclRudGp0Qjk5Zk02SWNwRG0wR2VXamY5MXFsWkNzWkNsdXNObXBkalc1TDJBTHNQU2FqaUkwRSs5bG1CbjBGSmdQK1pPZFVZOUcyT1NlOVpIYjAxa3l6ODVzaXBOME9JeWQ5dHlxV1dJQkNMUG91N2c1b2d1THAiLCJtYWMiOiIwYjJmNWY0Yzc1MjAzZGZlZmEyNDM5N2U1Mjc1NWYwMDgzYWU1ODJlZWZmMjI1OWVjMWViY2Q5ZGExMGQ4MTlhIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBwRW5HQndVSm01d3ZDTDg0MitlelE9PSIsInZhbHVlIjoid1RMVWFSM21hd3VVdFBkeHQ4RFJKVEJMbVZGNW5yOCtaZzQ5RDJ6cFlkcytDbWVjaVNhd2k3cjB5TUlQd1BZSktNVjA5WXRMQlJ4MXdzL1BFdGZDOU9rbmk3TFd4MnljUjRqQ3NkamMvY2ZYVnVzTUlmMGxCNzBUOFRUY1M5WFdHWm9XaVEySnN1YzFtYThYa0REMmtHS1Q2ZWk3MjJrOTNqNFhxMS8yaDBRV0prL1BFRlgyUTdCSVE5SGY0TTN6YmZSRmxMVzRoK1FzK25iTC9yTHBEK1FQNVhNdnRma0Q0MzBUR2I1OGExYVc4c2ZxYVVMbk9Lc0ZMRndLU2VWenYxanRBVlZPaWNSajR4STNudi9UK0FGcjRhTGFBUDFsQkpMQVNmVGptbUJpYWdKbVFoVzVuVm53cGlOc1lHQ2VSYUZ4NFNrN0hlbnkrVUR1UlUwYXNOenVadFljaDhaSTlmaUxsWmNTdjkySy93dXRlTUc0MTc2Z2p4OGlhV1RMbkxZZkxscU92VkFxR2FQdE0zOGN5UVhkUDErT2huUVFSVW5aVVRlRkJEMkM5dXNsUFpFR0wyNE9yQ3l3NVdub1pDUEdBQVU1bGNzQ3JNT1ZSUzhJaEc2Qm5uOEpFNFFzZ1ZQK2tTbGpGeDEwcHBGSHVETVhuODgwbjhkcXRlckUiLCJtYWMiOiJiNTQzZjE5ODBkNGRjNDIzOWUzYmFkYWMwYzBmZTNlNzZiM2MyMzEzYTYwZjYzMjYxYTNiZTczMzU0YjMzMjM2IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 00:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004885905\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-393677991 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zoPazlcEAyGUSkEQo0JBXx6iNECJVb4qF2C7Ttgn</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-393677991\", {\"maxDepth\":0})</script>\n"}}